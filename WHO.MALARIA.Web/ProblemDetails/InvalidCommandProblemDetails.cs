﻿using System;
using Microsoft.AspNetCore.Http;
using Serilog;
using WHO.MALARIA.Domain.Exceptions;

namespace WHO.MALARIA.Web.ProblemDetails
{
    public class InvalidCommandProblemDetails : Microsoft.AspNetCore.Mvc.ProblemDetails
    {
        public InvalidCommandProblemDetails(InvalidCommandException exception)
        {
            this.Title = exception.Message;
            this.Status = StatusCodes.Status400BadRequest;
            this.Detail = exception.Details;
            this.Type = "https://somedomain/validation-error";

            Log.Information(exception, "Logging with StatusCode, Message{@result}", exception);
        }
    }
}
