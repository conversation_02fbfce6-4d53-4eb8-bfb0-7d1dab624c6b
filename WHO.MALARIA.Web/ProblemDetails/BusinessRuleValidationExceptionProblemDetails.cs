﻿using System;
using Microsoft.AspNetCore.Http;
using Serilog;
using WHO.MALARIA.Domain.Exceptions;

namespace WHO.MALARIA.Web.ProblemDetails
{
    public class BusinessRuleValidationExceptionProblemDetails: Microsoft.AspNetCore.Mvc.ProblemDetails
    {
        public BusinessRuleValidationExceptionProblemDetails(BusinessRuleValidationException exception)
        {
            this.Title = "Business rule validation error";
            this.Status = StatusCodes.Status409Conflict;
            this.Detail = exception.Details;
            this.Type = "https://somedomain/business-rule-validation-error";

            Log.Information(exception, "Logging with StatusCode, Message{@result}", exception);
        }
    }
}
