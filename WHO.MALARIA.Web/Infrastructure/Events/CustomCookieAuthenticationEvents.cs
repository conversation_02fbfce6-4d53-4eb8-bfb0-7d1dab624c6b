﻿using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using WHO.MALARIA.Services.Infrastructure;

namespace WHO.MALARIA.Web.Infrastructure.Events
{
    /// <summary>
    /// Checks if token renewal is required or not and refresh token is expired or not. If refresh token is expired issues a challenge
    /// to perform silent user reauthentication , If token renewal is successful context is updated with latest token information
    /// </summary>
    public class CustomCookieAuthenticationEvents : CookieAuthenticationEvents
    {
        private readonly ITokenRefresherService _tokenRefresher;

        public CustomCookieAuthenticationEvents(ITokenRefresherService tokenRefresher)
        {
            _tokenRefresher = tokenRefresher;
        }

        /// <summary>
        /// Called each time a request principal has been validated by the middleware. 
        /// Used to check if the refresh token is required or no, Issues challenge is refresh token fails/expires to silently reauthenticate current logged in user
        /// </summary>
        /// <param name="context"> Context parameter which contains information about the login session as well as the user ClaimsIdentity. </param>
        public override async Task ValidatePrincipal(CookieValidatePrincipalContext context)
        {
            //Check if token refresh is required
            var result = await _tokenRefresher.TryRefreshTokenIfRequiredAsync(
                context.Properties.GetTokenValue("refresh_token"),
                context.Properties.GetTokenValue("expires_at"),
                CancellationToken.None);

            //Issue challenge and silently reauthenticate the user if attempt tot refresh token fails or refresh token expires
            if (!result.IsSuccessResult)
            {
                await context.HttpContext.ChallengeAsync();
                return;
            }
            //If token renewal is successful update the context with updated refresh token, access token and expiration time of access token
            else if (result.TokensRenewed)
            {
                context.Properties.UpdateTokenValue("access_token", result.AccessToken);
                context.Properties.UpdateTokenValue("refresh_token", result.RefreshToken);
                context.Properties.UpdateTokenValue("expires_at", result.ExpiresAt);
                context.ShouldRenew = true;
            }
        }
    }
}
