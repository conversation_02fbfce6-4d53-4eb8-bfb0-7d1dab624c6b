{"DQA": {"ServiceLevel": {"SummaryWorksheet": {"SheetNumber": 3, "DateCell": "D1", "ReasonObservedQualityCell": "B26"}, "HMISWorksheet": {"SheetNumber": 4, "DateCell": "H1", "YearMonthRowHeader": {"FirstCellRow": 7, "FirstCellColumn": 3, "LastCellColumn": 14, "LastCellRow": 19}, "ProtectedYearMonthRow": {"FirstCellRow": 9, "FirstCellColumn": 3, "LastCellColumn": 3, "LastCellRow": 19}, "HMISRowHeader": {"FirstCellRow": 8, "FirstCellColumn": 3, "LastCellColumn": 14, "LastCellRow": 19}, "CoreVariable": {"FirstCellRow": 9, "FirstCellColumn": 3, "LastCellRow": 9, "LastCellColumn": 3}}, "SourceWorksheet": {"SheetNumber": 5, "DateCell": "H1", "YearMonthRowHeader": {"FirstCellRow": 7, "FirstCellColumn": 3, "LastCellColumn": 14, "LastCellRow": 19}, "SourceRowHeader": {"FirstCellRow": 8, "FirstCellColumn": 3, "LastCellRow": 19, "LastCellColumn": 14}, "ProtectedSourceRow": {"FirstCellRow": 7, "FirstCellColumn": 3, "LastCellRow": 19, "LastCellColumn": 14}, "CoreVariable": {"FirstCellRow": 9, "FirstCellColumn": 3, "LastCellRow": 9, "LastCellColumn": 3}}, "CompletenessCoreWorksheet": {"SheetNumber": 6, "DateCell": "R1", "CompletenessCoreRowHeader": {"FirstCellRow": 5, "FirstCellColumn": 3, "LastCellRow": 22, "LastCellColumn": 38}, "ProtectedCompletenessCoreRow": {"FirstCellRow": 5, "FirstCellColumn": 3, "LastCellRow": 22, "LastCellColumn": 5}, "ProtectedOutPatientRegisterRow": {"FirstCellRow": 9, "FirstCellColumn": 3, "LastCellRow": 12, "LastCellColumn": 5}, "ProtectedInPatientRegisterRow": {"FirstCellRow": 14, "FirstCellColumn": 3, "LastCellRow": 17, "LastCellColumn": 5}, "ProtectedLabRegisterRow": {"FirstCellRow": 19, "FirstCellColumn": 3, "LastCellRow": 22, "LastCellColumn": 5}, "ProtectedFormulaRow": {"FirstCellRow": 9, "FirstCellColumn": 5, "LastCellRow": 22, "LastCellColumn": 5}, "ProtectedTotalCompletenessRow": {"FirstCellRow": 9, "FirstCellColumn": 39, "LastCellRow": 22, "LastCellColumn": 39}}, "ConcordanceWorksheet": {"SheetNumber": 7, "DateCell": "M1", "ConcordanceRowHeader": {"FirstCellRow": 5, "FirstCellColumn": 3, "LastCellRow": 26, "LastCellColumn": 14}, "ProtectedConcordanceRow": {"FirstCellRow": 5, "FirstCellColumn": 3, "LastCellRow": 17, "LastCellColumn": 4}, "ConcordanceRowErrorHeader": {"FirstCellRow": 6, "FirstCellColumn": 3, "LastCellRow": 26, "LastCellColumn": 14}, "CoreVariable": {"FirstCellRow": 7, "FirstCellColumn": 3, "LastCellRow": 7, "LastCellColumn": 4}}, "ObservedWorksheet": {"SheetNumber": 8, "DateCell": "M1", "ProtectedObservedRow": {"FirstCellRow": 6, "FirstCellColumn": 2, "LastCellRow": 35, "LastCellColumn": 14}}, "CorrectionWorksheet": {"SheetNumber": 9, "DateCell": "H1", "CorrectionRowHeader": {"FirstCellRow": 8, "FirstCellColumn": 3, "LastCellRow": 19, "LastCellColumn": 14}, "ProtectedCorrectionRow": {"FirstCellRow": 7, "FirstCellColumn": 3, "LastCellRow": 19, "LastCellColumn": 3}}}, "DeskLevel": {"TemplateFileName": "Desk_Level_DQA_", "ReportFileName": "DQA_desk_level_tool_report", "Sheet1Name": "A.Database 1(all DQ indicators)", "Sheet2Name": "B.Database 2(concordance)", "Sheet1Title": "A. Database - Health Facility (HF) data from ", "Sheet2Title": "B. Database 2 - ", "Sheet1Name_FR": "A. Base 1 (tous indicateurs QD)", "Sheet2Name_FR": "B. Base 2 (concordance)", "Sheet1Title_FR": "A. Base de données - Données d’établissements de santé du (ES)", "Sheet2Title_FR": "B. Base de données 2 - ", "SkipExcelRows": 3, "ExcelHeaderRowStartsFrom": 4, "FirstReportTitleFirstCellRow": 5, "FirstReportTitleFirstCellColumn": 2, "FirstReportTitleLastCellRow": 5, "FirstReportTitleLastCellColumn": 8, "FirstReportRow": 7, "FirstReportColumn": 2, "DeskLevelGraphsExcelSetting": {"CompletenessOfReportingGraph": {"NationalLevelType1Graph": {"InitialReportRow": 6, "InitialReportColumn": 12}, "NationalLevelType2Graph": {"InitialReportRow": 10, "InitialReportColumn": 11}, "ProvinceLevelType1Graph": {"InitialReportRow": 24, "InitialReportColumn": 11}, "DistrictType1Graph": {"InitialReportRow": 82, "InitialReportColumn": 11}}, "TimelinessOfReportingGraph": {"NationalLevelType1Graph": {"InitialReportRow": 6, "InitialReportColumn": 12}, "NationalLevelType2Graph": {"InitialReportRow": 10, "InitialReportColumn": 11}, "ProvinceLevelType1Graph": {"InitialReportRow": 24, "InitialReportColumn": 11}, "DistrictType1Graph": {"InitialReportRow": 82, "InitialReportColumn": 11}}, "VariableCompletenessGraph": {"NationalLevelSummaryGraph": {"InitialReportRow": 6, "InitialReportColumn": 12}, "ProvinceLevelSummaryGraph": {"InitialReportRow": 24, "InitialReportColumn": 11}, "DistrictLevelSummaryGraph": {"InitialReportRow": 80, "InitialReportColumn": 11}}, "ConsistencyBetweenVariableGraph": {"NationalLevelSummaryGraph": {"InitialReportRow": 6, "InitialReportColumn": 12}, "ProvinceLevelSummaryGraph": {"InitialReportRow": 25, "InitialReportColumn": 11}, "DistrictLevelSummaryGraph": {"InitialReportRow": 80, "InitialReportColumn": 11}}, "ConsistencyOverTimeGraph": {"NationalLevelGraph": {"ProportionOfMalariaOutpatientsGraph": {"InitialReportRow": 6, "InitialReportColumn": 11}, "ProportionOfMalariaInpatientsGraph": {"InitialReportRow": 20, "InitialReportColumn": 11}, "ProportionOfMalariaInpatientDeathsGraph": {"InitialReportRow": 40, "InitialReportColumn": 11}, "TestPositivityRateGraph": {"InitialReportRow": 60, "InitialReportColumn": 11}, "SlidePositivityRateGraph": {"InitialReportRow": 80, "InitialReportColumn": 11}, "RDTPositivityRateGraph": {"InitialReportRow": 100, "InitialReportColumn": 11}, "ProportionOfSuspectsTestedGraph": {"InitialReportRow": 120, "InitialReportColumn": 11}}, "ProvinceLevelGraph": {"ProportionOfMalariaOutpatientsGraph": {"InitialReportRow": 140, "InitialReportColumn": 11}, "ProportionOfMalariaInpatientsGraph": {"InitialReportRow": 196, "InitialReportColumn": 11}, "ProportionOfMalariaInpatientDeathsGraph": {"InitialReportRow": 253, "InitialReportColumn": 11}, "TestPositivityRateGraph": {"InitialReportRow": 310, "InitialReportColumn": 11}, "SlidePositivityRateGraph": {"InitialReportRow": 366, "InitialReportColumn": 11}, "RDTPositivityRateGraph": {"InitialReportRow": 423, "InitialReportColumn": 11}, "ProportionOfSuspectsTestedGraph": {"InitialReportRow": 480, "InitialReportColumn": 11}}, "DistrictLevelGraph": {"ProportionOfMalariaOutpatientsGraph": {"InitialReportRow": 537, "InitialReportColumn": 11}, "ProportionOfMalariaInpatientsGraph": {"InitialReportRow": 595, "InitialReportColumn": 11}, "ProportionOfMalariaInpatientDeathsGraph": {"InitialReportRow": 652, "InitialReportColumn": 11}, "TestPositivityRateGraph": {"InitialReportRow": 709, "InitialReportColumn": 11}, "SlidePositivityRateGraph": {"InitialReportRow": 766, "InitialReportColumn": 11}, "RDTPositivityRateGraph": {"InitialReportRow": 823, "InitialReportColumn": 11}, "ProportionOfSuspectsTestedGraph": {"InitialReportRow": 880, "InitialReportColumn": 11}}}, "ConcordanceGraph": {"NationalLevelSummaryGraph": {"InitialReportRow": 6, "InitialReportColumn": 12}, "ProvinceLevelSummaryGraph": {"InitialReportRow": 25, "InitialReportColumn": 11}, "DistrictLevelSummaryGraph": {"InitialReportRow": 80, "InitialReportColumn": 11}}}}, "Elimination": {"TemplateFileName": "DQA_Elimination", "ReportFileName": "DQA_elimination_tool_report"}}, "QuestionBank": {"SubNationalSheet": {"Name": "Subnational Level Questionnaire", "Name_FR": "Niveau infranational"}, "ServiceLevelSheet": {"Name": "Service Level Questionnaire", "Name_FR": "Niveau de service"}, "CommunityLevelSheet": {"Name": "Community Level Questionnaire", "Name_FR": "Niveau communautaire"}, "SubNationalSelfAssessmentSheet": {"Name": "Self Assessment-Subnational", "Name_FR": "Aé-Infranationale"}, "ServiceLevelSelfAssessmentSheet": {"Name": "Self Assessment-Service Level", "Name_FR": "Aé-Niveau de service"}, "CommunityLevelSelfAssessmentSheet": {"Name": "Self Assessment-Community Level", "Name_FR": "Aé-Niveau communautaire"}, "HealthFacilitySheet": {"Name": "Select Health Facility", "Name_FR": "Établissement de santé"}, "AssessmentSheet": {"AssessmentSheetStartingRowNumber": 13, "SelfAssessmentSheetStartingRowNumber": 12, "ObjectiveCell": {"Column": "A", "FirstCellRow": 0, "FirstCellColumn": 1, "LastCellRow": 0, "LastCellColumn": 6}, "SubObjectiveCell": {"Column": "A", "FirstCellRow": 0, "FirstCellColumn": 1, "LastCellRow": 0, "LastCellColumn": 6}, "IndicatorSequenceCell": {"Column": "B", "FirstCellRow": 0, "FirstCellColumn": 2, "LastCellRow": 0, "LastCellColumn": 2}, "IndicatorNameCell": {"Column": "C", "FirstCellRow": 0, "FirstCellColumn": 3, "LastCellRow": 0, "LastCellColumn": 3}, "CodeNameCell": {"Column": "D", "FirstCellRow": 0, "FirstCellColumn": 4, "LastCellRow": 0, "LastCellColumn": 4}, "QuestionCell": {"Column": "E", "FirstCellRow": 0, "FirstCellColumn": 5, "LastCellRow": 0, "LastCellColumn": 5}, "ResponseOptionCell": {"Column": "F", "FirstCellRow": 0, "FirstCellColumn": 6, "LastCellRow": 0, "LastCellColumn": 6}, "NotesCell": {"Column": "G", "FirstCellRow": 0, "FirstCellColumn": 7, "LastCellRow": 0, "LastCellColumn": 7}}, "EditableQuestionsIds": ["61C11183-7F06-497E-ADC5-9080B6BC91AA"]}, "AnalyticalOutput": {"ChartData": {"FirstCellRowNumber": 2, "FirstCellColumnNumber": 1, "TitleCellNumber": "Z1"}, "IndicatorsIncludedInTheTemplate": ["1.1.1a", "1.1.1b", "1.1.2a", "1.1.2b", "1.1.5", "1.1.6", "1.1.7e", "1.3.5", "1.3.6"]}, "ShellTable": {"Objective1": {"Name": "Objective 1", "Name_FR": "Objectif 1", "IndicatorSequence": "1", "ProtectCellRange": {"FirstCellRow": 1, "FirstCellColumn": 1, "LastCellRow": 53, "LastCellColumn": 6}, "SubObjective_1_3": {"IndicatorSequence": "1.3", "FirstCellRow": 3, "FirstCellColumn": 1, "LastCellRow": 53, "LastCellColumn": 6}}, "Objective2": {"Name": "Objective 2", "Name_FR": "Objectif 2", "IndicatorSequence": "2", "ProtectCellRange": {"FirstCellRow": 1, "FirstCellColumn": 1, "LastCellRow": 207, "LastCellColumn": 6}, "SubObjective_2_1": {"IndicatorSequence": "2.1", "FirstCellRow": 3, "FirstCellColumn": 1, "LastCellRow": 43, "LastCellColumn": 6}, "SubObjective_2_2": {"IndicatorSequence": "2.2", "FirstCellRow": 44, "FirstCellColumn": 1, "LastCellRow": 86, "LastCellColumn": 6}, "SubObjective_2_3": {"IndicatorSequence": "2.3", "FirstCellRow": 87, "FirstCellColumn": 1, "LastCellRow": 102, "LastCellColumn": 6}, "SubObjective_2_4": {"IndicatorSequence": "2.4", "FirstCellRow": 104, "FirstCellColumn": 1, "LastCellRow": 206, "LastCellColumn": 6}}, "Objective3": {"Name": "Objective 3", "Name_FR": "Objectif 3", "IndicatorSequence": "3", "ProtectCellRange": {"FirstCellRow": 1, "FirstCellColumn": 1, "LastCellRow": 457, "LastCellColumn": 6}, "SubObjective_3_1": {"IndicatorSequence": "3.1", "FirstCellRow": 3, "FirstCellColumn": 1, "LastCellRow": 85, "LastCellColumn": 6}, "SubObjective_3_2": {"IndicatorSequence": "3.2", "FirstCellRow": 86, "FirstCellColumn": 1, "LastCellRow": 185, "LastCellColumn": 6}, "SubObjective_3_3": {"IndicatorSequence": "3.3", "FirstCellRow": 186, "FirstCellColumn": 1, "LastCellRow": 283, "LastCellColumn": 6}, "SubObjective_3_4": {"IndicatorSequence": "3.4", "FirstCellRow": 284, "FirstCellColumn": 1, "LastCellRow": 347, "LastCellColumn": 6}, "SubObjective_3_4_3": {"IndicatorSequence": "3.4", "FirstCellRow": 327, "FirstCellColumn": 341, "LastCellRow": 340, "LastCellColumn": 3}, "SubObjective_3_5": {"IndicatorSequence": "3.5", "FirstCellRow": 348, "FirstCellColumn": 1, "LastCellRow": 412, "LastCellColumn": 6}, "SubObjective_3_6": {"IndicatorSequence": "3.6", "FirstCellRow": 413, "FirstCellColumn": 1, "LastCellRow": 456, "LastCellColumn": 6}}, "Objective4": {"Name": "Objective 4", "Name_FR": "Objectif 4", "IndicatorSequence": "4", "ProtectCellRange": {"FirstCellRow": 1, "FirstCellColumn": 1, "LastCellRow": 182, "LastCellColumn": 8}, "SubObjective_4_1": {"IndicatorSequence": "4.1", "FirstCellRow": 3, "FirstCellColumn": 1, "LastCellRow": 38, "LastCellColumn": 6}, "SubObjective_4_2": {"IndicatorSequence": "4.2", "FirstCellRow": 39, "FirstCellColumn": 1, "LastCellRow": 53, "LastCellColumn": 6}, "SubObjective_4_3": {"IndicatorSequence": "4.3", "FirstCellRow": 54, "FirstCellColumn": 1, "LastCellRow": 129, "LastCellColumn": 6}, "SubObjective_4_4": {"IndicatorSequence": "4.4", "FirstCellRow": 130, "FirstCellColumn": 1, "LastCellRow": 181, "LastCellColumn": 6}}, "HealthFacility": {"Name": "Select Health Facility", "Name_FR": "Etablissement de santé", "FirstCellRow": 1, "FirstCellColumn": 1, "LastCellRow": 1, "LastCellColumn": 8}}}