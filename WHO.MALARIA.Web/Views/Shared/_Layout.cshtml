﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - WHO.MALARIA.Web</title>
    <link rel="stylesheet" href="~/css/bootstrap.min.css" />
    <link rel="stylesheet" href="~/css/site.css" />
</head>
<body>
    <header class="bg-primary">
        <nav class="navbar navbar-expand-sm navbar-toggleable-sm navbar-light box-shadow">
            <div class="container">
                <a class="navbar-brand" asp-area="" asp-controller="Home" asp-action="Index">
                    <img src="~/images/WHO_Logo.png" alt="WHO - Malaria Surveilance Toolkit" />
                </a>
                <button class="navbar-toggler" type="button" data-toggle="collapse" data-target=".navbar-collapse" aria-controls="navbarSupportedContent"
                        aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>               
            </div>
        </nav>
    </header>
    <div class="">
        <div class="section-background-image">
            <main role="main" class="pb-3">
                @RenderBody()
            </main>
        </div>
    </div>

    <footer class="footer bg-primary py-2">
        <div class="container">
            &copy; 2021 - WHO.MALARIA.Web - <a asp-area="" asp-controller="Home" asp-action="Privacy">Privacy</a>
        </div>
    </footer>
    <script src="~/js/jquery.min.js"></script>
    <script src="~/js/popper.js"></script>
    <script src="~/js/bootstrap.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>
    @RenderSection("Scripts", required: false)
</body>
</html>
