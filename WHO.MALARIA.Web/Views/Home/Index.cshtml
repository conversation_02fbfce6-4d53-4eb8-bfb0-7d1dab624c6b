﻿@{
    ViewData["Title"] = "Home Page";
}

<div class="jumbotron">
    @if (User?.Identity?.IsAuthenticated ?? false)
    {
        <h1>Welcome, @User.Identity.Name</h1>

        <p>
            @foreach (var claim in Context.User.Claims)
            {
                <div><code>@claim.Type</code>: <strong>@claim.Value</strong></div>
            }
        </p>

        <a class="btn btn-lg btn-danger" href="/signout?returnUrl=%2F">Sign out</a> }

    else
    {
        <h1>Welcome, anonymous</h1>
        <a class="btn btn-lg btn-success" href="/signin?returnUrl=%2F">Sign in</a>
    }
</div>