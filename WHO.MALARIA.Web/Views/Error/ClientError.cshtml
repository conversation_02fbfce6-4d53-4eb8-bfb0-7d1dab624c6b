﻿
@{
    ViewData["Title"] = "Error";
    Layout = "~/Views/Shared/_Layout.cshtml";
}
@model WHO.MALARIA.Web.Models.ErrorViewModel
@using WHO.MALARIA.Domain.Constants


<div className="errorPage">
    <div class="section-bg-content">
        <h1 class="heading-bg">Oops !</h1>

        <div class="content">Sorry, something went wrong while processing the request.</div>

        <a class="btn app-btn-secondary" href="~/">
            Go Back
        </a>
    </div>
</div>