﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;

namespace WHO.MALARIA.Web.Middlewares
{
    // You may need to install the Microsoft.AspNetCore.Http.Abstractions package into your project
    public class RedirectMiddleware
    {
        private readonly RequestDelegate _next;

        public RedirectMiddleware(RequestDelegate next)
        {
            _next = next;
        }

        public async Task Invoke(HttpContext httpContext)
        {

            string routePath = httpContext.Request.Path.Value;
            bool isAuthenticated = httpContext.User.Identity.IsAuthenticated;
            string redirectUrl;
            if (!isAuthenticated)
            {
                redirectUrl = "/idp";
                httpContext.Response.Redirect(redirectUrl, true);
            }
            

            //string CountryCodeInUrl = "", redirectUrl = "";
            //var countryCode = CookieSettings.ReadCookie();
            //if (countryCode == "")
            //{
            //    countryCode = "gb";
            //}

            //if (httpContext.Request.Path.Value.Length >= 2)
            //{
            //    CountryCodeInUrl = httpContext.Request.Path.Value.Substring(1, 2);
            //}

            //if (countryCode != CountryCodeInUrl)
            //{
            //    if (httpContext.Request.Path.Value.Length >= 2)
            //    {
            //        if (httpContext.Request.Path.Value.Substring(1, 2) != "")
            //        {
            //            countryCode = httpContext.Request.Path.Value.Substring(1, 2);
            //        }
            //    }
            //    if (!httpContext.Request.Path.Value.Contains(countryCode))
            //    {
            //        redirectUrl = string.Format("/{0}{1}", countryCode, httpContext.Request.Path.Value);
            //    }
            //    else
            //    {
            //        redirectUrl = httpContext.Request.Path.Value;
            //    }
            //    //CookieSettings.SaveCookie(countryCode);
            //    httpContext.Response.Redirect(redirectUrl, true);
            //}

             await _next.Invoke(httpContext);
        }
    }

    // Extension method used to add the middleware to the HTTP request pipeline.
    public static class RedirectMiddlewareExtensions
    {
        public static IApplicationBuilder UseMiddlewareClassTemplate(this IApplicationBuilder builder)
        {
            return builder.UseMiddleware<RedirectMiddleware>();
        }
    }
}
