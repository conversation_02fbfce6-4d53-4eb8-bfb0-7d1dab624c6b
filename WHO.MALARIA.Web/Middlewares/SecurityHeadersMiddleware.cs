﻿using System.Threading.Tasks;
using Microsoft.Extensions.Hosting;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Primitives;

namespace WHO.MALARIA.Web.Middlewares
{
    /// <summary>
    /// Contains the custom headers that need to be added/removed from the each response
    /// </summary>
    public class SecurityHeadersMiddleware
    {
        private readonly RequestDelegate _next;

        public SecurityHeadersMiddleware(RequestDelegate next)
        {
            _next = next;
        }

        public async Task Invoke(HttpContext context, IWebHostEnvironment env)
        {
            context.Response.Headers.Remove("X-AspNet-Version");

            context.Response.Headers.Add("Strict-Transport-Security", new StringValues("max-age=63072000; includeSubDomains; preload"));

            context.Response.Headers.Add("X-Frame-Options", new StringValues("DENY"));

            context.Response.Headers.Add("X-XSS-Protection", new StringValues("1; mode=block"));

            context.Response.Headers.Add("X-Content-Type-Options", new StringValues("nosniff"));

            context.Response.Headers.Add("Referrer-Policy", new StringValues("same-origin"));

            if (context.Request.Path.Value.Contains("/api/"))
                context.Response.Headers.Add("Content-Type", new StringValues("application/json; charset=utf-8"));

            //TODO : Need to verify 'unsafe-inline' is required for which scripts
            string script_src = $"script-src 'self' 'unsafe-eval' 'unsafe-inline';";

            //Added google analytics to content-security-policy
            string script_src_ga = $"script-src 'self' 'unsafe-eval' 'unsafe-inline' https://www.google-analytics.com https://ssl.google-analytics.com https://www.googletagmanager.com;";

            context.Response.Headers.Add("Content-Security-Policy", new StringValues($"{script_src_ga} default - src 'self'; {script_src} style-src 'self' https://fonts.googleapis.com 'unsafe-inline'; font-src 'self' https://fonts.gstatic.com data: ; img-src 'self' blob: data: ; object-src 'self'; frame-ancestors 'none'; base-uri 'self'; upgrade-insecure-requests"));

            await _next.Invoke(context);
        }
    }

    public static class SecurityHeadersMiddlewareExtension
    {
        public static IApplicationBuilder UseSecurityHeaders(this IApplicationBuilder builder) => builder.UseMiddleware<SecurityHeadersMiddleware>();
    }
}
