﻿
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Diagnostics;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc.ViewFeatures;
using Microsoft.Extensions.Configuration;

using Newtonsoft.Json;

using Serilog;

using System.Collections.Generic;

using WHO.MALARIA.Domain.Exceptions;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Features.Exceptions;
using WHO.MALARIA.Web.Models;
using System.Linq;
using WHO.MALARIA.Features.Helpers;
using System;

namespace WHO.MALARIA.Web.Middlewares
{
    /// <summary>
    /// Global Middleware method to handle and log exceptions in log table.
    /// </summary>
    public static class ExceptionMiddlewareExtensions
    {
        public static void ConfigureExceptionHandler(this IApplicationBuilder app,
            bool isProduction, ITempDataProvider tempDataProvider, IConfiguration configuration, AppSettings appSettings)
        {
            app.UseExceptionHandler(appError =>
            {
                appError.Run(async context =>
                {
                    IExceptionHandlerFeature contextFeature = context.Features.Get<IExceptionHandlerFeature>();

                    // isAPIRequest - check request is api request or not
                    // ex - {Domain}/api/{action} - https://malaria-toolkit.azurewebsites.net/api/countries
                    if (contextFeature != null && isApiRequest(context))
                    {
                        if (contextFeature.Error is BusinessRuleValidationException)
                        {
                            context.Response.ContentType = "application/json";
                            BusinessRuleValidationException businessRuleValidationException = contextFeature.Error as BusinessRuleValidationException;

                            context.Response.StatusCode = businessRuleValidationException.StatusCode;
                            await context.Response.WriteAsync(new ErrorDetails()
                            {
                                StatusCode = businessRuleValidationException.StatusCode,
                                Message = businessRuleValidationException.InnerException?.ToString() ?? businessRuleValidationException.Message,
                            }.ToString());
                        }
                        else
                        {
                            // when request api     
                            Log.Error(contextFeature.Error, "Logging with StatusCode, Message{@result}", contextFeature.Error);
                            context.Response.ContentType = "application/json";
                            await context.Response.WriteAsync(new ErrorDetails()
                            {
                                StatusCode = context.Response.StatusCode,
                                Message = GetErrorMessage(contextFeature, isProduction),
                            }.ToString());
                        }
                    }
                    else
                    {
                        // when request non-api    
                        // ex - {Domain}/{action} - https://malaria-toolkit.azurewebsites.net/<api>
                        Log.Error(contextFeature.Error, "Logging with StatusCode, Message{@result}", contextFeature.Error);

                        // when exception occurred we are redirecting user to error page                         
                        // and we have to pass error data to error page
                        // reason is with new get request we don't want to pass data in querystring and show in URL
                        // that is why we have used TempDataProvider
                        tempDataProvider.SaveTempData(context, new Dictionary<string, object>
                        {
                            ["ErrorStatusCode"] = context.Response.StatusCode,
                        });

                        tempDataProvider.SaveTempData(context, new Dictionary<string, object>
                        {
                            ["ErrorMessage"] = isProduction ?
                                    "Internal Server Error." :
                                    contextFeature.Error.InnerException?.ToString() ?? contextFeature.Error.Message
                        });

                        ErrorDetails errorDetails = new ErrorDetails()
                        {
                            StatusCode = context.Response.StatusCode,
                            Message = isProduction ?
                                    "Internal Server Error." :
                                    contextFeature.Error.InnerException?.ToString() ?? contextFeature.Error.Message,
                        };

                        tempDataProvider.SaveTempData(context, new Dictionary<string, object>
                        {
                            ["ErrorDetails"] = JsonConvert.SerializeObject(errorDetails),
                        });

                        context.Response.Redirect(configuration[context.GetApplicationURL()] + "/Error/Info");
                    }
                });
            });
        }

        // To check request is api request or not
        // ex - {Domain}/api/{action} - https://malaria-toolkit.azurewebsites.net/api/study
        private static bool isApiRequest(HttpContext context)
        {
            return context.Request.Path.Value.Contains("/api/");
        }

        /// <summary>
        /// Checks exception type and return user friendly message
        /// </summary>
        /// <param name="contextFeature">Object of ExceptionHandlerFeature</param>
        /// <returns>Returns user friendly message for exception</returns>
        private static string GetErrorMessage(IExceptionHandlerFeature contextFeature, bool isProduction)
        {
            if (isProduction)
            {
                if (contextFeature.Error is System.AggregateException exception)
                {
                    string message = exception.InnerExceptions.FirstOrDefault(e => e is InSufficiantPermissionException)?.Message;
                    if (!string.IsNullOrEmpty(message)) return message;
                }

                if (contextFeature.Error is InSufficiantPermissionException
                    || contextFeature.Error is VirusScanException
                    || contextFeature.Error is VirusScanFailedException
                    || contextFeature.Error is InvalidHealthFacilityException)
                {
                    return contextFeature.Error.Message;
                }

                return "Internal Server Error.";
            }
            return contextFeature.Error.InnerException?.ToString() ?? contextFeature.Error.Message;
        }
    }
}
