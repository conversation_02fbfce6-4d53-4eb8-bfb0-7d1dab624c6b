import {
    Chart,
    ChartTitle,
    ChartSeries,
    ChartSeriesItem,
    ChartCategoryAxis,
    ChartCategoryAxisTitle,
    ChartCategoryAxisItem,
    ChartProps,
    ChartValueAxis,
    ChartValueAxisItem
} from "@progress/kendo-react-charts";

import "hammerjs";
import { UtilityHelper } from "../../utils/UtilityHelper";

interface IBarChart {
    chartTitle: string; //Chart title
    chartCategoryAxisTitle: string; //Chart x-axis title
    barChartCategories: Array<string | number>; //Chart x-axis tiles
    barData: Array<any>; //Chart Data
    displayType: any; //Chart type
}

type BarChartProps = IBarChart & ChartProps;

/** Renders Bar Chart with values */
const BarChart = (props: BarChartProps) => {
    const {
        chartTitle,
        barChartCategories,
        chartCategoryAxisTitle,
        displayType,
        barData,
    } = props;

    let alignLeft: any;
    const currentlanguage = UtilityHelper.getCookieValue("i18next");

    if (currentlanguage === "fr")
        alignLeft = "left";

    return (
        <Chart {...props}>
            <ChartTitle text={chartTitle} align={alignLeft} />
            <ChartValueAxis>
                <ChartValueAxisItem
                    min={0} max={100} />
            </ChartValueAxis>
            <ChartCategoryAxis>
                <ChartCategoryAxisItem categories={barChartCategories}>
                    <ChartCategoryAxisTitle text={chartCategoryAxisTitle} />
                </ChartCategoryAxisItem>
            </ChartCategoryAxis>
            <ChartSeries>
                {barData.map((item) => (
                    <ChartSeriesItem
                        type={displayType}
                        gap={2}
                        spacing={0.1}
                        data={item}
                    />
                ))}
            </ChartSeries>
        </Chart>
    );
};

export default BarChart;
