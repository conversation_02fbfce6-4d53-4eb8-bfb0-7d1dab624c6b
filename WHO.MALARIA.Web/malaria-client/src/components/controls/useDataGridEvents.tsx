import { useState } from "react";
import { CompositeFilterDescriptor } from "@progress/kendo-data-query";
import {
  GridFilterCellProps,
  GridFilterChangeEvent,
  GridPageChangeEvent,
  GridSortChangeEvent,
} from "@progress/kendo-react-grid";
import {
  FilterCriteria,
  GridBaseRequestModel,
  SortCriteria,
} from "../../models/GridBaseRequestModel";
import { Operator, SortDirection } from "../../models/Enums";
// import { Date | null } from "@material-ui/pickers/typings/date";

/**  Centralized location to handle all grid events like pagination, filteration */
function useDataGridEvents() {
  const [initialFilter, setInitialFilter] = useState<CompositeFilterDescriptor>(
    {
      logic: "and",
      filters: [],
    }
  );

  const [gridRequestModel, setGridRequestModel] =
    useState<GridBaseRequestModel>(GridBaseRequestModel.init());

  /** Triggers whenever user change thes filger  */
  const onFilterChange = (evt: GridFilterChangeEvent) => {
    const updatedModel = {
      ...gridRequestModel,
      skip: gridRequestModel.skip = 0,
      filterCriterias: evt?.filter?.filters as FilterCriteria[],
    };

    setGridRequestModel(updatedModel);
    setInitialFilter({
      ...initialFilter,
      filters: evt?.filter?.filters,
    });
  };

  /** Triggers whenever user changes the page */
  const onPageChange = (evt: GridPageChangeEvent) => {
    const updatedModel = {
      ...gridRequestModel,
      take: evt.page.take,
      skip: evt.page.skip,
    };
    setGridRequestModel(updatedModel);
  };

  /** Triggers whenever user changes the sort */
  const onSortChange = (evt: GridSortChangeEvent) => {
    if (evt.sort.length === 0) return [];

    const sort = new SortCriteria(
      evt.sort[0].field,
      evt.sort[0].dir === "asc" ? SortDirection.Asc : SortDirection.Dsc
    );

    const updatedModel = {
      ...gridRequestModel,
      sortCriterias: sort,
    };
    setGridRequestModel(updatedModel);
  };

  /** Triggers whenever any control (not date) from filter is changed */
  const onCustomFilterChange = (
    props: GridFilterCellProps,
    evt: React.ChangeEvent<HTMLInputElement>
  ) => {
    props.onChange({
      value: evt.target.value ? evt.target.value : "",
      operator: evt.target.value ? "contains" : "",
      syntheticEvent: evt,
    });
  };

  /** Triggers whenever date from filter is changed */
  const onCustomDateFilterChange = (
    props: GridFilterCellProps,
    date: string | null | undefined,
    evt: any
  ) => {
    console.log(date, "date");
    props.onChange({
      value: date || "",
      operator: "eq",
      syntheticEvent: evt,
    });
  };

  return {
    gridRequestModel,
    initialFilter,
    onCustomFilterChange,
    onCustomDateFilterChange,
    onFilterChange,
    onPageChange,
    onSortChange,
  };
}

export default useDataGridEvents;
