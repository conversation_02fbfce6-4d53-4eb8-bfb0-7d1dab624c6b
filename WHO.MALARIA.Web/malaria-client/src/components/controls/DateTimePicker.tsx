﻿import React from "react";
import { DateTimePicker as MuiDateTimePicker } from "@mui/x-date-pickers/DateTimePicker";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { TextField } from "@mui/material";

import { Constants } from "../../models/Constants";

interface DateTimePickerProps {
  value?: Date | null;
  onChange?: (date: Date | null) => void;
  format?: string;
  error?: boolean;
  helperText?: string;
  label?: string;
  disabled?: boolean;
  disablePast?: boolean;
  ampm?: boolean;
  className?: string;
}

/** Renders Date Time picker control */
const DateTimePicker = (props: DateTimePickerProps) => {
  const { format, error, helperText, disablePast, ampm, ...otherProps } = props;

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <MuiDateTimePicker
        format={format || Constants.Common.DefaultDateTimeFormat}
        disablePast={disablePast || false}
        ampm={ampm !== false}
        {...otherProps}
        slots={{
          textField: TextField,
        }}
        slotProps={{
          textField: {
            error: error,
            helperText: helperText,
            variant: "outlined",
            margin: "normal",
          },
        }}
      />
    </LocalizationProvider>
  );
};

export default DateTimePicker;
