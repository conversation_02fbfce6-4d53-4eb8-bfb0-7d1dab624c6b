﻿
import Modal from "./Modal";
import DownloadProgressIcon from "../../images/DownloadProgressIcon.png";
import { useTranslation } from "react-i18next";

type DownloadInprogressModelProps = {
  isFileDownloading: boolean;
}

/** Renders download in progress modal */
const DownloadInProgressModal = (props: DownloadInprogressModelProps) => {

  const { isFileDownloading } = props;
  const { t } = useTranslation();

  return (
    <>
      {/* Modal to show download in progress */}
      <Modal
        open={isFileDownloading}
        modalClassName="app-modal-xs"
        onEscPress={false}
      >
        <div className="d-flex flex-column align-items-center justify-content-center">
          <img
            src={DownloadProgressIcon}
            alt={t("translation:Landing.DownloadInprogress")}
          />
          <p className="p-4">
            <b>{t("translation:Landing.DownloadInprogress")}</b>
          </p>
        </div>
      </Modal>
    </>
  );
};

export default DownloadInProgressModal;
