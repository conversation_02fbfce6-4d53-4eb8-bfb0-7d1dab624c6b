import React, { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, useEffect } from "react";
import Stepper, { Orientation, StepperProps } from "@mui/material/Stepper";
import Step from "@mui/material/Step";
import { StepLabel } from "@mui/material";
import Button from "@mui/material/Button";
import classNames from "classnames";
import { StepperModel } from "../../models/StepperModel";
import classes from "./stepper.module.scss";

interface IStepper {
  /**
   * Set the steps (Array<StepperModel>)
   */

  steps: Array<StepperModel>;

  /** Step Component */
  children?: React.ReactElement;

  /** When true show next/prev buttons */
  showHideButtons?: boolean;

  /** pass classname to manage stepper */
  className?: string;

  /**
   * Set the active step (zero based index).
   * Set to -1 to disable all the steps.
   */
  activeStep?: number;

  /**
   * An element to be placed between each step.
   */
  connector?: React.ReactElement<any, any>;
  /**
   * If set the `Stepper` will not assist in controlling steps for linear flow.
   */
  nonLinear?: boolean;
  /**
   * The stepper orientation (layout flow direction).
   */
  orientation?: Orientation;

  alternativeLabel?: boolean;

  enableStepClick?: boolean;

  /** Triggers whenever user changes the step
   * @aparam curernt active Step
   */
  onStepChange?: (curerntStep: number) => void;
}
/** Renders Stepper
 * @param props IStepper
 */
const WHOStepper = (props: IStepper) => {
  const {
    steps,
    onStepChange,
    children,
    showHideButtons,
    className,
    enableStepClick,
  } = props;
  const activeStep: number = props.activeStep || 0;

  return (
    <>
      <Stepper className={className} activeStep={activeStep} {...props}>
        {steps.map(({ id, text }, index: number) => (
          <Step
            key={`step_${id}_${index}`}
            onClick={() =>
              enableStepClick && onStepChange && onStepChange(index)
            }
          >
            <StepLabel>{text}</StepLabel>
          </Step>
        ))}
      </Stepper>

      {activeStep === steps.length ? (
        <div>
          <div className={classNames(classes.instructions)}>
            All steps completed
          </div>
        </div>
      ) : (
        <>
          <div className={classes.instructions}>
            {steps[activeStep]?.component && steps[activeStep]?.component}
            {children && children}
          </div>
          {showHideButtons && (
            <>
              <Button
                disabled={activeStep === 0}
                onClick={() => onStepChange && onStepChange(activeStep - 1)}
                className={classes.backButton}
              >
                Back
              </Button>
              <Button
                variant="contained"
                color="primary"
                onClick={() => onStepChange && onStepChange(activeStep + 1)}
              >
                {activeStep === steps.length - 1 ? "Finish" : "Next"}
              </Button>
            </>
          )}
        </>
      )}
    </>
  );
};

export default WHOStepper;
