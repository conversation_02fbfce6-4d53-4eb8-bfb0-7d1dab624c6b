@use "../../mixins.scss" as *;

.label {
  display: block;
  padding: 1rem 1rem;
  color: #686868;
  font-weight: normal;
  text-transform: capitalize;
  text-decoration: none;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out,
    border-color 0.15s ease-in-out;
  cursor: pointer;
}
.label.selected {
  color: #008dc3;
  font-weight: 700;
}
.label.selected svg path {
  fill: #008dc3;
}
.label.disabled {
  color: #ddd;
}
.label.label.disabled svg path {
  fill: #ddd;
}
.br_b {
  border-bottom: 1px solid #ddd;
}
.label svg {
  margin-right: 10px;
  vertical-align: bottom;
}
.completedStep {
  color: #000 !important;
  cursor: pointer !important;
  svg {
    path {
      fill: #000 !important;
    }
  }
}
.completed {
  cursor: pointer !important;
  svg {
    margin-top: -20px;
    margin-right: 0px;

    path {
      fill: $green_color !important;
    }
  }
}
