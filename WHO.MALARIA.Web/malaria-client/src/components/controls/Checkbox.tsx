﻿import {
  FormControlLabel,
  Checkbox as CheckboxControl,
  CheckboxProps,
  FormHelperText,
} from "@mui/material";

interface ICheckboxProps extends CheckboxProps {
  label: string;
  helperText?: string;
}

/** Renders Checkbox controls */
const Checkbox = (props: ICheckboxProps) => {
  const { label, helperText } = props;
  return (
    <>
      <FormControlLabel
        control={<CheckboxControl {...props} className="checkbox-control" />}
        label={label}
        className="col-checkbox-control"
      />
      {helperText && <FormHelperText>{helperText}</FormHelperText>}
    </>
  );
};

export default Checkbox;
