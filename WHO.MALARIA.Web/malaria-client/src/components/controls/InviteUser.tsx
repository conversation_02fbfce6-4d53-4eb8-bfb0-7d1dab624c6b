import React, { useState } from "react";
import classNames from "classnames";
import classes from "../user/user.module.scss";
import Textbox from "./TextBox";
import Dropdown from "./Dropdown";
import { InviteUserModel } from "../../models/UserModel";
import ModalFooter from "./ModalFooter";
import { useTranslation } from "react-i18next";
import { CreateSuperManagerRequestModel } from "../../models/RequestModels/UserRequestModel";
import { userService } from "../../services/userService";
import { DialogAction, UserRoleEnum } from "../../models/Enums";
import MultiSelectModel from "../../models/MultiSelectModel";
import RadioButtonGroup from "../controls/RadioButtonGroup";
import { authService } from "../../services/authService";
import { UtilityHelper } from "../../utils/UtilityHelper";

type AddSuperManagerProps = {
    onDialogClose: (action: DialogAction) => void;
    countries: Array<MultiSelectModel>;
};

/** Renders Form in "Invite and Assign New User" Tab  */
const InviteUser = (props: AddSuperManagerProps) => {
    const { t } = useTranslation();
    const [error, setError] = useState<any>({});
    let isSubmitted: boolean = false;
    const { onDialogClose, countries } = props;
    const [superManager, setSuperManager] = useState<InviteUserModel>(
        InviteUserModel.init()
    );

    // triggers whenever user make changes to the input control
    const onChange = (evt: React.ChangeEvent<HTMLInputElement>) => {
        setSuperManager({
            ...superManager,
            [evt.target.name]: evt.target.value,
        });

        // delete the error if value is already filled in
        if (evt.target.value.length > 0) {
            delete error[evt.target.name];
        }
    };
    // validate form
    const validate = () => {
        let error: any = {};
        if (!superManager.email) {
            error = { ...error, ["email"]: "Field is mandatory" };
        }
        if (!superManager.name) {
            error = { ...error, ["name"]: "Field is mandatory" };
        }
        if (!superManager.assignCountry) {
            error = { ...error, ["assignCountry"]: "Field is mandatory" };
        }
        if (!superManager.organizationName) {
            error = { ...error, ["organizationName"]: "Field is mandatory" };
        }
        return error;
    };
    const hasError = Object.keys(validate()).length > 0;

    const currentUser = authService.getCurrentUser();

    // triggered when user clicks on 'save' button to create super manager
    const onInvite = () => {
        isSubmitted = true;
        const error = validate();
        if (isSubmitted && hasError) {
            setError(error);
            return;
        }
        isSubmitted = true;
        setError({});

        userService
            .createSuperManager(
                new CreateSuperManagerRequestModel(
                    currentUser?.userId,
                    superManager.email,
                    superManager.assignCountry,
                    superManager.name,
                    superManager.organizationName
                )
            )
            .then((success: any) => {
                if (success) {
                    onDialogClose(DialogAction.Add);
                }

                //Tracking add user as super manager event in analytics
                UtilityHelper.onEventAnalytics("Add new user", "Super manager", "Add new user");
            });
    };

    return (
        <div className={classNames(classes.formWrapper)}>
            <div className="row mb-3 mt-3">
                <div className="col-xs-12 col-md-12">
                    <label>{t("UserManagement.Role")}</label>
                    <RadioButtonGroup
                        id="userType"
                        name="userType"
                        color="primary"
                        options={[
                            new MultiSelectModel(UserRoleEnum.SuperManager, t("UserManagement.Supermanager"))
                        ]}
                        value={superManager.userType}
                    />
                </div>
            </div>
            <div className="row mb-3 mt-3">
                <div className="col-xs-12 col-md-6">
                    <Textbox
                        id="email"
                        label={t("UserManagement.Email")}
                        name="email"
                        fullWidth
                        required
                        value={superManager.email}
                        error={error["email"] ? true : false}
                        helperText={error["email"]}
                        onChange={onChange}
                        className="inputfocus"
                        maxLength={256}
                    />
                </div>
                <div className="col-xs-12 col-md-6">
                    <Textbox
                        id="name"
                        label={t("UserManagement.Name")}
                        name="name"
                        fullWidth
                        required
                        value={superManager.name}
                        error={error["name"] ? true : false}
                        helperText={error["name"]}
                        onChange={onChange}
                        className="inputfocus"
                        maxLength={100}
                    />
                </div>
            </div>
            <div className="row mb-3">
                <div className="col-xs-12 col-md-6">
                    <Textbox
                        id="organizationName"
                        name="organizationName"
                        label={t("UserManagement.OrganizationName")}
                        fullWidth
                        required
                        value={superManager.organizationName}
                        error={error["organizationName"] ? true : false}
                        helperText={error["organizationName"]}
                        onChange={onChange}
                        className="inputfocus"
                        maxLength={100}
                    />
                </div>
                <div className="col-xs-12 col-md-6">
                    <Dropdown
                        id="assignCountry"
                        name="assignCountry"
                        label={t("UserManagement.ChooseCountry")}
                        fullWidth
                        required
                        value={superManager.assignCountry}
                        error={error["assignCountry"] ? true : false}
                        helperText={error["assignCountry"]}
                        options={countries}
                        onChange={onChange}
                        className="inputfocus"
                    />
                </div>
            </div>
            <ModalFooter>
                <>
                    <button
                        className="btn app-btn-secondary"
                        onClick={() => onDialogClose(DialogAction.Close)}
                    >
                        {t("Common.Cancel")}
                    </button>
                    <button className="btn app-btn-primary" onClick={onInvite}>
                        {t("Common.InviteUser")}
                    </button>
                </>
            </ModalFooter>
        </div>
    );
};

export default InviteUser;
