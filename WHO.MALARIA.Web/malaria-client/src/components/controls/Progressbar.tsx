import {
  Box,
  LinearProgress,
  LinearProgressProps,
  makeStyles,
  Typography,
} from "@mui/material";

/** Renders Progress bar */
function Progressbar({ value = 0, className = "" }) {
  function LinearProgressWithLabel(
    props: LinearProgressProps & { value: number }
  ) {
    return (
      <Box className="box-wrap" display="flex" alignItems="center">
        <Box width="100%" mr={1}>
          <LinearProgress
            className="progress-custom-wrap"
            variant="determinate"
            {...props}
          />
        </Box>
        <Box minWidth={100}>
          <Typography variant="body2" color="textSecondary">{`${Math.round(
            props.value
          )}% completed`}</Typography>
        </Box>
      </Box>
    );
  }

  const useStyles = makeStyles({
    root: {
      width: "100%",
    },
  });

  const classes = useStyles();
  return (
    <div className={classes.root}>
      <LinearProgressWithLabel value={value} />
    </div>
  );
}

export default Progressbar;
