import { ReactNode, useState } from "react";
import {
    DataResult,
    process,
    SortDescriptor,
    State,
} from "@progress/kendo-data-query";
import {
    Grid,
    GridColumn as Column,
    GridColumnProps,
    GridHeaderCellProps,
    GridProps,
    GridSortChangeEvent,
} from "@progress/kendo-react-grid";
import GridHeaderCell from "./GridHeaderCell";

type DataGridProps = GridProps & {
    columns: Array<GridColumnProps>;
    className?: string;
    defaultHeight?: string;
    initialSort?: Array<SortDescriptor>;
    onSortChange?: (e: GridSortChangeEvent) => void;
    onFilterChange?: (e: any) => void;
    filterable?: boolean;
    hasActionBtn?: boolean;
};

/* Renders data grid */
const DataGrid = (props: DataGridProps) => {
    const {
        columns,
        className,
        defaultHeight,
        data,
        total,
        pageable,
        take,
        skip,
        hasActionBtn,
    } = props;

    const [dataState, setDataState] = useState<State>({
        skip,
        take,
        sort: [],
        group: [],
    });

    const [dataResult, setDataResult] = useState<DataResult>(
        process(data as any[], dataState)
    );
    const [openFilter, setOpenFilter] = useState(false);

    // triggered whenever user clicks on filter icon
    const onFilterClick = (evt: React.MouseEvent<HTMLSpanElement>) => {
        setOpenFilter((prevState) => !prevState);
    };

    return (
        <Grid
            style={{ height: defaultHeight || "auto" }}
            filterable={openFilter}
            className={hasActionBtn ? "k-grid-wrapper k-grid-action-wrapper" : "k-grid-wrapper"}
            pageable={pageable ? { buttonCount: 4, pageSizes: false } : undefined}
            total={total}
            headerCellRender={(
                defaultRendering: ReactNode | null,
                headerCellProps: GridHeaderCellProps
            ) => (
                <GridHeaderCell
                    open={openFilter}
                    data={data}
                    hasActionBtn={hasActionBtn}
                    onFilterClick={onFilterClick}
                    {...headerCellProps}
                    {...props}
                />
            )}
            {...props}
        >
            {columns.map((columnProps: GridColumnProps, index: number) => (
                <Column key={`${columnProps.field}_${index}`} {...columnProps} locked={columnProps.locked ? true : false} />
            ))}
        </Grid>
    );
};

export default DataGrid;
