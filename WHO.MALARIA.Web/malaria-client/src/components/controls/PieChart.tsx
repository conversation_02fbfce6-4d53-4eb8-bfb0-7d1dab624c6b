import * as React from "react";
import {
  Chart,
  ChartLegend,
  ChartProps,
  ChartSeries,
  ChartSeriesItem,
  ChartTitle,
} from "@progress/kendo-react-charts";
import "hammerjs";
import { SeriesType } from "@progress/kendo-react-charts/dist/npm/field-types/series-type";

const labelContent = (props: any) => {
  let formatedNumber = Number(props.dataItem.value).toLocaleString(undefined, {
    style: "percent",
    minimumFractionDigits: 2,
  });
  return `${props.dataItem.category} ${props.labelText} ${formatedNumber}`;
};

interface IPieChart {
  title: string; // Chart Title
  data: Array<any>; // Chart Data
  showLabel?: boolean; // Chart label
  labelText: string; // Chart label text

  /**
   * The positions of the Chart legend.
   *
   * The supported values are:
   * - `"top"`&mdash;The legend is positioned on the top.
   * - `"bottom"`&mdash;The legend is positioned on the bottom.
   * - `"left"`&mdash;The legend is positioned on the left.
   * - `"right"`&mdash;The legend is positioned on the right.
   * - `"custom"`&mdash;The legend is positioned by using [`legend.offsetX`]({% slug api_charts_chartlegendprops %}#toc-offsetx) and [`legend.offsetY`]({% slug api_charts_chartlegendprops %}#toc-offsety).
   */
  position?: "top" | "bottom" | "left" | "right" | "custom";
  /**
   * The type of the series.
   *
   * The supported values are:
   * - `area`
   * - `bar`
   * - `boxPlot`
   * - `bubble`
   * - `bullet`
   * - `candlestick`
   * - `column`
   * - `donut`
   * - `funnel`
   * - `horizontalWaterfall`
   * - `line`
   * - `ohlc`
   * - `pie`
   * - `polarArea`
   * - `polarLine`
   * - `polarScatter`
   * - `radarArea`
   * - `radarColumn`
   * - `radarLine`
   * - `rangeArea`
   * - `rangeBar`
   * - `rangeColumn`
   * - `scatter`
   * - `scatterLine`
   * - `verticalArea`
   * - `verticalBoxPlot`
   * - `verticalBullet`
   * - `verticalLine`
   * - `verticalRangeArea`
   * - `waterfall`
   */
  type?: SeriesType;

  /**
   * The data item field which contains the series value.
   */
  field?: string;

  /**
   * The data item field which contains the category name or date. If the category is a date, the points are rendered in chronological order.
   */
  categoryField?: string;
}

type PieChartProps = IPieChart & ChartProps;

/** Renders PIE Chart with values */
const PieChart = (props: PieChartProps) => {
  const { title, data, showLabel, position, type, field, categoryField } =
    props;
  return (
    <Chart {...props}>
      <ChartTitle text={title} />
      <ChartLegend position={position || "bottom"} />
      <ChartSeries>
        <ChartSeriesItem
          type={type || "pie"}
          data={data}
          field={field || "value"}
          categoryField={categoryField || "category"}
          labels={{ visible: showLabel || false, content: labelContent }}
        />
      </ChartSeries>
    </Chart>
  );
};

export default PieChart;
