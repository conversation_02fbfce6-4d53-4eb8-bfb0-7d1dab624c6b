﻿
import { TabPanelModel } from '../../models/TabModel';

/**
 * Renders TabPanel within tab control
 * @param props
 */
const WHOTabPanel = (props: TabPanelModel) => {
    const { children, value, index, ...other } = props;

    return (
        <div
            role="tabpanel"
            hidden={value !== index}
            id={`scrollable-auto-tabpanel-${index}`}
            className="tab-panel"
            aria-labelledby={`scrollable-auto-tab-${index}`}
            {...other}
        >
            {value === index && (
                children 
            )}
        </div>
    );
}

export default WHOTabPanel;
