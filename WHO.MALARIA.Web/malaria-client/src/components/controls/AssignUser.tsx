import React, { useState } from "react";
import classNames from "classnames";
import classes from "../user/user.module.scss";
import Textbox from "./TextBox";
import Dropdown from "./Dropdown";
import ModalFooter from "./ModalFooter";
import { useTranslation } from "react-i18next";
import { DialogAction, UserRoleEnum } from "../../models/Enums";
import { AssignSuperManagerModel } from "../../models/UserModel";
import MultiSelectModel from "../../models/MultiSelectModel";
import { userService } from "../../services/userService";
import { CreateSuperManagerRequestModel } from "../../models/RequestModels/UserRequestModel";
import { authService } from "../../services/authService";
import RadioButtonGroup from "../controls/RadioButtonGroup";
import { UtilityHelper } from "../../utils/UtilityHelper";

// properties for User
type AssignSuperManagerProps = {
  onDialogClose: (action: DialogAction) => void;
  countries: Array<MultiSelectModel>;
};

/** Renders Form in "Assign Existing User" Tab  */
const AssginUser = (props: AssignSuperManagerProps) => {
  const { t } = useTranslation();
  const [error, setError] = useState<any>({}); //  Error details
  let isSubmitted: boolean = false; //  check submit
  const { onDialogClose, countries } = props; //  Close Modal
  const currentUser = authService.getCurrentUser();

  const [assignSuperManager, setAssignSuperManager] =
    useState<AssignSuperManagerModel>(AssignSuperManagerModel.init());

  // triggered when user clicks on 'Assign' button to create super manager
  const onAssign = () => {
    isSubmitted = true;
    const error = validateUpdate();
    if (isSubmitted && hasError) {
      setError(error);
      return;
    }
    isSubmitted = true;
    setError({});

    userService
      .createExistingSuperManager(
        new CreateSuperManagerRequestModel(
          currentUser.userId,
          assignSuperManager.email,
          assignSuperManager.countryId,
          assignSuperManager.name,
          assignSuperManager.organizationName,
          assignSuperManager.role
        )
      )
      .then((response: any) => {
        if (response) {
          onDialogClose(DialogAction.Add);
        }

        //Tracking assign existing user as super manager or WHO admin event in analytics
        UtilityHelper.onEventAnalytics("Assign existing user", "Assign existing user as Super manager or WHO admin", "Assign existing user as Super manager or WHO admin");

      });
  };

  // validate update form
  const validateUpdate = () => {
    let error: any = {};
    if (!assignSuperManager.email) {
      error = { ...error, ["email"]: t("Errors.MandatoryField") };
    }
    if (!assignSuperManager.name) {
      error = { ...error, ["name"]: t("Errors.MandatoryField") };
    }
    if (!assignSuperManager.countryId && assignSuperManager.role !== UserRoleEnum.WHOAdmin) {
      error = { ...error, ["countryId"]: t("Errors.MandatoryField") };
    }
    if (!assignSuperManager.organizationName) {
      error = { ...error, ["organizationName"]: t("Errors.MandatoryField") };
    }
    if (!assignSuperManager.role) {
      error = { ...error, ["role"]: t("Errors.MandatoryField") };
    }
    return error;
  };

  const hasError = Object.keys(validateUpdate()).length > 0;

  // triggers whenever user make changes to the input control
  const onUpdateChange = (evt: React.ChangeEvent<HTMLInputElement>) => {
    setAssignSuperManager({
      ...assignSuperManager,
      [evt.target.name]: evt.target.value,
    });

    // delete the eror if value is already filled in
    if (evt.target.value.length > 0) {
      delete error[evt.target.name];
    }
  };

  //Triggers on change of role
  const onRoleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value: number = +e.currentTarget.value;
    setAssignSuperManager((prevState: AssignSuperManagerModel) => ({ ...prevState, role: value }));
  }

  return (
    <div className={classNames(classes.formWrapper)}>
      <div className="row mb-3 mt-3">
        <div className="col-xs-12 col-md-12">
          <label>{t("UserManagement.Role")}</label>
          <RadioButtonGroup
            id="role"
            name="role"
            color="primary"
            options={[
              new MultiSelectModel(UserRoleEnum.SuperManager, t("UserManagement.Supermanager")),
              new MultiSelectModel(UserRoleEnum.WHOAdmin, t("UserManagement.WhoAdmin")),
            ]}
            value={assignSuperManager.role}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
              onRoleChange(e)
            }
            error={error["role"] ? true : false}
            helperText={error["role"]}
          />
        </div>
      </div>
      <div className="row mb-3 mt-3">
        <div className="col-xs-12 col-md-6">
          <Textbox
            id="email"
            label={t("UserManagement.Email")}
            name="email"
            fullWidth
            required
            value={assignSuperManager.email}
            error={error["email"] ? true : false}
            helperText={error["email"]}
            onChange={onUpdateChange}
            className="inputfocus"
            maxLength={256}
          />
        </div>
        <div className="col-xs-12 col-md-6">
          <Textbox
            id="name"
            label={t("UserManagement.Name")}
            name="name"
            fullWidth
            required
            value={assignSuperManager.name}
            error={error["name"] ? true : false}
            helperText={error["name"]}
            onChange={onUpdateChange}
            className="inputfocus"
            maxLength={100}
          />
        </div>
      </div>
      <div className="row mb-3">
        <div className="col-xs-12 col-md-6">
          <Textbox
            id="organizationName"
            name="organizationName"
            label={t("UserManagement.OrganizationName")}
            fullWidth
            required
            value={assignSuperManager.organizationName}
            error={error["organizationName"] ? true : false}
            helperText={error["organizationName"]}
            onChange={onUpdateChange}
            className="inputfocus"
            maxLength={100}
            disabled={true}
          />
        </div>
        <div className="col-xs-12 col-md-6">
          {
            assignSuperManager.role === UserRoleEnum.WHOAdmin
              ? t("UserManagement.AllCountriesToWHOAdmin")
              :
              <Dropdown
                id="countryId"
                name="countryId"
                label={t("UserManagement.ChooseCountry")}
                fullWidth
                required
                value={assignSuperManager.countryId}
                error={error["countryId"] ? true : false}
                helperText={error["countryId"]}
                options={countries}
                onChange={onUpdateChange}
                className="inputfocus"
              />
          }
        </div>
      </div>

      <ModalFooter>
        <>
          <button
            className="btn app-btn-secondary"
            onClick={() => onDialogClose(DialogAction.Close)}
          >
            {t("Common.Cancel")}
          </button>
          <button className="btn app-btn-primary" onClick={onAssign}>
            {t("Common.Assign")}
          </button>
        </>
      </ModalFooter>
    </div>
  );
};

export default AssginUser;
