﻿import React, { MutableRefObject } from "react";
import { Button, IconButton, Link } from "@mui/material";
import CloudUploadIcon from "@mui/icons-material/CloudUpload";

import classes from "./FileUploader.module.scss";
import { useTranslation } from "react-i18next";

type FileUploaderProps = {
  id: string;
  accept?: string;
  multiple?: boolean;
  children?: React.ReactElement;
  linkCss?: string;
  label?: string;
  onChange?: (evt: React.ChangeEvent<HTMLInputElement>) => void;
};

/** Renders the File uploader control */
const FileUploader = (props: FileUploaderProps) => {
  const { id, accept, multiple, children, linkCss, label, onChange } = props;

  const { t } = useTranslation();

  return (
    <>
      <input
        id={id}
        style={{ display: "none" }}
        type="file"
        accept={accept || ""}
        multiple={multiple || false}
        onChange={onChange || undefined}
      />

      <label htmlFor={id}>
        <Link
          className={`${classes.uploadLink} ${linkCss || ""}`}
          color="primary"
        >
          <CloudUploadIcon /> {label || t("Common.Upload")}
        </Link>
      </label>

      <>{children && children}</>
    </>
  );
};

export default FileUploader;
