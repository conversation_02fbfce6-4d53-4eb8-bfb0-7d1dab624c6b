﻿import React, { useEffect, useState } from "react";
import Chip from "@mui/material/Chip";
import TextField from "@mui/material/TextField";
import Autocomplete, {
  AutocompleteGetTagProps,
} from "@mui/material/Autocomplete";

import MultiSelectModel from "../../models/MultiSelectModel";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import { IconButton } from "@mui/material";

interface IMultiSelectProps {
  id: string;
  label: string;
  placeholder?: string;
  error?: boolean;
  helperText?: string;
  disabled?: boolean;
  required?: boolean;
  /** list of all options */
  options: Array<MultiSelectModel>;

  /** pre-selected values */
  values: Array<MultiSelectModel>;

  /**An event which updates parent about addition or removal of chips from the list */
  onUpdate: (selectedOptions: Array<MultiSelectModel>) => void;
}

/** Renders the multi-select dropdown */
const MultiSelect = (props: IMultiSelectProps) => {
  const {
    id,
    label,
    values,
    options,
    placeholder,
    error,
    helperText,
    disabled,
    onUpdate,
    required,
  } = props;

  const [selectedValues, setSelectedValues] = useState<Array<MultiSelectModel>>(
    []
  );

  // since the selected values are either int[] or string[] we need to cast them back to objects

  const fixedValues: Array<MultiSelectModel> = values;
  useEffect(() => {
    setSelectedValues(values);
  }, [values]);

  // triggers when user selected new item from the list
  const onChange = (
    event: React.ChangeEvent<{}>,
    newValues: Array<MultiSelectModel>
  ) => {
    let updatedValues: Array<MultiSelectModel> = [];

    if (newValues.length <= selectedValues.length) {
      updatedValues = newValues;
    } else {
      updatedValues = [
        ...fixedValues,
        ...newValues.filter((option) => values.indexOf(option) === -1),
      ];
    }

    setSelectedValues(updatedValues);

    // update parent
    onUpdate(updatedValues);
  };

  // renders the chips inside dropdown
  const renderTags = (
    tagValue: Array<MultiSelectModel>,
    getTagProps: AutocompleteGetTagProps
  ) =>
    tagValue.map((option, index) => (
      <Chip
        label={option.text}
        {...getTagProps({ index })}
        disabled={!option.canRemoveChip}
        className="chip-wrap"
      />
    ));

  return (
    <Autocomplete
      multiple
      id={id}
      disabled={disabled || false}
      value={fixedValues}
      onChange={onChange}
      options={options}
      getOptionLabel={(option) => option.text}
      renderTags={renderTags}
      popupIcon={
        <IconButton size="small">
          <ExpandMoreIcon />
        </IconButton>
      }
      renderInput={(params) => (
        <TextField
          {...params}
          label={label}
          variant="outlined"
          helperText={helperText || ""}
          error={error || false}
          placeholder={placeholder || ""}
          className="inputfocus multi-select"
          InputLabelProps={{ required: required }}
        />
      )}
    />
  );
};

export default MultiSelect;
