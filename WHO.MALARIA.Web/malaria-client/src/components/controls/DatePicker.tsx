﻿import React from "react";
import { DatePicker as MuiDatePicker } from "@mui/x-date-pickers/DatePicker";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { TextField } from "@mui/material";
import frLocale from "date-fns/locale/fr";
import enLocale from "date-fns/locale/en-US";

import { Constants } from "../../models/Constants";
import { UtilityHelper } from "../../utils/UtilityHelper";

interface DatePickerProps {
  value?: Date | null;
  onChange?: (date: Date | null, value?: string | null) => void;
  format?: string;
  error?: boolean;
  helperText?: string;
  label?: string;
  disabled?: boolean;
  className?: string;
  disablePast?: boolean;
  minDate?: Date | null;
  maxDate?: Date | null;
  openTo?: "year" | "month" | "day";
  views?: Array<"year" | "month" | "day">;
  placeholder?: string;
  onKeyDown?: (event: React.KeyboardEvent) => void;
  InputLabelProps?: any;
  size?: "small" | "medium";
  disableToolbar?: boolean;
  inputVariant?: string;
}

/** Render Date control */
const DatePicker = (props: DatePickerProps) => {
  const {
    format,
    error,
    helperText,
    onChange,
    disablePast,
    minDate,
    maxDate,
    openTo,
    views,
    placeholder,
    onKeyDown,
    InputLabelProps,
    size,
    disableToolbar,
    inputVariant,
    ...otherProps
  } = props;

  const currentlanguage = UtilityHelper.getCookieValue("i18next");

  let localeLanguage: any;
  if (currentlanguage === "fr") localeLanguage = frLocale;
  else if (currentlanguage === "en") {
    localeLanguage = enLocale;
  }

  // Handle the onChange to maintain backward compatibility
  const handleDateChange = (date: Date | null) => {
    if (onChange) {
      // Format the date as string for backward compatibility
      const dateString = date ? date.toISOString() : null;
      onChange(date, dateString);
    }
  };

  return (
    <LocalizationProvider
      dateAdapter={AdapterDateFns}
      adapterLocale={localeLanguage}
    >
      <MuiDatePicker
        format={format || Constants.Common.DefaultDateFormat}
        disablePast={disablePast}
        minDate={minDate}
        maxDate={maxDate}
        openTo={openTo}
        views={views}
        onChange={handleDateChange}
        {...otherProps}
        slots={{
          textField: TextField,
        }}
        slotProps={{
          textField: {
            error: error,
            helperText: helperText,
            variant: inputVariant || "outlined",
            margin: "normal",
            className: "form-control inputfocus",
            placeholder: placeholder,
            onKeyDown: onKeyDown,
            InputLabelProps: InputLabelProps,
            size: size,
          },
        }}
      />
    </LocalizationProvider>
  );
};

export default DatePicker;
