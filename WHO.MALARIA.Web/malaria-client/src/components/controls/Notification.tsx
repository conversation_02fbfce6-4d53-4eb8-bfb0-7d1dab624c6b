﻿import { Snackbar, SnackbarProps } from "@mui/material";
import Mui<PERSON>lert, { AlertProps } from "@mui/material/Alert";

import { BaseMessageModel, ErrorModel } from "../../models/ErrorModel";

type NotificationProps = {
  notification: BaseMessageModel | ErrorModel;
  onCancel: () => void;
};

function Alert(props: AlertProps) {
  return <MuiAlert elevation={6} variant="filled" {...props} />;
}

/** Renders the toastr notification bar */
const Notification = (props: SnackbarProps & NotificationProps) => {
  const { notification, open, onCancel } = props;

  return (
    <Snackbar
      open={open}
      autoHideDuration={5000}
      anchorOrigin={{ vertical: "top", horizontal: "center" }}
      onClose={onCancel}
      {...props}
    >
      <Alert
        onClose={onCancel}
        severity={notification?.isSuccessful ? "success" : "error"}
      >
        {notification?.text || "Unknown error occured."}
      </Alert>
    </Snackbar>
  );
};

export default Notification;
