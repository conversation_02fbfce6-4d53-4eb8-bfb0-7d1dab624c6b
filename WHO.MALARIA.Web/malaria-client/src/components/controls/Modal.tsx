﻿import React, { ReactChild, ReactElement, useEffect, useState } from "react";
import CancelIcon from "@mui/icons-material/Close";
import { Fade, IconButton } from "@mui/material";
import FullscreenIcon from "@mui/icons-material/Fullscreen";
import FullscreenExitIcon from "@mui/icons-material/FullscreenExit";

import { DialogAction } from "../../models/Enums";

interface IModal {
  /** Parent modal wrapper className */
  modalClassName?: string;

  /** Dialog header */
  title?: string;

  /** An another component  */
  children: ReactChild;
  /** used to open/close the dialog */
  open: boolean;

  /** close dialog on esc key*/
  onEscPress: boolean;
  /** used to show/hide fullscreen icon */
  showFullScreenIcon?: boolean;

  // open the dialog in full screen mode
  fullscreen?: boolean;

  /** Used to render the customized  dialog header */
  renderHeader?: React.ReactElement;

  // events
  /** Triggers when user clicks close button */
  onDialogClose?: (action?: DialogAction) => void;

  /** used to show/hide background for modal popup */
  modalBgClassName?: boolean;
}

/** Renders the MalariaDialog  */
function Modal(props: IModal) {
  const {
    children,
    modalClassName,
    title,
    onDialogClose,
    open,
    showFullScreenIcon,
    renderHeader,
    fullscreen,
    onEscPress,
    modalBgClassName,
  } = props;
  const [isFullScreen, setIsFullScreen] = useState<boolean>(false);
  //const [isModalBg, setIsModalBg] = useState<boolean>(false);

  useEffect(() => {
    if (open) {
      document.body.classList.add("app-modal-open");
    } else {
      document.body.classList.remove("app-modal-open");
    }
  }, [open]);

  // Triggers when user requested for full screen and vice versa
  const onFullScreenClick = () => {
    setIsFullScreen((prevState: boolean) => {
      {
        return !prevState;
      }
    });
  };

  // Renders the Cancel Icon
  const Cancel = (): ReactElement =>
    onDialogClose ? (
      <IconButton
        onClick={() => onDialogClose && onDialogClose(DialogAction.Close)}
        title="Close"
        className="close-modal"
      >
        <CancelIcon />
      </IconButton>
    ) : (
      <></>
    );

  // renders full screen and collapse icon
  const FullScreenIcon = (): ReactElement =>
    isFullScreen ? (
      <IconButton
        onClick={onFullScreenClick}
        title={!isFullScreen ? "Full Screen" : "Exit Full Screen"}
      >
        {!isFullScreen ? <FullscreenIcon /> : <FullscreenExitIcon />}
      </IconButton>
    ) : (
      <></>
    );

  return open ? (
    <Fade in={open} timeout={{ enter: 300, exit: 300 }}>
      <div
        id="modal-wrapper"
        className={`app-modal app-modal-show  ${
          modalClassName || "app-modal-sm"
        } ${isFullScreen || fullscreen ? "full-screen" : ""} ${
          modalBgClassName ? "app-modal-bg" : ""
        }`}
        onKeyUp={(e: React.KeyboardEvent) => {
          if (onEscPress) {
            // close dialog on esc key
            if (e.keyCode === 27) {
              onDialogClose && onDialogClose(DialogAction.Close);
            }
          }
        }}
      >
        <div className={`app-modal-dialog `} role="document">
          <div className="app-modal-content">
            <div className="app-modal-header d-flex">
              {
                // if customized header then render header else render dialog title
                renderHeader || (
                  <h5 className="app-modal-header-text">{title || ""}</h5>
                )
              }
              <div className="ml-auto">
                <FullScreenIcon />
                <Cancel />
              </div>
            </div>

            <div className="app-modal-body">{children}</div>
          </div>
        </div>
      </div>
    </Fade>
  ) : (
    <></>
  );
}

export default Modal;
