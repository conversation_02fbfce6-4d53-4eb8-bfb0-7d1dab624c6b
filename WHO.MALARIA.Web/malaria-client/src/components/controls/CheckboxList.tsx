import React, { useEffect, useState } from "react";
import List from "@mui/material/List";
import ListItem, { ListItemTypeMap } from "@mui/material/ListItem";
import ListItemIcon from "@mui/material/ListItemIcon";
import ListItemText from "@mui/material/ListItemText";
import Checkbox from "@mui/material/Checkbox";
import MultiSelectModel from "../../models/MultiSelectModel";
import { OverridableComponent } from "@mui/material/OverridableComponent";
import { ExtendButtonBase } from "@mui/material";

type ListItemProps = OverridableComponent<
  ListItemTypeMap<{ button?: false }, "li">
> &
  ExtendButtonBase<ListItemTypeMap<{ button: true }, "div">>;

interface ICheckBoxList {
  options: Array<MultiSelectModel>;
  selectedItems: Array<string | number>;

  onClick?: (values: Array<string | number>) => void;
}

type CheckBoxListProps = ICheckBoxList;

/** Renders CheckBoxList items control
 * @param props CheckBoxListProps
 */
const CheckboxList = (props: CheckBoxListProps) => {
  const { options, selectedItems, onClick } = props;
  const [checked, setChecked] = useState<Array<string | number>>([]);

  useEffect(() => {
    setChecked(selectedItems);
  }, [selectedItems]);

  //Triggers whenever List Item value changes
  const onListItemChecked = (value: string | number) => {
    const currentIndex = selectedItems.indexOf(value);
    const newChecked = [...checked];

    if (currentIndex === -1) {
      if (value === "allServices") {
        newChecked.splice(0, newChecked.length);
        newChecked.push(
          "allServices",
          "consultation",
          "diagnosis",
          "treatment",
          "hospitalisation"
        );
      } else {
        newChecked.push(value);
      }
    } else {
      if (value === "allServices") {
        newChecked.splice(0, newChecked.length);
      } else {
        newChecked.splice(currentIndex, 1);
        if (newChecked.includes("allServices")) {
          newChecked.splice(newChecked.indexOf("allServices"), 1);
        }
      }
    }

    setChecked(newChecked);

    onClick && onClick(newChecked);
  };

  return (
    <List>
      {options.map((option: MultiSelectModel) => {
        const labelId = `checkbox-list-label-${option.id}`;

        return (
          <ListItem
            key={labelId}
            role={undefined}
            dense
            button
            onClick={() =>
              option.disabled === false ? onListItemChecked(option.id) : ""
            }
            className="col-checkbox-control"
          >
            <ListItemIcon>
              <Checkbox
                key={labelId}
                edge="start"
                checked={checked.indexOf(option.id) !== -1}
                tabIndex={-1}
                className="checkbox-control"
                disableRipple
                inputProps={{ "aria-labelledby": labelId }}
                disabled={option.disabled}
              />
            </ListItemIcon>
            <ListItemText id={labelId} primary={option.text} />
          </ListItem>
        );
      })}
    </List>
  );
};

export default CheckboxList;
