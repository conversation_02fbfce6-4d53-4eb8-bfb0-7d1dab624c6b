﻿import React, { useEffect, useState } from "react";
import Text<PERSON>ield from "@mui/material/TextField";
import Autocomplete from "@mui/material/Autocomplete";
import MultiSelectModel from "../../models/MultiSelectModel";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";

interface IAutoCompleteProps {
  id: string;
  label: string;
  placeholder?: string;
  error?: boolean;
  helperText?: string;
  disabled?: boolean;
  required?: boolean;
  /** list of all options */
  options: Array<MultiSelectModel>;

  /** pre-selected values */
  values: MultiSelectModel;

  /**An event which updates parent about addition or removal of chips from the list */
  onUpdate: (selectedOptions: MultiSelectModel | null) => void;
}

/** Renders the auto-complete dropdown */
const AutoComplete = (props: IAutoCompleteProps) => {
  const {
    id,
    label,
    values,
    options,
    placeholder,
    error,
    helperText,
    disabled,
    onUpdate,
    required,
  } = props;

  const [selectedValues, setSelectedValues] = useState<MultiSelectModel | null>(
    MultiSelectModel.initModel()
  );

  useEffect(() => {
    setSelectedValues(values);
  }, [values]);

  // triggers when user selected new item from the list
  const onChange = (
    evt: React.ChangeEvent<{}>,
    newValues: MultiSelectModel | null
  ) => {
    setSelectedValues(newValues);

    //update parent
    onUpdate(newValues);
  };

  return (
    <Autocomplete
      multiple={false}
      id={id}
      disabled={disabled || false}
      value={selectedValues}
      onChange={onChange}
      options={options}
      getOptionLabel={(option) => option.text}
      popupIcon={<ExpandMoreIcon />}
      renderInput={(params) => (
        <TextField
          {...params}
          label={label}
          variant="outlined"
          helperText={helperText || ""}
          error={error || false}
          placeholder={placeholder || ""}
          className="inputfocus multi-select"
          InputLabelProps={{ required: required, shrink: true }}
        />
      )}
    />
  );
};

export default AutoComplete;
