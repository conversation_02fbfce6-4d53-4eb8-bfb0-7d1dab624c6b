﻿import { I<PERSON><PERSON>utton } from "@mui/material";
import SadIcon from "@mui/icons-material/SentimentVeryDissatisfied";
import CancelIcon from "@mui/icons-material/Cancel";
import { useTranslation } from "react-i18next";
import classes from "./FileUploader.module.scss";

type FileProps = {
    files: FileList;
    showPreviewText?: boolean;
    onDelete: (file: File) => void;
};

/**Renders the preview */
const FilePreview = (props: FileProps) => {
    const { t } = useTranslation();
    const { files, showPreviewText, onDelete } = props;

    if ((!files || files.length === 0) && showPreviewText) {
        return (
            <section className={classes.preview}>
                <span className="d-flex justify-content-center align-items-center"><SadIcon className="mx-2" /> {t("Common.NoFilesToPreview")} <br />{t("Common.PleaseUploadFiles")}</span>
            </section>
        );
    }

    return (
        <div className={classes.preview}>
            {Array.from(files).map((file: File, index: number) => (
                <div className="row align-items-center" key={`img_detail_${index}`}>
                    <div className="col-md-3">
                        <img
                            className={`rounded float-start ${classes.snapshot}`}
                            src={URL.createObjectURL(file)}
                            alt={file.name}
                        />
                    </div>
                    <div className="col-md-6">
                        <span className="align-middle float-start">{file.name}</span>
                    </div>
                    <div className="col-md-2">
                        <span className="align-middle float-start">
                            <IconButton onClick={() => onDelete(file)}>
                                <CancelIcon />
                            </IconButton>
                        </span>
                    </div>
                </div>
            ))}
        </div>
    );
};

export default FilePreview;