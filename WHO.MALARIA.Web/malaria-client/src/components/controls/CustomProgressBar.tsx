import React from "react";

/** Renders Progress bar */
function CustomProgressBar(props: any) {
    const { bgcolor, completed, name, titleSize, textColor } = props;

    const containerStyles = {
        height: 20,
        width: '100%',
        backgroundColor: "#fff",
        borderRadius: 50,
        border: "1px solid #E6E6E6"
    }

    return (
        <div>
            <label className="custom-progress-bar-title mb-2" style={{ fontSize: titleSize, color: textColor }}> {name} </label>
            <div style={containerStyles}>
                <div style={{ height: '100%', width: `${completed == 0 ? 1 : completed}%`, backgroundColor: bgcolor, borderRadius: 'inherit', textAlign: 'right' }} title={completed}>
                </div>
            </div>
        </div>

    );
};

export default CustomProgressBar;