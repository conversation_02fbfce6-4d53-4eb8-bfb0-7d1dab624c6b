import classNames from "classnames";
import React from "react";
import DoneIcon from "@mui/icons-material/Done";
import classes from "./tabs.module.scss";

export type WHOTab = {
  label: string;
  shortLabel?: string;
  id: string | number;
  icon?: React.ReactElement;
  disabled?: boolean;
};

type TabProps = {
  tabs: Array<WHOTab>;
  activeTabIndex: number;
  completedTabIndex?: Array<number>;
  className?: string;
  children: React.ReactElement;
  onClick?: (currentIndex: number) => void;
};

/** Renders tab control */
function Tabs(props: TabProps) {
  const {
    tabs,
    activeTabIndex,
    className,
    children,
    completedTabIndex,
    onClick,
  } = props;

  return (
    <>
      <ul className={classNames("nav", className)}>
        {tabs.map(({ id, label, icon, disabled }, index: number) => (
          <li key={`tabs_${id}`} className={classNames("nav-item")}>
            <span
              className={classNames(classes.label, {
                [classes.selected]: activeTabIndex === index,
                [classes.disabled]: disabled === true,
                [classes.completedStep]:
                  index !== activeTabIndex &&
                  completedTabIndex?.includes(index),
              })}
              aria-current="page"
              onClick={() => onClick && onClick(index)}
            >
              {icon && icon}
              {label}
              {completedTabIndex?.includes(index) && (
                <span className={classes.completed}>
                  <DoneIcon />
                </span>
              )}
            </span>
          </li>
        ))}
      </ul>
      {children}
    </>
  );
}

export default Tabs;
