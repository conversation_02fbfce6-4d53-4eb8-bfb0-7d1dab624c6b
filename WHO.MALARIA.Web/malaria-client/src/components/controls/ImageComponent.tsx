﻿import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

type ImageProps = {
    file: string;
    extension: string;
};

/** Image component which renders image */
const ImageComponent = (props: ImageProps) => {
    const { t } = useTranslation();
    const { file, extension } = props;
    const [imageSrc, setImageSrc] = useState("");

    useEffect(() => {
        if (file)
            setImageSrc(`data:image/${extension || "png"};base64,${file}`);
    }, []);

    return (
        <section className="page-diagram-section">
            <>
                <img key={`img-${Math.random()}`} src={imageSrc} className="img-fluid p-2" />
            </>
        </section>
    )
}
export default ImageComponent;