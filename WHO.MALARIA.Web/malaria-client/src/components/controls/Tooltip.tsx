import React, { ReactElement } from "react";
import parse from "html-react-parser";
import { Tooltip as MatTooltip, TooltipProps } from "@mui/material";

type TooltipProp = {
  isHtml?: boolean;
  children: ReactElement;
  content: string;
  className?: string;
};

/** Renders Tooltip */
function Tooltip(props: TooltipProp) {
  const { children, isHtml, content, className } = props;

  return (
    <MatTooltip
          className={className || ""}
          title={isHtml ? parse(content) : content}
          arrow={true}
    >
      {children}
    </MatTooltip>
  );
}

export default Tooltip;
