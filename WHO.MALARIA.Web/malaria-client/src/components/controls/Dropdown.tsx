﻿import React from "react";
import TextField, { TextFieldProps } from "@mui/material/TextField";
import MultiSelectModel from "../../models/MultiSelectModel";
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import { IconButton } from "@mui/material";
import { useTranslation } from "react-i18next";

interface IDropdownProps {
    options: Array<MultiSelectModel>;
}

type DropdownProps = TextFieldProps & IDropdownProps;

/** Renders the dropdown */
const Dropdown = (props: DropdownProps) => {
    const { options } = props;
    const { t } = useTranslation();

    return (
        <TextField
            select
            SelectProps={{
                native: true,
                IconComponent: () => <IconButton className="expand-icon"><ExpandMoreIcon /></IconButton>,
            }}
            variant="outlined"
            InputLabelProps={{ shrink: true }}
            {...props}
            className="form-control inputfocus"
        >
            <option value="">{t("Common.Select")}</option>
            {options?.map((option) => (
                <option key={option.id} value={option.id}>
                    {option.text}
                </option>
            ))}
        </TextField>
    );
};

export default Dropdown;
