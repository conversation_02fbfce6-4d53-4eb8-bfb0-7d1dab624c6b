import { useEffect } from "react";
import { useDispatch } from "react-redux";
import { setIsDirty } from "../../redux/ducks/form-change-tracker";

/**Custom hook to handle change events of HTML input elements and 
   setting the isDirty flag if any input element's value is changed.*/
function useFormChangeTracker() {
    const dispatch = useDispatch();

    //Handles change event of html input elemnt
    const onHtmlInputElementChange = () => {
        dispatch(setIsDirty(true));
        onRemoveEventListener();
    };

    //Add event listener on html input element change event
    const onAddEventListener = () => {
        window.addEventListener("change", onHtmlInputElementChange);
    };

    //Remove event listener on html input element change event
    const onRemoveEventListener = () => {
        window.removeEventListener("change", onHtmlInputElementChange);
    };

    //Set isDirty to false
    const setIsDirtyToFalse = () => {
        dispatch(setIsDirty(false));
    };

    //Add and Remove Event Listners on html elements
    useEffect(() => {
        //Add change Event Listener which added on html element
        onAddEventListener();

        //Remove change Event Listener which added on html element
        return () => {
            onRemoveEventListener();
        };
    }, []);

    return {
        setIsDirtyToFalse
    };
}

export default useFormChangeTracker;