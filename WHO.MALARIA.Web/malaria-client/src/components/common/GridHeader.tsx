import React from "react";

type HeaderProps = {
  onClick: () => void;
  buttonText: string;
  headerTitle: string;
  hide?: boolean;
};

/** Renders Grid headers  */
function GridHeader(props: HeaderProps) {
  const { buttonText, headerTitle, hide, onClick } = props;
  return (
    <div className="d-flex document-title-section">
      <h2 className="heading-title">{headerTitle}</h2>

      {hide && (
        <div className="button-action-section ml-auto">
          <div className="button-group">
            <button className="btn app-btn-primary ripple" onClick={onClick}>
              {buttonText}
            </button>
          </div>
        </div>
      )}
    </div>
  );
}

export default GridHeader;
