import React from "react";
import { useTranslation } from "react-i18next";
import { authService } from "../../services/authService";

/** Renders for in active user */
function InActivatedUser() {
    const { t } = useTranslation();
    document.title = t("app.InactivatedUser");

    // on back button click redirect user to landing screen
    const onNavigate = () => {
        authService.logout();
    };

    return (
        <div className="section-background-image">

            <div className="section-bg-content">

                <div className="content">{t("Errors.InActivatedUserMessage")}</div>

                <button className={"btn app-btn-secondary"} onClick={onNavigate}>
                    {t("Common.GoBack")}
                </button>
            </div>
        </div>
    );
}

export default InActivatedUser;