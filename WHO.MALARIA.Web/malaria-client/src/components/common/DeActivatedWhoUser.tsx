import React, { useEffect } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import { authService } from "../../services/authService";

/** Renders for deactivated who user */
function DeActivatedWhoUser() {
    const { t } = useTranslation();
    document.title = t("app.DeactivatedWHOUser");
    const navigate = useNavigate();

    useEffect(() => {
        navigate("/deactivatedwhouser")
    }, []);

    // on back button click redirect user to landing screen
    const onNavigate = () => {
        authService.logout();
    };

    return (
        <div className="section-background-image">

            <div className="section-bg-content">

                <div className="content">{t("Errors.DeactivatedUserMessage")}</div>

                <button className={"btn app-btn-secondary"} onClick={onNavigate}>
                    {t("Common.GoBack")}
                </button>
            </div>
        </div>
    );
}

export default DeActivatedWhoUser;