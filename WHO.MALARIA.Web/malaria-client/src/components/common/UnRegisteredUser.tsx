import React, { useEffect } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import { authService } from "../../services/authService";

/** Renders for unregistered user */
function UnRegisteredUser() {
    const { t } = useTranslation();
    document.title = t("app.UnregisteredUser");
    const navigate = useNavigate();

    useEffect(() => {
        navigate("/unregistereduser")
    }, []);

    // on back button click redirect user to landing screen
    const onNavigate = () => {
        authService.logout();
    };

    return (
        <div className="section-background-image">

            <div className="section-bg-content">

                <div className="content">{t("Errors.UnregisteredUserMessage")}</div>

                <button className={"btn app-btn-secondary"} onClick={onNavigate}>
                    {t("Common.GoBack")}
                </button>
            </div>
        </div>
    );
}

export default UnRegisteredUser;