import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import InactiveUserProfile from "../common/InactiveUserProfile";
import Modal from "../controls/Modal";
import { authService } from "../../services/authService";

/** Renders for deactivated user */
function DeactivatedUser() {
    const { t } = useTranslation();
    document.title = t("app.DeactivatedUser");
    const [openNewUserDialog, setOpenNewUserDialog] = useState<boolean>(false);

    // on back button click redirect user to landing screen
    const onNavigate = () => {
        authService.logout();
    };

    return (
        <div className="section-background-image">

            <div className="section-bg-content">

                <div className="content">{t("Errors.UserProfileAccessCountryMessage")}</div>

                <div className="button-group">
                    <button className={"btn app-btn-secondary"} onClick={() => setOpenNewUserDialog(true)}>
                        {t("Common.Yes")}
                    </button>
                    <button className={"btn app-btn-secondary mx-2"} onClick={onNavigate}>
                        {t("Common.No")}
                    </button>
                </div>
                {
                    <Modal
                        open={openNewUserDialog}
                        title={t("translation:UserManagement.Profile")}
                        onEscPress={false}
                        onDialogClose={() => setOpenNewUserDialog(false)}
                    >
                        <InactiveUserProfile onCancel={() => setOpenNewUserDialog(false)} />
                    </Modal>
                }
            </div>
        </div>
    );
}

export default DeactivatedUser;
