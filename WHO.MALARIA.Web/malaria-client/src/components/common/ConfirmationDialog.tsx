import { Button, Dialog } from "@mui/material";
import React from "react";
import { useTranslation } from "react-i18next";
import { DialogAction } from "../../models/Enums";
import Modal from "../controls/Modal";
import ModalFooter from "../controls/ModalFooter";

type ConfirmationDialogProps = {
  open: boolean;
  title: string;
  content: string;
  onClick: (action: DialogAction) => void;
};

/** Renders the confirmation dialog */
function ConfirmationDialog(props: ConfirmationDialogProps) {
  const { t } = useTranslation();
  const { open, title, content, onClick } = props;
  return (
    <Modal
      open={open}
      title={title}
      onEscPress={false}
      onDialogClose={() => onClick(DialogAction.Close)}
    >
      <>
        {content}
        <ModalFooter>
          <>
            <Button
              className="btn app-btn-secondary"
              onClick={() => onClick(DialogAction.Close)}
            >
              {t("Common.No")}
            </Button>
            <Button
              className="btn app-btn-primary"
              onClick={() => onClick(DialogAction.Add)}
            >
              {t("Common.Yes")}
            </Button>
          </>
        </ModalFooter>
      </>
    </Modal>
  );
}

export default ConfirmationDialog;
