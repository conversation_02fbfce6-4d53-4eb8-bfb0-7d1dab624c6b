import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate,  useLocation } from "react-router-dom";
import { DialogAction } from "../../models/Enums";
import { showUserGuide } from "../../redux/ducks/user-guide-drawer";
import ConfirmationDialog from "./ConfirmationDialog";

/**Custom hook to handle confirmation dialog onBack button click */
function useConfirmationDialogOnBackButtonClick(redirectUrl: string) {
    const isFormDirty: boolean = useSelector((state: any) => state.formChangeTracker.isDirty);
    const navigate = useNavigate();
    const { t } = useTranslation();
    const location: any = useLocation();
    const dispatch = useDispatch();
    const [showConfirmationDialog, setShowConfirmationDialog] = useState<boolean>(false);

    //Redirects to the given URL in the hook's parameter along with the existing location state
    function RedirectTo() {
        const state = location?.state;
        navigate(redirectUrl, { state: {
            ...state
        } });
    }

    //Handles confirmation dialog box actions
    const onConfirmationButtonClick = (action: DialogAction) => {
        switch (action) {
            case DialogAction.Add:
                setShowConfirmationDialog(false);
                break;
            case DialogAction.Close:
                setShowConfirmationDialog(false);
                RedirectTo();
                break;
        }
    }

    //Handles back navigation click event
    const onBackButtonClick = () => {
        if (isFormDirty) {
            setShowConfirmationDialog(true);
        }
        else {
            setShowConfirmationDialog(false);
            RedirectTo();
        }

        dispatch(showUserGuide(true));
    };

    return {
        onBackButtonClick,
        ConfirmationDialog: <ConfirmationDialog
            title={t("Common.ConfirmationTitle")}
            content={t("Common.ConfirmationToGoBack")}
            open={showConfirmationDialog}
            onClick={onConfirmationButtonClick}
        />
    };
}

export default useConfirmationDialogOnBackButtonClick;