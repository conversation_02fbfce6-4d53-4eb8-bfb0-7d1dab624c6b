// import { Date | null } from "@material-ui/pickers/typings/date";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { AnalyticsDataModel } from "../../models/AnalyticsDataModel";
import { AnalyticsResponseDataModel } from "../../models/AnalyticsResponseDataModel";
import { analyticsService } from "../../services/analyticsService";
import DatePicker from "../controls/DatePicker";
import { UtilityHelper } from "../../utils/UtilityHelper";
import {
  Grid,
  GridColumn as Column,
  GridDataStateChangeEvent,
  GridCellProps,
} from "@progress/kendo-react-grid";
import { process, State } from "@progress/kendo-data-query";

//Intial state for pagination and filter
const initialDataState: State = {
  sort: [{ field: "code", dir: "asc" }],
  take: 10,
  skip: 0,
};

/** Renders to show analytics data between selected start date and end date*/
function Analytics() {
  const { t } = useTranslation();

  document.title = t("app.GoogleAnalytics");

  const [dataState, setDataState] = useState<State>(initialDataState);

  const [analyticsResponseData, setAnalyticsResponseData] = useState<
    Array<AnalyticsResponseDataModel>
  >([]);

  const [analyticsData, setAnalyticsData] = useState<AnalyticsDataModel>(
    AnalyticsDataModel.init()
  );

  useEffect(() => {
    LoadAnalyticalData();
  }, []);

  // load analytical data
  const LoadAnalyticalData = () => {
    const analyticsDataModel = new AnalyticsDataModel(
      analyticsData.startDate,
      analyticsData.endDate
    );
    analyticsService
      .getAnalyticsData(analyticsDataModel)
      .then((analyticsResponseData: Array<AnalyticsResponseDataModel>) => {
        setAnalyticsResponseData(analyticsResponseData);
      });
  };

  // Triggers whenever user tries to modify the start date and end date
  const onDateChange = (date: Date | null, field: string) => {
    setAnalyticsData({
      ...analyticsData,
      [field]: date,
    });
  };

  // Triggers when click on show data
  const onClick = () => {
    LoadAnalyticalData();
  };

  return (
    <section className="page-full-section">
      <div className="container-fluid">
        <div className="d-flex document-title-section">
          <h2 className="heading-title">{t("Analytics.AnalyticsHeading")}</h2>
        </div>

        <div className="row my-3">
          <div className="col-md-4">
            <DatePicker
              value={analyticsData.startDate}
              label={t("Common.StartDate")}
              onChange={(date: Date | null) => onDateChange(date, "startDate")}
              InputLabelProps={{ required: true, shrink: true }}
              openTo="year"
              views={["year", "month", "day"]}
              size="small"
              placeholder={t("Common.YearMonthDate")}
            />
          </div>
          <div className="col-md-4">
            <DatePicker
              value={analyticsData.endDate}
              label={t("Common.EndDate")}
              onChange={(date: Date | null) => onDateChange(date, "endDate")}
              InputLabelProps={{ required: true, shrink: true }}
              openTo="year"
              views={["year", "month", "day"]}
              size="small"
              placeholder={t("Common.YearMonthDate")}
            />
          </div>
          <div className="col-md-4">
            <button
              className="btn app-btn-primary"
              type="submit"
              onClick={onClick}
            >
              {t("Common.ShowData")}
            </button>
          </div>
        </div>

        <Grid
          className="k-widget k-grid k-grid-wrapper k-grid-action-wrapper"
          pageable={true}
          sortable={true}
          filterable={true}
          style={{ height: "400px" }}
          data={process(analyticsResponseData, dataState)}
          {...dataState}
          onDataStateChange={(e: GridDataStateChangeEvent) => {
            setDataState(e.dataState);
          }}
        >
          <Column
            field="date"
            title={t("Analytics.Date")}
            filter="text"
            width="200px"
            filterable={true}
            cell={(props: GridCellProps) => (
              <td>
                <td>{UtilityHelper.formatDate(props.dataItem["date"])}</td>
              </td>
            )}
          />
          <Column
            field="eventAction"
            title={t("Analytics.EventAction")}
            filter="text"
            width="250px"
            filterable={true}
          />
          <Column
            field="eventCategory"
            title={t("Analytics.EventCategory")}
            filter="text"
            width="150px"
            filterable={true}
          />
          <Column
            field="userType"
            title={t("Analytics.UserType")}
            filter="text"
            width="150px"
            filterable={true}
          />
          <Column
            field="pageTitle"
            title={t("Analytics.PageTitle")}
            filter="text"
            width="150px"
            filterable={true}
          />
          <Column
            field="city"
            title={t("Analytics.City")}
            filter="text"
            width="150px"
            filterable={true}
          />
          <Column
            field="activeUsersCount"
            title={t("Analytics.ActiveUsersCount")}
            filter="text"
            width="150px"
            filterable={true}
          />
        </Grid>
      </div>
    </section>
  );
}
export default Analytics;
