﻿import React, { useState } from 'react'

import { authService } from '../../services/authService'
import { FeedbackModel } from '../../models/FeedbackModel';
import TextBox from '../controls/TextBox'
import { Button } from '@mui/material';

const Feedback = () => {
    const [feedback, setFeedback] = useState<FeedbackModel>(FeedbackModel.init());

    const currentUser = authService.getCurrentUser();

    return (
        <form>
             <div className="row">
            <div className="col-md-12">
                <TextBox
                    id="name"
                    name="name"
                    label="Name"
                    disabled
                    fullWidth
                    value={currentUser.name}
                />
            </div>
            <div className="col-md-12 mt-3">
            <TextBox
                    id="email"
                    name="email"
                    label="Email"
                    disabled
                    fullWidth
                    value={currentUser.email}
                />
            </div>
            <div className="col-md-12 mt-3">
                <TextBox 
                    id="feedback"
                    name="feedback"
                    label="Feedback"
                    fullWidth                    
                    value = {feedback.writeUs}
                    rows="5"
                    multiline
                />
            </div>
            <div className="col-md-12 mt-3">
                Attachment
            </div>
            <div className="col-md-12 mt-5">
                <div className="d-flex justify-content-center">
                    <Button color="secondary">Cancel</Button>
                    <Button color="primary" >Save</Button>
                </div>
            </div>
        </div>
        </form>
    )
}

export default Feedback
