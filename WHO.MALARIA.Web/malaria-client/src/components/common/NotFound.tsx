import React from "react";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";

/** Renders Page not found component */
function NotFound() {
  const { t } = useTranslation();
  const navigate = useNavigate();
  document.title = t("app.NotFoundTitle");

  const goBack = () => {
    navigate("/dashboard");
  };

  return (
    <div className="section-background-image">
      <div className="section-bg-content">
        <h1 className="heading-bg">{t("Errors.Oops")}!</h1>

        <div className="content">{t("Errors.PageNotFoundMessage")}</div>

        <button className={"btn app-btn-secondary"} onClick={goBack}>
          {t("Common.GoBack")}
        </button>
      </div>
    </div>
  );
}

export default NotFound;
