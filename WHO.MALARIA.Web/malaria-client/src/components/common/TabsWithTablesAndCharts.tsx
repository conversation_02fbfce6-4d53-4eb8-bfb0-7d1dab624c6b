﻿import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import WHOTabs from "../controls/WHOTabs";
import { TabModel } from "../../models/TabModel";
import TablesWithCharts from "../common/TablesWithCharts";
import { TabWithTablesChartsModel } from "../../models/TabWithTablesChartsModel";

type TabWithTablesChartsProps = {
    data: Array<TabWithTablesChartsModel>;
};

/** Component which renders table and charts with tabs */
const TabsWithTablesAndCharts = (props: TabWithTablesChartsProps) => {
    const { t } = useTranslation();
    const { data } = props;
    const [currentTab, setCurrentTab] = useState<number>(0);

    // Triggers whenever tab is changed
    const onTabChange = (event: React.ChangeEvent<{}>, tabIndex: number) => {
        setCurrentTab(tabIndex);
    };

    // Generate dynamic tab's component
    const renderTabComponent: Array<TabModel> = data?.map((tab: TabWithTablesChartsModel, index: number) => {
        return (new TabModel(
            tab.tabId,
            tab.tabName,
            <TablesWithCharts tables={data[index]?.tables} charts={data[index]?.charts} />
        ))
    });

    return (
        <>
            {data?.length &&
                <div className="app-tab-wrapper">
                    <WHOTabs
                        tabs={renderTabComponent}
                        value={currentTab}
                        onChange={onTabChange}
                        scrollable={false}
                    >
                        <div className="p-2">{renderTabComponent[currentTab].children}</div>
                    </WHOTabs>
                </div>
            }
        </>
    )
}

export default TabsWithTablesAndCharts;