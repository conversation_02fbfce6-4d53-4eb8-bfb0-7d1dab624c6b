﻿import React from "react";
import { useTranslation } from "react-i18next";
import { ChartModel } from "../../models/ChartModel";
import {
    Chart,
    ChartSeries,
    ChartSeriesItem,
    ChartCategoryAxis,
    ChartCategoryAxisItem,
    ChartTitle,
    ChartLegend,
    ChartValueAxis,
    ChartValueAxisItem,
} from "@progress/kendo-react-charts";

type ChartsProps = {
    charts: Array<ChartModel> | undefined;
};


/** Renders bar chart component */
const BarChartComponent = (props: ChartsProps) => {
    const { t } = useTranslation();
    const { charts } = props;

    // Sets the defaults colors for the columns 
    const chartDefaultV4Colors = [
        "#6FEDA1",
        "#FF9393",
        "#F2E56F",
        "#ADB2B9",
    ];

    const renderChartComponent = () => {
        const chartComponent = charts?.map((chart: ChartModel) => {
            return <div className="col-sm-12" key={chart.title}>
                {chart.values.length ?
                    <>
                        <Chart seriesColors={chartDefaultV4Colors} style={{
                            height: 750,
                        }}>
                            <ChartValueAxis>
                                <ChartValueAxisItem
                                    title={{
                                        text: chart.xAxis,
                                    }}
                                    min={0}
                                    max={100}
                                />
                            </ChartValueAxis>
                            <ChartLegend position="bottom" orientation="horizontal" visible={false} />
                            <ChartSeries>
                                {chart?.values?.map((item, idx) => (
                                    <ChartSeriesItem
                                        key={idx}
                                        type="column"
                                        tooltip={{
                                            visible: true,
                                        }}
                                        data={item.value}
                                        name={item.key}
                                        stack={true}
                                        colorField="#6FEDA1"
                                    />
                                ))}
                            </ChartSeries>

                            <ChartCategoryAxis>
                                <ChartCategoryAxisItem
                                    labels={{
                                        format: "d",
                                        rotation: -45,
                                    }}
                                    categories={chart.categories}
                                />
                            </ChartCategoryAxis>
                        </Chart>

                        <div className="d-flex justify-content-center py-4">
                            <ul className="dashboard-legend">
                                <li><span className="met"></span>{t("Common.Met")}​​​​​</li>
                                <li><span className="partially-met"></span>{t("Common.PartiallyMet")}​​​​​</li>
                                <li><span className="not-met"></span>{t("Common.NotMet")}​​​​​</li>
                                <li><span className="not-assessed"></span>{t("Common.NotAssessed")}​​​​​</li>
                            </ul>
                        </div>
                    </>
                    :
                    <div className="d-flex h-300 align-items-center justify-content-center">{t("Dashboard.RegionalDashboardNoData")}</div>
                }
            </div>
        })

        return chartComponent;
    }

    return (
        <>
            <div className="app-dashboard">
                <div className="row">
                    {renderChartComponent()}
                </div>
            </div>
        </>
    )
}

export default BarChartComponent;