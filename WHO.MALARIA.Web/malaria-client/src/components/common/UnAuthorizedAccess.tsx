import React from "react";
import Lock from "../../images/Lock.svg";
import { useTranslation } from "react-i18next";

/** Renders Aunthorized Access Screen */
function UnAuthorizedAccess() {
  const { t } = useTranslation();

  const goBack = () => {
    window.history.back();
  };

  return (
    <div className="section-background-image">
      <div className="section-bg-content">
        <h1>
          S<img src={Lock} className="lock-icon img-fluid" />
          rry!
        </h1>

        <div className="content">{t("Errors.UnAuthorizedAccessMessage")}.</div>

        <button className={"btn app-btn-secondary"} onClick={goBack}>
          {t("Common.GoBack")}
        </button>
      </div>
    </div>
  );
}

export default UnAuthorizedAccess;
