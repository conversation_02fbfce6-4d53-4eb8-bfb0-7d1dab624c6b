type UserMessageProps = {
  content?: string;
  className?: string;
  /** Renders the custom content */
  renderContent?: React.ReactChild;
};
/**Renders the No Record found component */
const UserMessage = (props: UserMessageProps) => {
  const { content, className, renderContent } = props;
  return (
    <div className={className}>
      {content && content}
      {renderContent && renderContent}
    </div>
  );
};

export default UserMessage;
