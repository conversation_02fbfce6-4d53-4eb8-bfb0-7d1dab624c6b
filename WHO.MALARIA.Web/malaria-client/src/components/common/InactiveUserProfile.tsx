﻿import React, { useEffect, useState } from "react";
import TextBox from "../controls/TextBox";
import ModalFooter from "../controls/ModalFooter";
import MultiSelect from "../controls/MultiSelect";
import MultiSelectModel from "../../models/MultiSelectModel";
import { useTranslation } from "react-i18next";
import { DeactivatedUserModel, IdAndName, UserCountryAssessRequestModel } from "../../models/DeactivatedUserModel";
import { userService } from "../../services/userService";
import { authService } from "../../services/authService";

interface IProfile {
    onCancel?: () => void;
}

/** Renders the user profile page to inactivate user and send countries access request */
const InactiveUserProfile = (props: IProfile) => {
    const { t } = useTranslation();
    const { onCancel } = props;
    const [countries, setCountries] = useState<Array<MultiSelectModel>>([]);
    const [selectedCountries, setSelectedCountries] = useState<Array<MultiSelectModel>>([]);

    const [userProfile, setUserProfile] = useState<DeactivatedUserModel>(DeactivatedUserModel.init());
    const [error, setError] = useState<any>({});

    useEffect(() => {
        getDeactivatedUserProfile();
    }, []);

    // retrieve all records from country entity by making an api call
    const getDeactivatedUserProfile = () => {
        userService.getDeactivatedUserProfile().then((profile: DeactivatedUserModel) => {
            setUserProfile(profile);
            const records: Array<MultiSelectModel> = profile.countries.map(
                (record: IdAndName): MultiSelectModel =>
                    new MultiSelectModel(record.id, record.name, false, true)
            );
            setCountries(records);
        });
    };

    // triggers whenever user add/removes items in multi-select dropdown
    const onMultiSelectChange = (
        field: string,
        selectedItems: Array<MultiSelectModel>
    ) => {
        setSelectedCountries(selectedItems);
        removeControlValidationOnChange(selectedItems, field);
    };

    // checks and remove validation for the control if satisfies the validate condition
    const removeControlValidationOnChange = (
        value: string | Array<MultiSelectModel>,
        field: string
    ) => {
        if (value?.length > 0) {
            const formError = { ...error };
            delete formError[field];
            setError({ ...formError });
        } else {
            setError({ ...error, [field]: t("Errors.MandatoryField") });
        }
    };

    // validate form
    const validate = () => {
        let error: any = {};

        if (selectedCountries.length === 0) {
            error = { ...error, ["accessRequestedCountryIds"]: t("Errors.MandatoryField") };
        }
        return error;
    }

    const isFormValid = () => Object.keys(validate()).length === 0;

    // triggers on save
    const onSubmit = (event: React.FormEvent<HTMLFormElement>) => {
        event.preventDefault();

        // validate
        if (!isFormValid()) {
            setError(validate());
            return;
        }

        const selectedCountryIds = selectedCountries.map((uca: MultiSelectModel) => uca.id);

        userService.addUserCountryAccessRequest(
            new UserCountryAssessRequestModel(
                selectedCountryIds
            )
        ).then((response: boolean) => {
            if (response) {
                setTimeout(function () {
                    authService.logout()
                }, 3000);
            }
        });

        onCancel && onCancel();
    };

    return (
        <form autoComplete="off" onSubmit={onSubmit}>
            <div className="row">
                <div className="row mt-3">
                    <div className="col-md-12">
                        <TextBox
                            id="name"
                            name="name"
                            value={userProfile?.userProfileDetail?.name}
                            label={t("UserManagement.GridColumn.Name")}
                            error={!!error["name"]}
                            helperText={error["name"]}
                            autoComplete="off"
                            fullWidth
                            className="inputfocus"
                            InputLabelProps={{ required: true, shrink: true }}
                            maxLength={256}
                            disabled={true}
                        />
                    </div>
                </div>

                <div className="row mt-3">
                    <div className="col-md-12">
                        <TextBox
                            id="email"
                            name="email"
                            label={t("UserManagement.GridColumn.Email")}
                            error={!!error["email"]}
                            helperText={error["email"]}
                            value={userProfile?.userProfileDetail?.email}
                            autoComplete="off"
                            fullWidth
                            className="inputfocus"
                            InputLabelProps={{ required: true, shrink: true }}
                            maxLength={256}
                            disabled={true}
                        />
                    </div>
                </div>

                <div className="row  mt-3">
                    <div className="col-md-12">
                        <TextBox
                            id="organizationName"
                            name="organizationName"
                            label={t("UserManagement.GridColumn.OrganizationName")}
                            error={!!error["organizationName"]}
                            helperText={error["organizationName"]}
                            value={userProfile?.userProfileDetail?.organizationName}
                            autoComplete="off"
                            fullWidth
                            className="inputfocus"
                            InputLabelProps={{ required: true, shrink: true }}
                            maxLength={256}
                            disabled={true}
                        />
                    </div>
                </div>

                <div className="row mt-3">
                    <div className="col-md-12">
                        <MultiSelect
                            id="accessRequestedCountryIds"
                            label={t("UserManagement.CountryAccessRequested")}
                            error={!!error["accessRequestedCountryIds"]}
                            helperText={error["accessRequestedCountryIds"]}
                            options={countries}
                            values={selectedCountries} 
                            placeholder={t("UserManagement.CountriesRequested")}
                            onUpdate={(selectedItems: Array<MultiSelectModel>) =>
                                onMultiSelectChange("selectedCountries", selectedItems)
                            }
                        />
                    </div>
                </div>

                <ModalFooter>
                    <button className="btn app-btn-primary" type="submit">
                        {t("Common.Request")}
                    </button>
                </ModalFooter>
            </div>
        </form>
    );
};

export default InactiveUserProfile;
