﻿import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import WHOTabs from "../controls/WHOTabs";
import { TabModel } from "../../models/TabModel";
import { TabWithChartsModel } from "../../models/ChartModel";
import BarChartComponent from "./BarChartComponent";

type TabWithTablesChartsProps = {
    data: Array<TabWithChartsModel>;
};

/** Component which renders charts with tabs */
const TabsWithCharts = (props: TabWithTablesChartsProps) => {
    const { t } = useTranslation();
    const { data } = props;
    const [currentTab, setCurrentTab] = useState<number>(0);

    // Triggers whenever tab is changed
    const onTabChange = (event: React.ChangeEvent<{}>, tabIndex: number) => {
        setCurrentTab(tabIndex);
    };

    // Generate dynamic tab's component
    const renderTabComponent: Array<TabModel> = data?.map((tab: TabWithChartsModel, index: number) => {
        return (new TabModel(
            tab.tabId,
            tab.tabName,
            <BarChartComponent charts={data[index]?.charts} key={tab.tabId} />
        ))
    });

    return (
        <>
            {data?.length &&
                <div className="app-tab-wrapper">
                    <WHOTabs
                        tabs={renderTabComponent}
                        value={currentTab}
                        onChange={onTabChange}
                        scrollable={false}
                    >
                        <div className="p-2"> {renderTabComponent[currentTab].children} </div>
                    </WHOTabs>
                </div>
            }
        </>
    )
}

export default TabsWithCharts;