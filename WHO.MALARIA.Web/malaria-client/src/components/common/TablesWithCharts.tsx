﻿import { GridCellProps, GridColumnProps } from "@progress/kendo-react-grid";
import React from "react";
import { useTranslation } from "react-i18next";
import { TableModel, Column } from "../../models/TableModel";
import { ChartModel } from "../../models/ChartModel";
import DataGrid from "../controls/DataGrid";
import {
    Chart,
    ChartSeries,
    ChartSeriesItem,
    ChartCategoryAxis,
    ChartCategoryAxisItem,
    ChartTitle,
    ChartLegend,
    ChartValueAxis,
    ChartValueAxisItem,
} from "@progress/kendo-react-charts";
import { Constants } from "../../models/Constants";
import i18next from "../../i18n";

type TablesWithChartsProps = {
    tables: Array<TableModel> | undefined;
    charts: Array<ChartModel> | undefined;
};

const DATA_ITEM_KEY = "id";
const SELECTED_FIELD = "selected";

/** Renders tabular and chart component */
const TablesWithCharts = (props: TablesWithChartsProps) => {
    const { t } = useTranslation();
    const { tables, charts } = props;

    // create column definition
    const getColDefs = (columns: Array<Column>): Array<GridColumnProps> => {
        let colDef: Array<GridColumnProps> = [];
        // get table column
        const rowFields = columns;

        rowFields?.forEach((row: Column, index: number) => {
            if (row.key === Constants.Common.iptp) {
                row.key = Constants.Common.iptpColumnfield;
            }

            if (row.key === Constants.Common.tpig) {
                row.key = Constants.Common.tpigColumnfield;
            }

            const healthFacilityTypeKey = t("translation:DataQualityAnalysis:HealthFacilityType");
            if (row.key.toLocaleLowerCase() === healthFacilityTypeKey.toLocaleLowerCase()) {
                row.key = Constants.Common.healthFacilityType;
            }

            const healthFacilityNameKey = t("translation:DataQualityAnalysis:HealthFacilityName");
            if (row.key.toLocaleLowerCase() === healthFacilityNameKey.toLocaleLowerCase()) {
                row.key = Constants.Common.healthFacilityName;
            }

            const percentageKey = t("translation:DataQualityAnalysis:Percentage");
            if (row.key.toLocaleLowerCase() === percentageKey.toLocaleLowerCase()) {
                row.key = Constants.Common.Percentage;
            }

            colDef.push({
                title: row.label,
                width: `${row.width}px`,
                filterable: false,
                locked: index === 0 ? true : false,
                cell: (props: GridCellProps) => (
                    <td title={props.dataItem[row.key]}
                        style={props.style} // this applies styles that lock the column at a specific position
                        className={props.className} // this adds classes needed for locked columns
                        colSpan={props.colSpan}
                        role={"gridcell"}
                        aria-colindex={props.ariaColumnIndex}
                        aria-selected={props.isSelected}
                    >
                        {props.dataItem[row.key]}
                    </td>
                ),
            })
        });

        return colDef;
    }

    const renderTableComponent = () => {
        const tableComponent = tables?.map((table: TableModel) => {
            return <div className={tables.length > 1 ? "col-sm-6" : "col-sm-12"}>
                <p><b>{table.title}</b></p>
                <DataGrid
                    className={`${tables ? 'k-grid k-grid-wrapper k-grid-action-wrapper' : 'k-grid k-grid-wrapper k-grid-action-wrapper k-desk-review'} `}
                    columns={getColDefs(table.columns)}
                    data={table.rows}
                    dataItemKey={DATA_ITEM_KEY}
                    selectedField={SELECTED_FIELD}
                    hasActionBtn={true}
                    filterable={false}
                    selectable={{
                        enabled: true,
                        drag: false,
                        cell: true,
                        mode: "single",
                    }}
                />
            </div>
        })

        return tableComponent;
    }

    const yAxisTitle = i18next.t("DataQualityAnalysis.Percentage") + ' %';
    const xAxisTitle = i18next.t("DataQualityAnalysis.Year");

    const renderGraphComponent = () => {
        const graphComponent = charts?.map((chart: ChartModel) => {
            return <div className={charts.length > 1 ? "col-sm-6" : "col-sm-12"}>
                <Chart
                    style={{
                        height: 350,
                    }}
                >
                    <ChartTitle text={chart?.title} />
                    <ChartLegend position="top" orientation="horizontal" />
                    <ChartValueAxis>
                        <ChartValueAxisItem
                            title={{ text: yAxisTitle }}
                            labels={{

                            }}
                            type="numeric"
                        />
                    </ChartValueAxis>
                    <ChartCategoryAxis>
                        <ChartCategoryAxisItem
                            title={{ text: xAxisTitle }}
                            categories={chart.categories}
                            startAngle={45}
                        />
                    </ChartCategoryAxis>
                    <ChartSeries>
                        {chart?.values?.map((item: any, idx) => (
                            <ChartSeriesItem
                                key={idx}
                                type="line"
                                tooltip={{
                                    visible: true,
                                }}
                                data={item.values}
                                name={item.key}
                            />
                        ))}
                    </ChartSeries>
                </Chart>
            </div>
        })

        return graphComponent;
    }

    return (
        <>
            <div className="row">
                {renderTableComponent()}
            </div>
            {charts &&
                <div className="row">
                    {renderGraphComponent()}
                </div>
            }
        </>
    )
}

export default TablesWithCharts;