﻿import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import classNames from "classnames";
import WHOStepper from "../../../controls/WHOStepper";
import { StepperModel } from "../../../../models/StepperModel";
import { DQASteps, Preference, StrategiesEnum } from "../../../../models/Enums";
import classes from "../../data-collection/data-quality-assessment/dqa.module.scss";
import IndicatorsSelection from "../../data-analysis/data-quality-assessment/desk-level-delivery/IndicatorsSelection"
import { VariableModel } from "../../../../models/VariableModel";
import { dqaService } from "../../../../services/dqaService";
import SummaryResult from "./service-level-delivery/SummaryResult";
import { But<PERSON> } from "@mui/material";
import EliminationSummaryResult from "./EliminationSummaryResult"
import { UtilityHelper } from "../../../../utils/UtilityHelper";
import DownloadInProgressModal from "../../../controls/DownloadInProgressModal";

type DQAContainerProps = {
    strategyId: string;
    assessmentId: string;
};

/** Renders the DQA Container for data analysis and all components underneath */
const DQAContainer = (props: DQAContainerProps) => {
    const { t } = useTranslation();
    document.title = t("app.DQATitle");
    const { strategyId, assessmentId } = props;
    const [currentStep, setCurrentStep] = useState<number>(0);
    const [priorityVariables, setPriorityVariables] = useState<Array<VariableModel>>([]);
    const [isFileDownloading, setIsFileDownloading] = useState<boolean>(false);

    const currentlanguage = UtilityHelper.getCookieValue("i18next");

    useEffect(() => {
        getDQACoreVariables();
    }, [strategyId, currentlanguage]);

    // get the core variables for DQA
    const getDQACoreVariables = () => {
        if (!strategyId) return;

        dqaService
            .getDQACoreVariables(strategyId, false)
            .then((response: Array<VariableModel>) => {
                setPriorityVariables(response.filter(variable => variable.priority === Preference.Priority));
            });
    };

    // Triggers when step is changed
    const onStepChange = (currentStep: number) => {
        setCurrentStep(currentStep);
    };

    // Triggers whenever user clicks on export button
    const onGenerateReport = () => {
        const downloadedTemplateName =
        t("app.DQADeskLevelToolReportFileName");
        dqaService
            .downloadReport(assessmentId).then((response: any) => {
                UtilityHelper.download(response, downloadedTemplateName);
                setIsFileDownloading(false);
            }).catch((error: any) => {
                setIsFileDownloading(false);
            });
    }

    return (
        <section className="page-full-section">
            {(strategyId !== StrategiesEnum.Elimination.toLowerCase()) ?
                <>
                    {(strategyId !== StrategiesEnum.Both.toLowerCase()) ?
                        <WHOStepper
                            steps={[
                                new StepperModel(
                                    DQASteps.DeskLevelDelivery,
                                    `${t("Assessment.DataCollection.DataQualityAssessment.DeskLevelDelivery")}`
                                ),
                                new StepperModel(
                                    DQASteps.ServiceLevelDelivery,
                                    `${t("Assessment.DataCollection.DataQualityAssessment.ServiceLevelDelivery")}`
                                ),
                            ]}
                            className={classNames(classes.stepWrapper)}
                            activeStep={currentStep}
                            enableStepClick
                            alternativeLabel={false}
                            nonLinear={true}
                            onStepChange={onStepChange}
                        >
                            <>
                                {currentStep === DQASteps.DeskLevelDelivery &&
                                    <>
                                        <div className="button-action-wrapper">
                                            <div className="button-action-section d-flex align-items-center justify-content-end px-4">
                                                {
                                                    <Button size="small"
                                                        className="btn app-btn-secondary"
                                                        onClick={onGenerateReport}
                                                    >
                                                        {t("Common.Export")}
                                                    </Button>
                                                }
                                            </div>
                                        </div>
                                        <div
                                            className={classNames(
                                                "d-flex",
                                                "align-items-stretch",
                                                "mt-4",
                                                classes.categoryWrapper
                                            )}
                                        >
                                            <IndicatorsSelection assessmentId={assessmentId} />
                                        </div>
                                    </>
                                }

                                {currentStep === DQASteps.ServiceLevelDelivery &&
                                    <>
                                        <div
                                            className={classNames(
                                                "align-items-stretch",
                                                "mt-4",
                                                classes.categoryWrapper
                                            )}
                                        >
                                            <SummaryResult
                                                strategyId={strategyId}
                                                assessmentId={assessmentId}
                                            />
                                        </div>
                                    </>
                                }
                            </>
                        </WHOStepper>
                        :
                        <WHOStepper
                            steps={[
                                new StepperModel(
                                    DQASteps.DeskLevelDelivery,
                                    `${t("Assessment.DataCollection.DataQualityAssessment.DeskLevelDeliveryBurden")}`
                                ),
                                new StepperModel(
                                    DQASteps.ServiceLevelDelivery,
                                    `${t("Assessment.DataCollection.DataQualityAssessment.ServiceLevelDeliveryBurden")}`
                                ),
                                new StepperModel(
                                    DQASteps.EliminationDQA,
                                    `${t("Assessment.DataCollection.DataQualityAssessment.DQAElimination")}`
                                ),
                            ]}
                            className={classNames(classes.stepWrapper)}
                            activeStep={currentStep}
                            enableStepClick
                            alternativeLabel={false}
                            nonLinear={true}
                            onStepChange={onStepChange}
                        >
                            <>
                                {currentStep === DQASteps.DeskLevelDelivery &&
                                    <>
                                        <div className="button-action-wrapper">
                                            <div className="button-action-section d-flex align-items-center justify-content-end px-4">
                                                {
                                                    <Button size="small"
                                                        className="btn app-btn-secondary"
                                                        onClick={onGenerateReport}
                                                    >
                                                        {t("Common.Export")}
                                                    </Button>
                                                }
                                            </div>
                                        </div>
                                        <div
                                            className={classNames(
                                                "d-flex",
                                                "align-items-stretch",
                                                "mt-4",
                                                classes.categoryWrapper
                                            )}
                                        >
                                            <IndicatorsSelection assessmentId={assessmentId} />
                                        </div>
                                    </>
                                }

                                {currentStep === DQASteps.ServiceLevelDelivery &&
                                    <>
                                        <div
                                            className={classNames(
                                                "align-items-stretch",
                                                "mt-4",
                                                classes.categoryWrapper
                                            )}
                                        >
                                            <SummaryResult
                                                strategyId={strategyId}
                                                assessmentId={assessmentId}
                                            />
                                        </div>
                                    </>
                                }

                                {currentStep === DQASteps.EliminationDQA &&
                                    <>
                                        <EliminationSummaryResult
                                            strategyId={strategyId}
                                            assessmentId={assessmentId}
                                        />
                                    </>
                                }
                            </>
                        </WHOStepper>
                    }
                </>
                :
                <EliminationSummaryResult
                    strategyId={strategyId}
                    assessmentId={assessmentId}
                />
            }

            <DownloadInProgressModal isFileDownloading={isFileDownloading} />
        </section>
    )
}

export default DQAContainer;