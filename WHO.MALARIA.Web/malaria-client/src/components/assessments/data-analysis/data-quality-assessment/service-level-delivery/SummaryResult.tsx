﻿import { useTranslation } from "react-i18next";
import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import classNames from "classnames";
import DataGrid from "../../../../controls/DataGrid";
import { GridCellProps, GridColumnProps } from "@progress/kendo-react-grid";
import { getter } from "@progress/kendo-data-query";
import { FinalizeObservedDataQualityReasonModel, ServiceLevelObservedDataQualityReasonModel, ServiceLevelSummaryModel } from "../../../../../models/DQA/ServiceLevelSummaryModel";
import { KeyValuePair } from "../../../../../models/DeskReview/KeyValueType";
import { dqaService } from "../../../../../../src/services/dqaService";
import TextBox from "../../../../controls/TextBox";
import { <PERSON><PERSON>, IconButton } from "@mui/material";
import useFormValidation from "../../../../common/useFormValidation";
import ValidationRules from "./ValidationRules";
import { UserAssessmentPermission } from "../../../../../models/PermissionModel";
import parse from "html-react-parser";
import { UtilityHelper } from "../../../../../utils/UtilityHelper";

const DATA_ITEM_KEY = "id";
const SELECTED_FIELD = "selected";
const idGetter = getter(DATA_ITEM_KEY);

type SummaryResultProps = {
    strategyId: string;
    assessmentId: string
}

/** Renders the Summary Result for servive level delivery DQA */
const SummaryResult = (props: SummaryResultProps) => {
    const { t } = useTranslation();
    const { assessmentId } = props;
    const [serviceLevelSummaryData, setServiceLevelSummaryData] = useState<ServiceLevelSummaryModel>(ServiceLevelSummaryModel.init());
    const [observedDataQualityResultReason, setObservedDataQualityResultReason] = useState<string>("");

    const validate = useFormValidation(ValidationRules);
    const errors = useSelector((state: any) => state.error);

    const userPermission: UserAssessmentPermission = useSelector((state: any) => state.userPermission.assessment);

    const currentlanguage = UtilityHelper.getCookieValue("i18next");

    useEffect(() => {
        getServiceLevelSummary(assessmentId);
    }, [currentlanguage]);

    // create column definition for completeness of core variables
    const getCompletenessOfCoreVariableColumns = (): Array<GridColumnProps> => {
        return [
            {
                field: "variables",
                title: "",
                sortable: false,
                filterable: false,
                cell: (props: GridCellProps) => (
                    <td>
                        <>
                            <span title={props.dataItem["key"]}>{props.dataItem["key"]} </span>
                        </>
                    </td>
                ),
            },
            {
                field: "nationalLevelResults",
                title: "%",
                width: "200",
                sortable: false,
                cell: (props: GridCellProps) => (
                    <>
                        {props.dataItem["value"] != null ?
                            (
                                <td className={getCssClassForCompletenessCoreVariable(props.dataItem["value"])} >
                                    <span>{props.dataItem["value"]} %</span>
                                </td>
                            ) :
                            (
                                <td></td>
                            )
                        }
                    </>
                ),
            },
        ]
    }

    // Convert value to percentage
    const convertToPercentage = (value: number | null) => {
        if (value !== null)
            return Math.round(value * 100);
        else return null;
    }

    // Create data for completeness core variable to be bound
    const getCompletenessCoreVariableData = () => {
        const completenessCoreVariableData: Array<KeyValuePair<string, number | null>> = [];
        if (serviceLevelSummaryData.variableCompletenesses != null) {
            completenessCoreVariableData.push({ key: t("Common.Sex"), value: convertToPercentage(serviceLevelSummaryData.variableCompletenesses.sex) });
            completenessCoreVariableData.push({ key: t("Common.Age"), value: convertToPercentage(serviceLevelSummaryData.variableCompletenesses.age) });
            completenessCoreVariableData.push({ key: t("Common.Diagnosis"), value: convertToPercentage(serviceLevelSummaryData.variableCompletenesses.diagnosis) });
            completenessCoreVariableData.push({ key: t("Common.Total"), value: convertToPercentage(serviceLevelSummaryData.variableCompletenesses.total) });
        }
        return completenessCoreVariableData
    };

    // Create column definition for concordance core variable
    const getConcordanceCoreVariableColumns = (): Array<GridColumnProps> => {
        // Load column for Service Level DQA
        return [
            {
                field: "variables",
                title: "",
                sortable: false,
                filterable: false,
                cell: (props: GridCellProps) => (
                    <td className="text-wrap">
                        <>
                            <span title={props.dataItem["serviceLevelVariableName"]} > {props.dataItem["serviceLevelVariableName"]} </span>
                        </>
                    </td >
                ),
            },
            {
                field: "concordanceCoreVariablePercent",
                title: `${t("Assessment.DataCollection.DataQualityAssessment.ConcordanceCoreVariablePercent")} *`,
                sortable: false,
                cell: (props: GridCellProps) => (
                    <>
                        {props.dataItem["monthConcordance"] !== null ?
                            (
                                <td className={getCssClassForMonthConcordanceCoreVariable(props.dataItem["monthConcordance"])}>
                                    <span>
                                        {convertToPercentage(props.dataItem["monthConcordance"])} %</span>
                                </td>
                            ) :
                            (
                                <td>  </td>
                            )
                        }
                    </>
                ),
            },
            {
                field: "errorDataSources",
                title: t("Assessment.DataCollection.DataQualityAssessment.ErrorDataSources"),
                sortable: false,
                filterable: false,
                cell: (props: GridCellProps) => (
                    <>
                        {props.dataItem["errorInDataSources"] !== null ?
                            (
                                <td className={getCssClassForErrorInDataSource(props.dataItem["errorInDataSources"])}>
                                    <span>{props.dataItem["errorInDataSources"]}</span>
                                </td>
                            ) :
                            (
                                <td></td>
                            )
                        }
                    </>
                ),
            },
            {
                field: "illustration",
                title: t("Assessment.DataCollection.DataQualityAssessment.Result"),
                sortable: false,
                cell: (props: GridCellProps) => (
                    <>
                        {props.dataItem["errorInDataSources"] !== null ?
                            (
                                <td className="text-wrap">
                                    <span title={getIllustrationMessage(props.dataItem["errorInDataSources"])}>{getIllustrationMessage(props.dataItem["errorInDataSources"])}</span>
                                </td>
                            ) :
                            (
                                <td></td>
                            )
                        }
                    </>
                ),
            }
        ];
    };

    // Create data to be bound for concordance of core variables within registers
    const getConcordanceCoreVariablesData = () => {
        return serviceLevelSummaryData.variableData.map(({ serviceLevelVariableName, monthConcordance, errorInDataSources }, index: number) => {
            return {
                [SELECTED_FIELD]: false,
                index,
                serviceLevelVariableName,
                monthConcordance,
                errorInDataSources
            };
        });
    };

    // Get CSS class for completeness core variables to highlight percentage value
    const getCssClassForCompletenessCoreVariable = (value: number) => {
        if (!value && value !== 0) { return ""; }
        switch (true) {
            case value < 80 || value > 100:
                return "bg-red";
            case value >= 80 && value <= 95:
                return "bg-yellow";
            case value > 95 && value <= 100:
                return "bg-green";
            default: return "";
        }
    }

    // Get CSS class for month concordance core variables to highlight percentage value
    const getCssClassForMonthConcordanceCoreVariable = (value: number) => {
        let className: string = "";
        const percentageValue = convertToPercentage(value);
        if (percentageValue !== null) {
            className = getCssClassForCompletenessCoreVariable(percentageValue);
        }

        return className;
    }

    // Get CSS class for Error in Datasource to highlight value
    const getCssClassForErrorInDataSource = (value: number) => {
        switch (true) {
            case value > 0:
                return "bg-blue";
            case value < 0:
                return "bg-red";
            default: return "";
        }
    }

    //Get Illustration message 
    const getIllustrationMessage = (value: number) => {
        switch (true) {
            case value > 0:
                return t("Assessment.DataCollection.DataQualityAssessment.Message.HmisLessCasesMessage");
            case value < 0:
                return t("Assessment.DataCollection.DataQualityAssessment.Message.HmisMoreCasesMessage");
            case value == 0:
                return t("Assessment.DataCollection.DataQualityAssessment.Message.HmisEqualCasesMessage");
            default: return "";
        }
    }

    // Get service level dqa summary response by assessmentId
    const getServiceLevelSummary = (assessmentId: string) => {
        dqaService.getServiceLevelSummary(assessmentId).then((response) => {
            setServiceLevelSummaryData(response);
            if (response.variableCompletenesses !== null) {
                setObservedDataQualityResultReason(response.variableCompletenesses.observedDataQualityResultReason);
            }
        });
    }

    // Triggers whenever the user modifies the observed data quality reason.
    const onObservedDataQualityReasonChange = (evt: React.ChangeEvent<HTMLInputElement>) => {
        const value: string = evt.currentTarget.value;
        setObservedDataQualityResultReason(value);
    }

    // Triggers whenever user clicks on Save button
    const onSave = () => {
        const isFormValid = validate({ observedDataQualityResultReason: observedDataQualityResultReason });
        if (isFormValid) {
            const request: ServiceLevelObservedDataQualityReasonModel = new ServiceLevelObservedDataQualityReasonModel(assessmentId, serviceLevelSummaryData.serviceLevelId, observedDataQualityResultReason);
            dqaService.updateServiceLevelObservedDataQualityReason(request);
        }
    }

    // Triggers whenever user clicks on finalize button
    const onFinalize = () => {
        const isFormValid = validate({ observedDataQualityResultReason: observedDataQualityResultReason });

        if (isFormValid) {
            const request: FinalizeObservedDataQualityReasonModel = new FinalizeObservedDataQualityReasonModel(assessmentId, serviceLevelSummaryData.serviceLevelId);
            dqaService.finalizeObservedDataQualityResultReason(request);
        }
    }

    return (
        <>
            {serviceLevelSummaryData.variableCompletenesses != null && serviceLevelSummaryData.variableData.length ?
                (
                    <>
                        < div className={classNames("summary-wrapper", "p-3")}>
                            <div className="d-flex justify-content-center mb-3">
                                <span className="fw-bold fs-5">{serviceLevelSummaryData?.year}</span>
                            </div>
                            <div className="row">
                                <div className="col-xs-12 col-md-6">
                                    <div>
                                        <p className="fw-bold">
                                            {t("Assessment.DataCollection.DataQualityAssessment.Desc1.2.11")}
                                        </p>

                                        <DataGrid
                                            className="k-widget k-grid k-grid-wrapper k-grid-summary"
                                            columns={getCompletenessOfCoreVariableColumns()}
                                            data={getCompletenessCoreVariableData()}
                                            dataItemKey={DATA_ITEM_KEY}
                                            selectedField={SELECTED_FIELD}
                                            selectable={{
                                                enabled: true,
                                                drag: false,
                                                cell: true,
                                                mode: "single",
                                            }}
                                            sortable={false}
                                            filterable={false}
                                            hasActionBtn={true}
                                        />
                                    </div>
                                </div>
                                <div className="col-xs-12 col-md-12 mt-3">
                                    <p className="mb-0">{parse(t("Assessment.DataCollection.DataQualityAssessment.LegendTextCoreVariables"))}</p>
                                </div>

                                <div className="col-xs-12 col-md-12 mt-5">
                                    <p className="fw-bold">
                                        {t("Assessment.DataCollection.DataQualityAssessment.Desc1.2.12")} <br />
                                        {t("Assessment.DataCollection.DataQualityAssessment.Desc1.2.13")}
                                    </p>

                                    <DataGrid
                                        className="k-widget k-grid k-grid-wrapper k-grid-summary"
                                        columns={getConcordanceCoreVariableColumns()}
                                        data={getConcordanceCoreVariablesData()}
                                        dataItemKey={DATA_ITEM_KEY}
                                        selectedField={SELECTED_FIELD}
                                        selectable={{
                                            enabled: true,
                                            drag: false,
                                            cell: true,
                                            mode: "single",
                                        }}
                                        sortable={false}
                                        filterable={false}
                                        hasActionBtn={true}
                                    />

                                    <p className="mt-3">{parse(t("Assessment.DataCollection.DataQualityAssessment.LegendTextCoreVariables"))}</p>

                                    <p className="mb-0">{parse(t("Assessment.DataCollection.DataQualityAssessment.LegendTextErrorDataSource"))}</p>
                                </div>

                                <div className="col-xs-12 col-md-6">
                                    <div className="mt-5">
                                        <p className="fw-bold">
                                            {t("Assessment.DataCollection.DataQualityAssessment.Desc1_2_14")}
                                        </p>
                                        <TextBox
                                            id="observedDataQualityResultReason"
                                            name="observedDataQualityResultReason"
                                            placeholder={t("Assessment.DataCollection.DataQualityAssessment.Placeholder1.2.14")}
                                            rows={6}
                                            multiline
                                            fullWidth
                                            value={observedDataQualityResultReason}
                                            onChange={(evt: React.ChangeEvent<HTMLInputElement>) => {
                                                onObservedDataQualityReasonChange(evt);
                                            }}
                                            error={errors["observedDataQualityResultReason"] && errors["observedDataQualityResultReason"]}
                                            helperText={errors["observedDataQualityResultReason"] && errors["observedDataQualityResultReason"]}
                                            maxLength={2000}
                                        />
                                    </div>
                                </div>
                                {userPermission.canUploadFile &&

                                    <div className="button-action-section d-flex justify-content-center py-3">
                                        <Button className={classNames("btn", "app-btn-secondary")} onClick={onSave}>
                                            {t("Common.Save")}
                                        </Button>
                                        <Button className={classNames("btn", "app-btn-secondary")} onClick={onFinalize}>
                                            {t("Common.Finalize")}
                                        </Button>
                                    </div>

                                }
                            </div>
                        </div>
                    </>
                ) :
                (
                    <div className={classNames("summary-wrapper", "p-3", "d-flex", "justify-content-center", "fw-bold", "my-5")}>
                        {t("Assessment.DataCollection.DataQualityAssessment.SummaryDataNotAvailable")}
                    </div>
                )
            }
        </>
    );
}

export default SummaryResult;