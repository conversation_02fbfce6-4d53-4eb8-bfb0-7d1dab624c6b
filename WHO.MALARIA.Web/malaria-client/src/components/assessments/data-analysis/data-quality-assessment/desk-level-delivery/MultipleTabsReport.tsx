﻿import React, { useState } from "react";
import { useTranslation } from "react-i18next";
//import { ViewReportMultipleTabModel } from "../../../../../models/DataAnalysis/DQA/DataQualityAssessmentModel";
import WHOTabs from "../../../../controls/WHOTabs";
import { TabModel } from "../../../../../models/TabModel";
import { TabWithTablesChartsModel, MultipleChildTabsModel } from "../../../../../models/TabWithTablesChartsModel";
import TablesWithCharts from "../../../../common/TablesWithCharts";

type MultipleTabsProps = {
    data: Array<MultipleChildTabsModel>;
    orientation: "vertical" | "horizontal" | undefined;
};

/** Data analysis DL DQA component which renders nested tabs for the indicators with (table/graph components) */
const MultipleTabsReport = (props: MultipleTabsProps) => {
    const { t } = useTranslation();
    const { data, orientation } = props;
    const [currentTab, setCurrentTab] = useState<number>(0);
    const [currentSubTab, setCurrentSubTab] = useState<number>(0);

    // Triggers whenever tab is changed
    const onTabChange = (event: React.ChangeEvent<{}>, tabIndex: number) => {
        setCurrentTab(tabIndex);
    };

    // Triggers whenever tab is changed
    const onSubTabChange = (event: React.ChangeEvent<{}>, tabIndex: number) => {
        setCurrentSubTab(tabIndex);
    };

    // Generate dynamic sub tab's component
    const renderSubTabComponent: any = (tabs: any) => {
        return (tabs?.map((tab: TabWithTablesChartsModel, index: number) => {
            const tabModel = new TabModel(
                tab?.tabId,
                tab?.tabName,
                <></>
            )

            return tabModel;
        })
        )
    };

    // Generate dynamic tab's component
    const renderTabComponent: Array<TabModel> = data?.map((tab: MultipleChildTabsModel) => {
        return (new TabModel(
            tab.tabId,
            tab.tabName,
            <>
                <WHOTabs
                    tabs={renderSubTabComponent(tab.tabs)}
                    value={currentSubTab}
                    onChange={onSubTabChange}
                    scrollable={false}
                    orientation={orientation}
                >
                    <div className="p-2">
                        {
                            tab?.tabs &&
                            <TablesWithCharts
                                tables={tab?.tabs[currentSubTab]?.tables}
                                charts={tab?.tabs[currentSubTab]?.charts}
                            />
                        }
                    </div>
                </WHOTabs>
            </>
        ))
    });

    return (
        <>
            {data?.length &&
                <div className="app-tab-wrapper">
                    <WHOTabs
                        tabs={renderTabComponent}
                        value={currentTab}
                        onChange={onTabChange}
                        scrollable={false}
                    >
                        <div className="p-2"> {renderTabComponent[currentTab].children} </div>
                    </WHOTabs>
                </div>
            }
        </>
    )
}

export default MultipleTabsReport;