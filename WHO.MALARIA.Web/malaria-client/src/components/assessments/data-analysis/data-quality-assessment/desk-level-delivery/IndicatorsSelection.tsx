﻿import React, { useEffect, useState } from "react";
import classes from "../../../data-collection/data-quality-assessment/dqa.module.scss";
import classNames from "classnames";
import { useTranslation } from "react-i18next";
import DataGrid from "../../../../controls/DataGrid";
import { dataQualityAssessmentService } from "../../../../../services/dataQualityAssessmentService";
import {
    GridCellProps,
    GridColumnProps,
    GridHeaderCellProps,
} from "@progress/kendo-react-grid";
import { getter } from "@progress/kendo-data-query";
import { Button, IconButton, Table, TableBody, TableCell, TableRow } from "@mui/material";
import Tooltip from "../../../../controls/Tooltip";
import InfoIcon from "@mui/icons-material/Info";
import { useNavigate,  useLocation } from "react-router-dom";
import { GetIndicatorModel } from "../../../../../models/IndicatorModel";
import { dqaService } from "../../../../../services/dqaService";
import { IndicatorModel } from "../../../../../models/DQA/IndicatorModel";
import { Label } from "@mui/icons-material";
import { align } from "@progress/kendo-drawing";
import { UtilityHelper } from "../../../../../utils/UtilityHelper";

const DATA_ITEM_KEY = "id";
const SELECTED_FIELD = "selected";
const idGetter = getter(DATA_ITEM_KEY);

type IndicatorSelectionProps = {
    assessmentId: string
}

/** Renders the Indicator Selection */
const IndicatorsSelection = (props: IndicatorSelectionProps) => {
    const { t } = useTranslation();
    const navigate = useNavigate();
    const location: any = useLocation();
    const subTabIndex = location?.state?.subTabIndex;
    const strategyId = location?.state?.strategyId;
    const { assessmentId } = props;
    const [indicators, setIndicators] = useState<Array<IndicatorModel>>([]);

    const currentlanguage = UtilityHelper.getCookieValue("i18next");

    useEffect(() => {
        bindIndicatorsForDQA();
    }, [currentlanguage])

    // create column definition
    const getColDefs = (): Array<GridColumnProps> => {
        return [
            {
             
                title: t("Assessment.DataCollection.DataQualityAssessment.Indicator"),
                sortable: true,
                cell: (props: GridCellProps) => (
                    <td>
                        <span>{props.dataItem["sequence"]}</span>
                        <span className="ps-2">{props.dataItem["name"]} </span>
                        <IconButton className="grid-icon-button">
                            <Tooltip content={props?.dataItem["description"]} isHtml>
                                <InfoIcon fontSize="small" />
                            </Tooltip>
                        </IconButton>
                    </td>
                ),
            },
            {
                sortable: true,
                cell: (props: GridCellProps) => (
                    <td>
                        <IconButton className="grid-icon-button action-btn">
                            <Button
                                className="btn app-btn-secondary"
                                onClick={() =>
                                    onViewClick(
                                        props.dataItem["id"],
                                        props.dataItem["name"],
                                        props.dataItem["sequence"],
                                        props.dataItem["assessmentIndicatorId"],
                                        props.dataItem["assessmentStrategyId"]
                                    )
                                }
                            >
                                {t("Common.View")}
                            </Button>
                        </IconButton>
                    </td>
                ),
            },
        ];
    };

    // triggers whenever user takes an action to view the data quality assessment indicator
    const onViewClick = (
        id: number,
        name: string,
        sequence: string,
        assessmentIndicatorId: string,
        assessmentStrategyId: string
    ) => {
        navigate("/assessment/data-analysis/dqa/indicator/response", { state: {
            assessmentId: assessmentId,
            indicatorId: id,
            indicatorName: name,
            indicators,
            sequence,
            country: location?.state.country,
            approach: location?.state.approach,
            status: location.state.status,
            assessmentIndicatorId,
            assessmentStrategyId,
            subTabIndex: location.state.subTabIndex,
            strategyId: strategyId
        } });
    };

    // triggers whenever user takes an action to view the data quality assessment summary result for burden reduction.
    const onSummaryResultViewClick = () => {
        navigate("/assessment/data-analysis/dqa/indicator/summary", { state: {
            assessmentId: assessmentId,
            indicators,
            country: location?.state.country,
            approach: location?.state.approach,
            status: location.state.status,
            subTabIndex: location.state.subTabIndex,
            strategyId: strategyId
        } });
    };

    // Call Desk Level-DQA indicators API service and map to GetIndicatorModel.
    const bindIndicatorsForDQA = () => {

        dqaService
            .getDeskLevelIndicators()
            .then((indicators: Array<IndicatorModel>) => {
                const indicatorsList = indicators.map((indicator: IndicatorModel, index: number) => {
                    return {
                        ...indicator,
                        sr: ++index,
                    };
                });
                setIndicators(indicatorsList);
            });

    };

    return (
        <div className={classNames(classes.indicatorWrapper)}>
            <DataGrid
                className="k-grid-wrapper k-grid-action-wrapper k-desk-review hide-grid-icon"
                columns={getColDefs()}
                data={indicators}
                hasActionBtn={true}
            />
            {/*Summary Results for DL DQA*/}
            <div className="k-grid-wrapper">
                <Table>
                    <>
                        <TableBody>
                            <TableRow className="pe-auto">

                                <TableCell className="py-2 ">
                                   {t("Assessment.DataCollection.DataQualityAssessment.SummaryResults")}
                                </TableCell>
                                <TableCell className="py-0 text-end">
                                    <Button
                                        className="btn app-btn-secondary grid-icon-button action-btn"
                                        onClick={() => onSummaryResultViewClick()}
                                    >
                                        {t("Common.View")}
                                    </Button>
                                </TableCell>
                            </TableRow>
                        </TableBody>
                    </>
                </Table>
            </div>
        </div>
    );
}

export default IndicatorsSelection;
