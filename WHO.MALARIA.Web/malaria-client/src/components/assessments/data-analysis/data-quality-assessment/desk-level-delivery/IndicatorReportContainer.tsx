﻿import { IconButton } from "@mui/material";
import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate,  useLocation } from "react-router-dom";
import CancelIcon from "@mui/icons-material/Close";
import Breadcrumbs from "../../../data-collection/desk-review/responses/Breadcrumbs";
import TabsWithTablesAndCharts from "../../../../common/TabsWithTablesAndCharts";
import MultipleTabsReport from "./MultipleTabsReport";
import { DLDQAReportType } from "../../../../../models/Enums";
import { IndicatorReportModel } from "../../../../../models/DataAnalysis/DeskReview/IndicatorReportDetailsModel";
import { dqaService } from "../../../../../services/dqaService";

/** Data analysis DL DQA indicator report container which renders indicator components to be rendered*/
const IndicatorReportContainer = () => {
    const { t } = useTranslation();
    const location: any = useLocation();
    const navigate = useNavigate();

    const indicatorName = location?.state?.indicatorName;
    const assessmentId = location?.state?.assessmentId;
    const strategyId = location?.state?.strategyId;
    const sequence = location?.state?.sequence;
    const indicatorId = location?.state?.indicatorId;
    const [subObjectiveName, setSubObjectiveName] = useState<string>("");
    const subTabIndex = location?.state?.subTabIndex;

    const [indicatorReportData, setIndicatorReportData] = useState<any>();
    const [indicatorReport, setIndicatorReport] = useState<IndicatorReportModel>();

    useEffect(() => {
        setSubObjectiveName(location?.state?.subObjectiveName);
    }, [location?.state?.subObjectiveName]);

    // Map indicator sequence with the DQAIndicatorReport enum from the server side 
    // Pass this as a parameter to get DQA report
    const DQAReportType = ["", "1.2.1", "1.2.3", "1.2.7", "1.2.8", "1.2.9", "1.2.10"].indexOf(sequence)

    // Get desk level indicators report
    const getDeskLevelReport = () => {
        dqaService
            .getDeskLevelReport(
                DQAReportType,
                assessmentId
            ).then((indicatorReport: IndicatorReportModel) => {
                const indicatorReportResponse = indicatorReport.response;
                setIndicatorReportData(indicatorReportResponse);
                setIndicatorReport(indicatorReport);
            })
    }

    useEffect(() => {
        getDeskLevelReport();
    }, []);

    //Triggers whenever user takes an action on close or back navigation click
    const onBackOrCloseButtonClick = () => {
        const state = location?.state;

        navigate("/assessment/data-analysis/dqa", { state: {
            ...state,
            subTabIndex,
            strategyId
        } });
    };

    return (
        <>
            <section className="page-full-section page-full-assess-section">
                <div className="assess-header-section d-flex pt-2 pb-2">
                    <Breadcrumbs
                        parentLabel={t("Assessment.DataCollection.DataQualityAssessment.DataAnalysis")}
                        label={`${sequence} ${indicatorName}`}
                        onNavigate={onBackOrCloseButtonClick}
                    />
                    <div className="ml-auto">
                        <IconButton onClick={onBackOrCloseButtonClick} title="Close" className="close-modal">
                            <CancelIcon />
                        </IconButton>
                    </div>
                </div>

                <div className="page-response-section page-report-section">
                    <div className="response-wrapper h-500">                       
                        {indicatorReport?.type === DLDQAReportType.TabWithTableChart &&
                            <TabsWithTablesAndCharts data={indicatorReportData} />
                        }

                        {(indicatorReport?.type === DLDQAReportType.MultipleTabsWithTableChart ||
                            indicatorReport?.type === DLDQAReportType.SidebarTabsWithTableChart) &&
                            <>
                                <MultipleTabsReport
                                    data={indicatorReportData}
                                    orientation={indicatorReport?.type === DLDQAReportType.SidebarTabsWithTableChart
                                        ? "vertical" : "horizontal"}
                                />
                            </>
                        }
                    </div>
                </div>
            </section>
        </>
    )
}

export default IndicatorReportContainer;