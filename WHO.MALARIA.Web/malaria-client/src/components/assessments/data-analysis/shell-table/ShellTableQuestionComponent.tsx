﻿import { useTranslation } from "react-i18next";
import { QuestionModel, ShellTableHeader } from "../../../../models/ShellTable/ShellTableModel";
import { GeographicLevels, OptionOrders, QuestionIds_For_Caclulation, ShellTableQuestionCalculationResultTypes } from "../../../../models/Enums";
import Table from "../../data-collection/desk-review/responses/Table";
import TableRow from "../../data-collection/desk-review/responses/TableRow";
import TableCell from "../../data-collection/desk-review/responses/TableCell";
import TableBody from "../../data-collection/desk-review/responses/TableBody";
import { Hidden } from "@mui/material";

type ShellTableQuestionsProps = {
    shellTableQuestions: Array<QuestionModel>;
    geoGraphiclevel: number;
};

/** Renders the shell table question component*/
const ShellTableQuestionComponent = (props: ShellTableQuestionsProps) => {
    const { t } = useTranslation();
    document.title = t("app.QuestionBankTitle");

    const {
        shellTableQuestions
    } = props;

    return (
        <>
            {shellTableQuestions.map((question: QuestionModel, questionIndex: number) => {
                return (
                    <Table key={`table_${questionIndex}_${Math.random()}`} className="app-table questions-table table">
                        <>
                            <thead>
                                <TableRow key={`header_${questionIndex}_${Math.random()}`} >
                                    <>
                                        {question.headers.map((header: ShellTableHeader, headerIndex: number) => {
                                            return (
                                                headerIndex == 0 ?
                                                    (
                                                        <th colSpan={question.shellTableQuestionCalculationResultType == ShellTableQuestionCalculationResultTypes.Average ? ShellTableQuestionCalculationResultTypes.Average : 0}>
                                                            {header.headerName}
                                                        </th>
                                                    ) : (
                                                        <>
                                                            {props.geoGraphiclevel === GeographicLevels.District &&
                                                                (<th colSpan={2}>
                                                                    {header.regionName}
                                                                    <br />  <br />
                                                                    {header.headerName}
                                                                </th>)
                                                            }

                                                            {props.geoGraphiclevel !== GeographicLevels.District &&
                                                                (<th colSpan={2}>
                                                                    {header.headerName}
                                                                </th>)
                                                            }
                                                        </>
                                                    )
                                            )
                                        }
                                        )}
                                    </>
                                </TableRow>
                            </thead>
                            <thead>
                                <TableRow key={`subheader_${questionIndex}_${Math.random()}`}>
                                    <>
                                        {question.headers.map((header: ShellTableHeader, subHeaderIndex: number) => {
                                            return (
                                                subHeaderIndex == 0 ?
                                                    (
                                                        <th colSpan={question.shellTableQuestionCalculationResultType == ShellTableQuestionCalculationResultTypes.Average ? ShellTableQuestionCalculationResultTypes.Average : 0}>

                                                        </th>
                                                    ) : (
                                                        <>

                                                            {question.shellTableQuestionCalculationResultType == ShellTableQuestionCalculationResultTypes.Percentage && (
                                                                <th>N</th>)

                                                            }

                                                            <th colSpan={question.shellTableQuestionCalculationResultType == ShellTableQuestionCalculationResultTypes.Average ? ShellTableQuestionCalculationResultTypes.Average : 0}>
                                                                {question.shellTableQuestionCalculationResultType == ShellTableQuestionCalculationResultTypes.Percentage ? (<>%</>)
                                                                    : (question.indicatorId === QuestionIds_For_Caclulation.Que_4_3_2 && (questionIndex == 1 || questionIndex == 2)) ? (<> {t("Common.AverageVisits")}</>)
                                                                        : ((question.indicatorId === QuestionIds_For_Caclulation.Que_4_3_3 && questionIndex == 4)) || (question.indicatorId === QuestionIds_For_Caclulation.Que_3_5_2) ? (<> {t("Common.AverageScore")}</>)
                                                                            : (<> {t("Common.Average")}</>)}
                                                            </th>
                                                        </>
                                                    )
                                            )
                                        }
                                        )}
                                    </>
                                </TableRow>
                            </thead>
                            <TableBody>
                                <>
                                    {question.options.map((optionData: Array<string>, optionIndex: number) => {
                                        if (question.indicatorId === QuestionIds_For_Caclulation.Que_1_3_3 && questionIndex == 0) {
                                            return (
                                                <TableRow key={`option${optionIndex}_${Math.random()}`}>
                                                    <>
                                                        {optionData.map((option: string, index: number) => {
                                                            if (optionIndex == OptionOrders.OptionOrder_1 && index != 0 && (index % 2 == 0)) {
                                                                return (
                                                                    <TableCell rowSpan={2} className="td-percentage">{option}</TableCell>
                                                                )
                                                            }
                                                            else {
                                                                return (
                                                                    <TableCell>{option}</TableCell>
                                                                )
                                                            }
                                                        }
                                                        )}
                                                    </>
                                                </TableRow>
                                            )
                                        }
                                        else if (question.indicatorId === QuestionIds_For_Caclulation.Que_1_3_4 && questionIndex == 0) {
                                            return (
                                                <TableRow key={`option${optionIndex}_${Math.random()}`}>
                                                    <>
                                                        {optionData.map((option: string, index: number) => {
                                                            if ((optionIndex == OptionOrders.OptionOrder_1 || optionIndex === OptionOrders.OptionOrder_3) && index != 0 && (index % 2 == 0)) {
                                                                return (
                                                                    <TableCell rowSpan={2} className="td-percentage">{option}</TableCell>
                                                                )
                                                            }
                                                            else {
                                                                return (
                                                                    <TableCell>{option}</TableCell>
                                                                )
                                                            }
                                                        }
                                                        )}
                                                    </>
                                                </TableRow>
                                            )
                                        }
                                        else if (question.indicatorId === QuestionIds_For_Caclulation.Que_2_4_1 && questionIndex == shellTableQuestions.length - 1) {
                                            return (
                                                <TableRow key={`option${optionIndex}_${Math.random()}`}>
                                                    <>
                                                        {optionData.map((option: string, index: number) => {
                                                            if (optionIndex == OptionOrders.OptionOrder_1 && index != 0 && (index % 2 == 0)) {
                                                                return (
                                                                    <TableCell rowSpan={2} className="td-percentage">{option}</TableCell>
                                                                )
                                                            }
                                                            else {
                                                                return (
                                                                    <TableCell>{option}</TableCell>
                                                                )
                                                            }
                                                        }
                                                        )}
                                                    </>
                                                </TableRow>
                                            )
                                        }
                                        else {
                                            return (
                                                <TableRow key={`option${optionIndex}_${Math.random()}`}>
                                                    <>
                                                        {optionData.map(((option, index) => {
                                                            if (question.shellTableQuestionCalculationResultType == ShellTableQuestionCalculationResultTypes.Average && index != 0 && index % 2 == 0) {
                                                                return (
                                                                    <></>)
                                                            }
                                                            else {
                                                                return (
                                                                    <TableCell colSpan={question.shellTableQuestionCalculationResultType == ShellTableQuestionCalculationResultTypes.Average ? ShellTableQuestionCalculationResultTypes.Average : 0}>{option}</TableCell>)
                                                            }
                                                        }

                                                        ))}
                                                    </>
                                                </TableRow>
                                            )
                                        }
                                    }
                                    )}

                                    {question.shellTableQuestionCalculationResultType === ShellTableQuestionCalculationResultTypes.Percentage &&
                                        <TableRow key={`total${questionIndex}_${Math.random()}`}>
                                            <>
                                                {question.totals.map((total: number, totalIndex: number) => {
                                                    return (
                                                        totalIndex == 0 ?
                                                            (
                                                                <>
                                                                    <TableCell>
                                                                        {t("Common.Total")}
                                                                    </TableCell>
                                                                    <TableCell colSpan={2}>
                                                                        {total}
                                                                    </TableCell>
                                                                </>
                                                            ) : (
                                                                <TableCell colSpan={2}>
                                                                    {total}
                                                                </TableCell>
                                                            )
                                                    )
                                                }
                                                )}
                                            </>
                                        </TableRow>
                                    }
                                </>
                            </TableBody>
                        </>
                    </Table>
                )
            })}
        </>
    )
}

export default ShellTableQuestionComponent;