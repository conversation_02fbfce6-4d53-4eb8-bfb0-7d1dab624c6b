﻿import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useLocation } from "react-router-dom";
import Tabs, { WHOTab } from "../../../controls/Tabs";
import { shellTableService } from "../../../../services/shellTableService";
import { HealthFacilityRepondentType, RespondentAndHealthFacilityType, ObjectiveModel, SubObjectiveModel, IndicatorModel, ObjectivesSubObjectivesIndicatorsModel, QuestionModel } from "../../../../models/ShellTable/ShellTableModel";
import { KeyValuePair } from "../../../../models/DeskReview/KeyValueType";
import MultiSelectModel from "../../../../models/MultiSelectModel";
import Dropdown from "../../../controls/Dropdown";
import { GeographicLevels, RespondentType, HealthFacilityType } from "../../../../models/Enums";
import WHOTabs from "../../../controls/WHOTabs";
import { TabModel } from "../../../../models/TabModel";
import ShellTableQuestionComponent from "./ShellTableQuestionComponent";
import { Button, IconButton } from "@mui/material";
import InfoIcon from "@mui/icons-material/Info";
import Tooltip from "../../../controls/Tooltip";
import { UtilityHelper } from "../../../../utils/UtilityHelper";

/** Renders the shell table container for data analysis and all components underneath */
const ShellTableContainer = () => {
    const { t } = useTranslation();
    document.title = t("app.QuestionBankTitle");

    const location: any = useLocation();
    const assessmentId = location?.state?.assessmentId;

    const [respondentTypes, setRespondentTypes] = useState<Array<KeyValuePair<number, string>>>([]);
    const [healthFacilityTypes, setHealthFaclityTypes] = useState<Array<HealthFacilityRepondentType>>([]);
    const [filterHealthFacilityTypes, setFilterHealthFaclityTypes] = useState<Array<HealthFacilityRepondentType>>([]);
    const [geoGraphicLevels, setGeoGraphicLevels] = useState<Array<KeyValuePair<number, string>>>([]);
    const [showHealthFacilityType, SetShowHealthFacilityType] = useState(true);
    const [respondentType, setRespondentType] = useState<number>(RespondentType.SubnationalLevel);
    const [healthFacilityType, setHealthFacilityType] = useState<number>(HealthFacilityType.Public);
    const [geoGraphiclevel, setGeoGraphicLevel] = useState<number>(GeographicLevels.National);

    const [respondentTypesHeathFaciityTypes, setRespondentTypesHeathFaciityTypes] = useState<RespondentAndHealthFacilityType>(RespondentAndHealthFacilityType.init());
    const [objectivesSubObjectivesIndicators, setObjectivesSubObjectivesIndicators] = useState<ObjectivesSubObjectivesIndicatorsModel>(ObjectivesSubObjectivesIndicatorsModel.init());
    const [questions, setQuestions] = useState<Array<QuestionModel>>([]);
    const [filteredQuestions, setFilteredQuestions] = useState<Array<QuestionModel>>([]);

    const [currentObjectiveTabIndex, setCurrentObjectiveTabIndex] = useState<number>(0);
    const [currentSubObjectiveTabIndex, setCurrentSubObjectiveTabIndex] = useState<number>(0);
    const [currentIndicatorTabIndex, setCurrentIndicatorTabIndex] = useState<number>(0);

    const [objectiveId, setObjectiveId] = useState("");
    const [subObjectiveId, setSubObjectiveId] = useState("");
    const [indicatorId, setIndicatorId] = useState("");

    const [objectives, setObjectives] = useState<Array<ObjectiveModel>>([]);
    const [filteredObjectives, setFilteredObjectives] = useState<Array<ObjectiveModel>>([]);
    const [subObjectives, setSubObjectives] = useState<Array<SubObjectiveModel>>([]);
    const [filteredSubObjectives, setFilteredSubObjectives] = useState<Array<SubObjectiveModel>>([]);
    const [indicators, setIndicators] = useState<Array<IndicatorModel>>([]);
    const [filteredIndicators, setFilteredIndicators] = useState<Array<IndicatorModel>>([]);
    const currentlanguage = UtilityHelper.getCookieValue("i18next");
    useEffect(() => {
        bindObjectivesSubobjectivesIndicators();
        bindRespondentTypesHealthFacilityTypes();
        bindGeographicLevel();
    }, [currentlanguage]);

    // Get respondent type , health facility types, objectives based on respondent type and objective sub objective indicator response
    useEffect(() => {
        if (respondentTypesHeathFaciityTypes.respondentTypes.length > 0 && objectivesSubObjectivesIndicators.objectives.length > 0) {

            setRespondentTypes(respondentTypesHeathFaciityTypes.respondentTypes);
            setHealthFaclityTypes(respondentTypesHeathFaciityTypes.healthFacilityTypes);
            setObjectives(objectivesSubObjectivesIndicators.objectives);
            setSubObjectives(objectivesSubObjectivesIndicators.subObjectives);
            setIndicators(objectivesSubObjectivesIndicators.indicators);

            getHealthFacilityObjectivesData(respondentTypesHeathFaciityTypes.healthFacilityTypes, respondentTypesHeathFaciityTypes.respondentTypes[0].key, objectivesSubObjectivesIndicators);
        }
    }, [respondentTypesHeathFaciityTypes, objectivesSubObjectivesIndicators]);

    // Get filter questions based on indicator id and questions
    useEffect(() => {
        if (indicatorId && questions.length) {
            filterQuestions(indicatorId);
        }
    }, [questions]);

    // Bind objectives, sub-objectives and indicators based on assessmentId
    const bindObjectivesSubobjectivesIndicators = () => {
        shellTableService
            .getObjectiveSubObjectivesIndicatorsAsync(assessmentId)
            .then((response: ObjectivesSubObjectivesIndicatorsModel) => {

                setObjectivesSubObjectivesIndicators(response);
            });
    };

    // Bind respondent types and health facility types
    const bindRespondentTypesHealthFacilityTypes = () => {
        shellTableService
            .getRespondentTypesHealthFacilityTypesByAssessmentId(assessmentId)
            .then((response: RespondentAndHealthFacilityType) => {

                setRespondentTypesHeathFaciityTypes(response);
            });
    };

    // Bind geo graphic level
    const bindGeographicLevel = () => {
        const geoGraphicLevelData: Array<KeyValuePair<number, string>> =
            [
                { key: GeographicLevels.National, value: t("Common.National") },
                { key: GeographicLevels.Regional, value: t("Common.Regional") },
                { key: GeographicLevels.District, value: t("Common.District") },
            ];

        setGeoGraphicLevels(geoGraphicLevelData);
    }

    // Bind questions based on assessmentId, respondent type, geo graphical level, health facility type
    const bindQuestions = (respondentType: number, geoGraphicLevel: number, healthFacilityType: number) => {
        shellTableService
            .getQuestionsAsync(assessmentId, respondentType, geoGraphicLevel, healthFacilityType)
            .then((response: Array<QuestionModel>) => {
                setQuestions(response);
            });
    };

    // Bind health facility type, objectives based on respondent type
    const getHealthFacilityObjectivesData = (healthFacilityData: Array<HealthFacilityRepondentType>, respondentType: number, objectivesSubObjectivesIndicators: ObjectivesSubObjectivesIndicatorsModel) => {
        let _healthFacilityType = healthFacilityType;

        if (RespondentType.SubnationalLevel != respondentType) {
            const filteredHealthFacility: Array<HealthFacilityRepondentType> = healthFacilityData.filter(
                (objective: HealthFacilityRepondentType) =>
                    objective.respondentType === respondentType
            );
            _healthFacilityType = filteredHealthFacility[0].healthFacilityType;
            setRespondentType(respondentType);
            setFilterHealthFaclityTypes(filteredHealthFacility);
            setHealthFacilityType(_healthFacilityType);
            SetShowHealthFacilityType(true);
        } else {
            SetShowHealthFacilityType(false);
            setHealthFacilityType(_healthFacilityType);
        }

        filterObjective(objectivesSubObjectivesIndicators.objectives, objectivesSubObjectivesIndicators.subObjectives, objectivesSubObjectivesIndicators.indicators, respondentType)

        bindQuestions(respondentType, geoGraphiclevel, _healthFacilityType);
    }

    // Triggers whenever user tries to modify the value of respondent type
    const onChangeRespondentType = (evt: React.ChangeEvent<HTMLInputElement>) => {
        if (evt.currentTarget.value) {
            setRespondentType(+evt.currentTarget.value);
            getHealthFacilityObjectivesData(healthFacilityTypes, +evt.currentTarget.value, objectivesSubObjectivesIndicators);
        }
    }

    // Triggers whenever user tries to modify the value of geo graphic level
    const onChangeGeographicLevel = (evt: React.ChangeEvent<HTMLInputElement>) => {
        if (evt.currentTarget.value) {
            const _geoGraphiclevel: number = +evt.currentTarget.value;
            setGeoGraphicLevel(_geoGraphiclevel);
            bindQuestions(respondentType, _geoGraphiclevel, healthFacilityType);
        }
    }

    // Triggers whenever user tries to modify the value of health facility type
    const onChangeHealthFacilityType = (evt: React.ChangeEvent<HTMLInputElement>) => {
        if (evt.currentTarget.value) {
            const _healthFacilityType: number = +evt.currentTarget.value;
            setHealthFacilityType(_healthFacilityType);
            bindQuestions(respondentType, geoGraphiclevel, _healthFacilityType);
        }
    }

    //Renders the objectives tabs
    const renderObjectivesTab =
        filteredObjectives.map((objective: ObjectiveModel): WHOTab => {
            return { id: objective.id, label: objective.name };
        });

    //Renders the sub-objectives tabs
    const renderSubObjectivesTab =
        filteredSubObjectives.map((subObjective: SubObjectiveModel): WHOTab => {
            return { id: subObjective.id, label: `${subObjective.sequence} ${subObjective.name}` };
        });

    //Renders the  indicator tabs
    const renderIndicatorsTab = filteredIndicators.map(
        (indicator: IndicatorModel): TabModel =>
            new TabModel(indicator.id, `${indicator.sequence} ${indicator.name}`, <></>)
    );

    //Get the filtered objectives by respondentType 
    const filterObjective = (objectives: Array<ObjectiveModel>, subObjectives: Array<SubObjectiveModel>, indicators: Array<IndicatorModel>, respondentType: number) => {

        const filteredObjectives = objectives.filter(
            (objective: ObjectiveModel) =>
                objective.respondentType === respondentType
        );

        setFilteredObjectives(filteredObjectives);
        setObjectiveId(filteredObjectives[0]?.id);
        setCurrentObjectiveTabIndex(0);
        filterSubObjective(filteredObjectives[0]?.id, subObjectives, indicators, respondentType);
    }

    //Get the filtered sub-objective by objectives 
    const filterSubObjective = (objectiveId: string, subObjectives: Array<SubObjectiveModel>, indicators: Array<IndicatorModel>, respondentType: number) => {

        const filteredSubObjectives = subObjectives.filter(
            (subObjective: SubObjectiveModel) =>
                subObjective.objectiveId === objectiveId &&
                subObjective.respondentType === respondentType
        );

        setFilteredSubObjectives(filteredSubObjectives);

        if (filteredSubObjectives.length) {
            filterIndicators(filteredSubObjectives[0].id, indicators, respondentType);
            setSubObjectiveId(filteredSubObjectives[0].id);
        }

        setCurrentSubObjectiveTabIndex(0);
    }

    //Get the filtered indicators by sub-objectives   
    const filterIndicators = (subObjectiveId: string, indicators: Array<IndicatorModel>, respondentType: number) => {

        const filteredIndicators = indicators.filter(
            (indicator: IndicatorModel) =>
                indicator.subObjectiveId === subObjectiveId && indicator.respondentType === respondentType
        );
        if (filteredIndicators.length) {
            setFilteredIndicators(filteredIndicators);
            setIndicatorId(filteredIndicators[0].id);
            filterQuestions(filteredIndicators[0].id);
        }
        setCurrentIndicatorTabIndex(0);
    }

    //Get the filtered questions by indicatorId   
    const filterQuestions = (indicatorId: string) => {
        const filteredQuestions: Array<QuestionModel> = questions.filter(
            (question: QuestionModel) =>
                question.indicatorId === indicatorId
        );
        setFilteredQuestions(filteredQuestions);
    }

    // Triggers whenever user changes the objective tab
    const onObjectiveTabChange = (currentObjectiveTabIndex: number) => {
        const _objectivesId = filteredObjectives[currentObjectiveTabIndex].id;
        setCurrentObjectiveTabIndex(currentObjectiveTabIndex);
        setObjectiveId(_objectivesId);
        filterSubObjective(_objectivesId, subObjectives, indicators, respondentType);
    };

    // Triggers whenever user changes the sub-objective tab
    const onSubObjectiveTabChange = (currentSubObjectiveIndex: number) => {
        const _subObjectivesId = filteredSubObjectives[currentSubObjectiveIndex].id;
        setCurrentSubObjectiveTabIndex(currentSubObjectiveIndex);
        setSubObjectiveId(_subObjectivesId);
        filterIndicators(_subObjectivesId, indicators, respondentType);
    };

    // Triggers whenever user changes the indicator tab
    const onIndicatorTabChange = (event: React.ChangeEvent<{}>, newValue: any) => {
        const _indicatorId = filteredIndicators[newValue].id;
        setIndicatorId(_indicatorId);
        setCurrentIndicatorTabIndex(newValue);
        filterQuestions(_indicatorId);
    };

    // Triggers whenever user clicks on export button
    const onExportQuestions = () => {
        shellTableService.exportQuestions(assessmentId, respondentType, healthFacilityType);
    }

    return (
        <section className="page-full-section">
            <>
                {respondentTypes.length > 0 ? (
                    <>
                        <div>
                            <div className="row mt-3 mb-3">
                                <div className="col-sm-4">
                                    <Dropdown
                                        name="respondentType"
                                        id="respondentType"
                                        variant="outlined"
                                        size="small"
                                        label={t("Assessment.DataAnalysis.RespondentType")}
                                        options={respondentTypes.map((respondentType) => {
                                            return new MultiSelectModel(
                                                respondentType.key,
                                                respondentType.value,
                                                false,
                                                false
                                            );
                                        })}
                                        value={respondentType}
                                        onChange={onChangeRespondentType}
                                    />
                                </div>
                                <div className="col-sm-4">
                                    <Dropdown
                                        name="level"
                                        id="level"
                                        variant="outlined"
                                        size="small"
                                        label={t("Common.Level")}
                                        options={geoGraphicLevels.map((geoGraphic) => {
                                            return new MultiSelectModel(
                                                geoGraphic.key,
                                                geoGraphic.value,
                                                false,
                                                false
                                            );
                                        })}
                                        value={geoGraphiclevel}
                                        onChange={onChangeGeographicLevel}
                                    />
                                </div>
                                <div className={"col-sm-4 " + (showHealthFacilityType ? "" : "d-none")}>
                                    <Dropdown
                                        name="healthFacilityType"
                                        id="healthFacilityType"
                                        variant="outlined"
                                        size="small"
                                        label={t("Assessment.DataAnalysis.HealthFacilityType")}
                                        options={filterHealthFacilityTypes.map((healthFacility) => {
                                            return new MultiSelectModel(
                                                healthFacility.healthFacilityType,
                                                healthFacility.healthFacilityName,
                                                false,
                                                false
                                            );
                                        })}
                                        value={healthFacilityType}
                                        onChange={onChangeHealthFacilityType}
                                    />
                                </div>
                            </div>

                        </div>


                        <div className="app-tab-wrapper">
                            <Tabs
                                tabs={renderObjectivesTab}
                                activeTabIndex={currentObjectiveTabIndex}
                                onClick={onObjectiveTabChange}
                            >
                                <>
                                    <div className="category-wrapper mt-3">
                                        <div className="d-flex align-items-stretch">
                                            <div className="subcategories-wrapper">
                                                <Tabs
                                                    tabs={renderSubObjectivesTab}
                                                    activeTabIndex={currentSubObjectiveTabIndex}
                                                    onClick={onSubObjectiveTabChange}
                                                >
                                                    <></>
                                                </Tabs>
                                            </div>

                                            <div className="indicators-wrapper">
                                                <WHOTabs
                                                    tabs={renderIndicatorsTab}
                                                    scrollable={true}
                                                    value={currentIndicatorTabIndex}
                                                    onChange={onIndicatorTabChange}>
                                                    <>
                                                        <div className="py-2">
                                                            <ShellTableQuestionComponent shellTableQuestions={filteredQuestions} geoGraphiclevel={geoGraphiclevel} />
                                                        </div>
                                                    </>
                                                </WHOTabs>
                                            </div>
                                        </div>
                                    </div>
                                </>
                            </Tabs>
                            <div className="response-action-wrapper">
                                <div className="button-action-section d-flex justify-content-center p-3">
                                    <button className="btn app-btn-primary" onClick={onExportQuestions} >
                                        {t("Common.Export")}
                                    </button>
                                    <IconButton className="grid-icon-button">
                                        <Tooltip
                                            content={respondentType === RespondentType.SubnationalLevel ? t("Assessment.DataCollection.QuestionBankSurvey.ExportRawSurveyDataByDistrict")
                                                : t("Assessment.DataCollection.QuestionBankSurvey.ExportRawSurveyDataByHFC")
                                            }
                                            isHtml
                                        >
                                            <InfoIcon fontSize="small" />
                                        </Tooltip>
                                    </IconButton>
                                </div>

                            </div>
                        </div>
                    </>
                ) :
                    (<div className="d-flex h-400 justify-content-center align-items-center">
                        {t("Assessment.DataAnalysis.QuestionBankForDataAnalysis")}
                    </div>)
                }
            </>
        </section>
    )
}

export default ShellTableContainer;