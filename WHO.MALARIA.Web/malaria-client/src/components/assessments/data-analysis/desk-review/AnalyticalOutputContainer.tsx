﻿import { Icon<PERSON>utton } from "@mui/material";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate,  useLocation } from "react-router-dom";
import Breadcrumbs from "../../data-collection/desk-review/responses/Breadcrumbs";
import CancelIcon from "@mui/icons-material/Close";
import TabularReport from "./TabularReport";
import InteractiveTableReport from "./InteractiveTableReport";
import TableWithTabsContainer from "./TableWithTabsContainer";
import GraphReport from "./GraphReport";
import GraphWithTabsContainer from "./GraphWithTabsContainer";
import { AnalyticalOutputType } from "../../../../models/Enums";
import { analyticalOutputService } from "../../../../services/analyticalOutputService";
import { IndicatorReportRequestModel } from "../../../../models/DataAnalysis/DeskReview/IndicatorReportRequestModel";
import { IndicatorReportModel } from "../../../../models/DataAnalysis/DeskReview/IndicatorReportDetailsModel";
import LineGraphReport from "./LineGraphReport";
import { IndicatorSelectionRequestModel, IndicatorsRequestModel } from "../../../../models/RequestModels/AnalyticalOutputRequestModel";
import { UtilityHelper } from "../../../../utils/UtilityHelper";
import MultipleTableReport from "./MultipleTableReport";
import DownloadInProgressModal from "../../../controls/DownloadInProgressModal";

type ResponseValue = {
    key: string,
    value: number
};

/** Data analysis container which renders indicator components to be rendered*/
const AnalyticalOutputContainer = () => {
    const { t } = useTranslation();
    const location: any = useLocation();
    const navigate = useNavigate();
    const assessmentId = location?.state?.assessmentId;
    const indicatorId = location?.state?.indicatorId;
    const strategyId = location?.state?.selectedStrategyId;
    const assessmentIndicatorId: string = location?.state?.assessmentIndicatorId;
    const assessmentStrategyId: string = location?.state?.assessmentStrategyId;

    const indicatorName = location?.state?.indicatorName;
    const sequence = location?.state?.sequence;
    const [subObjectiveName, setSubObjectiveName] = useState<string>("");

    const [indicatorReportData, setIndicatorReportData] = useState<any>();
    const [indicatorReport, setIndicatorReport] = useState<IndicatorReportModel>();

    const [isFileDownloading, setIsFileDownloading] = useState<boolean>(false);

    const analyticalOutputIndicators: Array<IndicatorSelectionRequestModel> =
        [new IndicatorSelectionRequestModel(
            indicatorId,
            assessmentIndicatorId,
            assessmentStrategyId
        )];

    // Get assessment indicators response
    const getIndicatorsResponse = () => {

        analyticalOutputService
            .getIndicatorsResponse(
                new IndicatorReportRequestModel(
                    assessmentId,
                    assessmentIndicatorId,
                    assessmentStrategyId,
                    indicatorId,
                    strategyId
                )
            ).then((indicatorReport: IndicatorReportModel) => {

                const categories = (indicatorReport.response?.categories || []).map((category: string | null) => category ?? "");
                if (indicatorReport.response) {
                    indicatorReport.response.categories = categories;
                }
                if (indicatorReport.type === 3 && indicatorReport.response?.graphType === 1) {
                    indicatorReport.response.values.forEach(function (val: ResponseValue) {
                        val.key = t(`translation:Month:${val.key}`);
                    });
                }
                const indicatorData = indicatorReport.response;
                setIndicatorReportData(indicatorData);
                setIndicatorReport(indicatorReport);
            })
    }

    // Triggers whenever user clicks on generate report button
    const onGenerateReport = () => {

        setIsFileDownloading(true);

        analyticalOutputService
            .downloadReport(
                new IndicatorsRequestModel(
                    assessmentId,
                    strategyId,
                    analyticalOutputIndicators
                )
            )
            .then((response: any) => {
                UtilityHelper.download(response, "AnalyticalOutput.xlsx");
                setIsFileDownloading(false);
            }).catch((error: any) => {
                setIsFileDownloading(false);
            });
    }

    useEffect(() => {
        getIndicatorsResponse();
    }, []);

    useEffect(() => {
        setSubObjectiveName(location?.state?.subObjectiveName);
    }, [location?.state?.subObjectiveName]);

    //Triggers whenever user takes an action on close or back navigation click
    const onBackOrCloseButtonClick = () => {
        const state = location?.state;

        navigate("/assessment/data-analysis/desk-review", { state: {
            ...state
        } });
    };

    return (
        <>
            <section className="page-full-section page-full-assess-section">
                <div className="assess-header-section d-flex pt-2 pb-2">
                    <Breadcrumbs
                        parentLabel={subObjectiveName}
                        label={`${sequence} ${indicatorName}`}
                        onNavigate={onBackOrCloseButtonClick}
                    />
                    <div className="ml-auto">
                        <IconButton onClick={onBackOrCloseButtonClick} title="Close" className="close-modal">
                            <CancelIcon />
                        </IconButton>
                    </div>
                </div>

                <div className="page-response-section page-report-section">
                    <div className="response-wrapper h-500">
                        {(indicatorReport?.type === AnalyticalOutputType.InteractiveTable) &&
                            <InteractiveTableReport data={indicatorReportData} indicatorSequence={indicatorReport?.indicatorSequence} strategyId={strategyId} />
                        }
                        {indicatorReport?.type === AnalyticalOutputType.Table &&
                            <TabularReport data={indicatorReportData} indicatorSequence={indicatorReport?.indicatorSequence} strategyId={strategyId} />
                        }
                        {indicatorReport?.type === AnalyticalOutputType.TabWithTable &&
                            <TableWithTabsContainer data={indicatorReportData} />
                        }
                        {indicatorReport?.type === AnalyticalOutputType.Graph &&
                            <GraphReport data={indicatorReportData} key={indicatorReportData} />
                        }
                        {indicatorReport?.type === AnalyticalOutputType.TabWithGraph &&
                            <GraphWithTabsContainer data={indicatorReportData} key={indicatorReportData} />
                        }
                        {indicatorReport?.type === AnalyticalOutputType.MultiLineGraph &&
                            <LineGraphReport data={indicatorReportData} />
                        }
                        {indicatorReport?.type === AnalyticalOutputType.MultipleTable &&
                            <MultipleTableReport data={indicatorReportData} />
                        }
                    </div>
                    {
                        indicatorReport?.type !== AnalyticalOutputType.Graph &&
                        indicatorReport?.type !== AnalyticalOutputType.TabWithGraph &&
                        indicatorReport?.type !== AnalyticalOutputType.MultiLineGraph &&
                        <div className="response-action-wrapper">
                            <div className="button-action-section d-flex justify-content-center p-3">
                                <button className="btn app-btn-primary" onClick={onGenerateReport}>
                                    {t("Common.Export")}
                                </button>
                            </div>
                        </div>
                    }
                </div>

                <DownloadInProgressModal isFileDownloading={isFileDownloading} />
            </section>
        </>
    )
}

export default AnalyticalOutputContainer;