﻿import React from "react";
import { useTranslation } from "react-i18next";
import { TabularReportModel } from "../../../../models/DataAnalysis/DeskReview/IndicatorReportDetailsModel";
import TabularReport from "./TabularReport";

type TabularReportProps = {
    data: Array<TabularReportModel>;
};

/** Data analysis component which renders indicator (multiple table components) to be rendered*/
const MultipleTableReport = (props: TabularReportProps) => {
    const { t } = useTranslation();
    const { data } = props;

    // Renders multiple table data 
    const multipleTableData: any = (tabularReportData: Array<TabularReportModel>) => {
        const tabularReport = tabularReportData.map((tableData: TabularReportModel) =>
            <TabularReport data={tableData} />
        )

        return tabularReport;
    }

    return (
        <>
            {multipleTableData(data)}
        </>
    )
}
export default MultipleTableReport;