﻿import React from "react";
import { useTranslation } from "react-i18next";
import { useLocation } from "react-router-dom";
import { GraphReportModel } from "../../../../models/DataAnalysis/DeskReview/IndicatorReportDetailsModel";
import { GraphType } from "../../../../models/Enums";
import {
    Chart,
    ChartCategoryAxis,
    ChartCategoryAxisItem,
    ChartCategoryAxisTitle,
    ChartProps,
    ChartSeries,
    ChartSeriesItem,
    ChartTitle,
    ChartValueAxis,
    ChartValueAxisItem,
    exportVisual
} from "@progress/kendo-react-charts";
import { exportImage } from "@progress/kendo-drawing";
import { saveAs } from "@progress/kendo-file-saver";
import { KeyValuePair } from "../../../../models/DeskReview/KeyValueType";

type GraphReportProps = {
    data: GraphReportModel;
};

/** Data analysis component which renders indicators (graph components) to be rendered*/
const GraphReport = (props: GraphReportProps) => {
    const { t } = useTranslation();
    const { data } = props;
    const location: any = useLocation();
    const sequence = location?.state?.sequence;
    let refContainer: any = React.useRef(null);
    const graphName = data.graphName ? `_${data.graphName }` : "";

    // Get the chart category name to show on the chart
    const chartCategoryName = () => {
        return data.values.map((item: KeyValuePair<string | null, number | null>) => item.key)
    }

    // Triggers whenever user clicks on generate/export report button
    const onGenerateReport = () => {
        const chartVisual = exportVisual(refContainer);
        if (chartVisual) {
            exportImage(chartVisual).then((dataURI) => saveAs(dataURI,
                `${t("Common.Indicator")}_${sequence}${graphName}.png`));
        }
    };

    return (
        <>
            <section className="page-graph-section">
                {data.graphType === GraphType.Line &&
                    <>
                        <Chart {...props} ref={(chart) => (refContainer = chart)}>
                            <ChartTitle text={data?.graphTitle} />
                            <ChartValueAxis>
                                <ChartValueAxisItem
                                    min={0} max={100} />
                            </ChartValueAxis>
                            <ChartCategoryAxis>
                                <ChartCategoryAxisItem
                                    title={{ text: data?.xAxis }}
                                    categories={chartCategoryName()}
                                />
                            </ChartCategoryAxis>
                            <ChartSeries>
                                <ChartSeriesItem type="line" data={data.values} />
                            </ChartSeries>
                        </Chart>
                    </>
                }

                {data.graphType === GraphType.Bar &&
                    <>
                        <Chart {...props} ref={(chart) => (refContainer = chart)}>
                            <ChartTitle text={data?.graphTitle} />
                            <ChartValueAxis>
                                <ChartValueAxisItem
                                    min={0} max={100} />
                            </ChartValueAxis>
                            <ChartCategoryAxis>
                                <ChartCategoryAxisItem categories={chartCategoryName()}>
                                    <ChartCategoryAxisTitle text={data?.xAxis} />
                                </ChartCategoryAxisItem>
                            </ChartCategoryAxis>
                            <ChartSeries>
                                <ChartSeriesItem
                                    type="column"
                                    gap={2}
                                    spacing={0.1}
                                    data={data.values}
                                />
                            </ChartSeries>
                        </Chart>
                    </>
                }
            </section>

            <div className="response-action-wrapper">
                <div className="button-action-section d-flex justify-content-center p-3">
                    <button className="btn app-btn-primary" onClick={onGenerateReport}>
                        {t("Common.Export")}
                    </button>
                </div>
            </div>
        </>
    )
}
export default GraphReport;