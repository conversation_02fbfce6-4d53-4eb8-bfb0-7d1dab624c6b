﻿import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { TabularReportModel } from "../../../../models/DataAnalysis/DeskReview/IndicatorReportDetailsModel";
import WHOTabs from "../../../controls/WHOTabs";
import { TabModel } from "../../../../models/TabModel";
import TabularReport from "./TabularReport";

type TableWithTabsProps = {
    data: Array<TabularReportModel>;
};

/** Data analysis container which renders indicators (table with tabs components) to be rendered*/
const TableWithTabsContainer = (props: TableWithTabsProps) => {
    const { t } = useTranslation();
    const { data } = props;
    const [currentTab, setCurrentTab] = useState<number>(0);

    // Triggers whenever tab is changed
    const onTabChange = (event: React.ChangeEvent<{}>, tabIndex: number) => {
        setCurrentTab(tabIndex);
    };

    // Prepare table data
    const prepareTabularReportData: any = (index: number) => {
        const tabTableData: TabularReportModel = new TabularReportModel(
            data[index]?.rows,
            data[index]?.columns,
            data[index]?.hasCalculation,
            data[index]?.tabName,
            data[index]?.tabId,
        )

        return tabTableData;
    }

    // Generate dynamic tab's component
    const renderTabComponent: Array<TabModel> = data?.map((tab: TabularReportModel) => {
        return (new TabModel(
            tab.tabId,
            tab.tabName,
            <TabularReport data={prepareTabularReportData(tab.tabId)} />
        ))
    });

    return (
        <>
            <div className="app-tab-wrapper">
                <WHOTabs
                    tabs={renderTabComponent}
                    value={currentTab}
                    onChange={onTabChange}
                    scrollable={false}
                >
                    <div className="p-2"> {renderTabComponent[currentTab].children} </div>
                </WHOTabs>
            </div>
        </>
    )
}
export default TableWithTabsContainer;