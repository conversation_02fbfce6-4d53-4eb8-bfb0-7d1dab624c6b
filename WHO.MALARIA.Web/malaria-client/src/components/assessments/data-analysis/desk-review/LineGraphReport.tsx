﻿import React from "react";
import {
    Chart,
    ChartSeries,
    ChartSeriesItem,
    ChartCategoryAxis,
    ChartCategoryAxisItem,
    ChartTitle,
    ChartLegend,
    exportVisual,
} from "@progress/kendo-react-charts";
import { useTranslation } from "react-i18next";
import { useLocation } from "react-router-dom";
import { LineGraphReportModel } from "../../../../models/DataAnalysis/DeskReview/IndicatorReportDetailsModel";
import { exportImage } from "@progress/kendo-drawing";
import { saveAs } from "@progress/kendo-file-saver";

type LineGraphReportProps = {
    data: LineGraphReportModel;
};

/** View data analysis container which renders indicator components to be rendered*/
const LineGraphReport = (props: LineGraphReportProps) => {
    const { t } = useTranslation();
    const location: any = useLocation();
    const sequence = location?.state?.sequence;
    const { data } = props;
    let refContainer: any = React.useRef(null);
    const graphName = data.graphName ? `_${data.graphName}` : "";

    // Triggers whenever user clicks on generate/export report button
    const onGenerateReport = () => {
        const chartVisual = exportVisual(refContainer);
        if (chartVisual) {
            exportImage(chartVisual).then((dataURI) => saveAs(dataURI,
                `${t("Common.Indicator")}_${sequence}${graphName}.png`));
        }
    };

    return (
        <>
            <section className="page-full-section">
                <Chart
                    style={{
                        height: 350,
                    }}
                    ref={(chart) => (refContainer = chart)}
                >
                    <ChartTitle text={data?.graphTitle} />
                    <ChartLegend position="top" orientation="horizontal" />
                    <ChartCategoryAxis>
                        <ChartCategoryAxisItem
                            title={{ text: data?.xAxis }}
                            categories={data.categories}
                            startAngle={45}
                        />
                    </ChartCategoryAxis>
                    <ChartSeries>
                        {data?.values?.map((item, idx) => (
                            <ChartSeriesItem
                                key={idx}
                                type="line"
                                tooltip={{
                                    visible: true,
                                }}
                                data={item.value}
                                name={item.key}
                            />
                        ))}
                    </ChartSeries>
                </Chart>
            </section>
            <div className="response-action-wrapper">
                <div className="button-action-section d-flex justify-content-center p-3">
                    <button className="btn app-btn-primary" onClick={onGenerateReport}>
                        {t("Common.Export")}
                    </button>
                </div>
            </div>
        </>
    )
}

export default LineGraphReport;