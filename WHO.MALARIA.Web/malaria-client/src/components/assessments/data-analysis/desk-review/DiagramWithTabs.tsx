﻿import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { DiagramModel } from "../../../../models/DataAnalysis/DeskReview/IndicatorReportDetailsModel";
import WHOTabs from "../../../controls/WHOTabs";
import { TabModel } from "../../../../models/TabModel";
import ImageComponent from "../../../controls/ImageComponent"

type DiagramWithTabsProps = {
    diagramDetails: Array<DiagramModel>;
};

/** Data analysis component which renders indicators (diagram with tabs components) to be rendered*/
const DiagramWithTabs = (props: DiagramWithTabsProps) => {
    const { t } = useTranslation();
    const { diagramDetails } = props;
    const [currentTab, setCurrentTab] = useState<number>(0);

    // Triggers whenever tab is changed
    const onTabChange = (event: React.ChangeEvent<{}>, tabIndex: number) => {
        setCurrentTab(tabIndex);
    };
   
    // Generate dynamic tab's component
    const renderTabComponent: Array<TabModel> = diagramDetails?.map((tab: DiagramModel) => {
        return (new TabModel(
            tab.order - 1,
            `${t("Assessment.DataCollection.DeskReviewDiagram.Diagram")} ${tab.order}`,
            <ImageComponent file={diagramDetails[tab.order - 1]?.file} extension={diagramDetails[tab.order - 1]?.extension} key={tab.order} />
        ))
    });

    return (
        <>
            <div className="app-tab-wrapper">
                <WHOTabs
                    tabs={renderTabComponent}
                    value={currentTab}
                    onChange={onTabChange}
                    scrollable={false}
                >
                    <> {renderTabComponent[currentTab]?.children} </>
                </WHOTabs>
            </div>
        </>
    )
}
export default DiagramWithTabs;