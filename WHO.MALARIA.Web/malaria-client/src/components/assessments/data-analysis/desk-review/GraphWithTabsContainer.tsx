﻿import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { GraphReportModel } from "../../../../models/DataAnalysis/DeskReview/IndicatorReportDetailsModel";
import WHOTabs from "../../../controls/WHOTabs";
import { TabModel } from "../../../../models/TabModel";
import GraphReport from "./GraphReport";

type GraphReportWithTabProps = {
    data: Array<GraphReportModel>;
};

/** Data analysis component which renders indicators (graph with tabs components) to be rendered*/
const GraphWithTabsContainer = (props: GraphReportWithTabProps) => {
    const { t } = useTranslation();
    const { data } = props;
    const [currentTab, setCurrentTab] = useState<number>(0);

    // Triggers whenever tab is changed
    const onTabChange = (event: React.ChangeEvent<{}>, tabIndex: number) => {
        setCurrentTab(tabIndex);
    };

    // Prepare graph data 
    const prepareGraphReportData: any = (index: number) => {        
        const tabGraphData: GraphReportModel = new GraphReportModel(
            data[index]?.values,
            data[index]?.xAxis,
            data[index]?.yAxis,
            data[index]?.graphTitle,
            data[index]?.tabName,
            data[index]?.tabId,
            data[index]?.graphName,
            data[index]?.graphType
        )

        return tabGraphData;
    }

    // Generate dynamic tab's component    
    const renderTabComponent: Array<TabModel> = data?.map((tab: GraphReportModel) => {
        return (new TabModel(
            tab.tabId,
            tab.tabName,
            <GraphReport data={prepareGraphReportData(tab.tabId)} key={tab.tabId} />
        ))
    });

    return (
        <section className="page-graph-section" id={`${Math.random()}`}>
            <>
                <div className="app-tab-wrapper">
                    <WHOTabs
                        tabs={renderTabComponent}
                        value={currentTab}
                        onChange={onTabChange}
                        scrollable={false}
                    >
                        <div className="p-2">{renderTabComponent[currentTab].children}</div>
                    </WHOTabs>
                </div>
            </>
        </section>
    )
}
export default GraphWithTabsContainer;