﻿import { IconButton } from "@mui/material";
import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate,  useLocation } from "react-router-dom";
import Breadcrumbs from "../../data-collection/desk-review/responses/Breadcrumbs";
import CancelIcon from "@mui/icons-material/Close";
import DiagramWithTabs from "./DiagramWithTabs";
import { analyticalOutputService } from "../../../../services/analyticalOutputService";
import { DiagramModel } from "../../../../models/DataAnalysis/DeskReview/IndicatorReportDetailsModel";
import { Constants } from "../../../../models/Constants";
import { assessmentService } from "../../../../services/assessmentService";

/** Data analysis container which renders uploaded diagram for objective/sub-objective components to be rendered*/
const AnalyticalOutputDiagramContainer = () => {
    const { t } = useTranslation();
    const location: any = useLocation();
    const navigate = useNavigate();
    const assessmentId = location?.state?.assessmentId;
    const strategyId = location?.state?.selectedStrategyId;
    const diagramFor = location?.state?.diagramFor;

    const [subObjectiveName, setSubObjectiveName] = useState<string>("");
    const [diagrams, setDiagrams] = useState<Array<DiagramModel>>();

    useEffect(() => {
        if (diagramFor === Constants.Diagrams.Objective) {
            analyticalOutputService
                .getObjectiveUploadedDiagrams(assessmentId, strategyId)
                .then((diagrams: Array<DiagramModel>) => {
                    setDiagrams(diagrams);
                })
        } else if (diagramFor === Constants.Diagrams.SubObjective) {
            assessmentService
                .getSubObjectiveDiagrams(assessmentId, strategyId)
                .then((diagrams: Array<DiagramModel>) => {
                    setDiagrams(diagrams);
                })
        }
    }, []);

    useEffect(() => {
        setSubObjectiveName(location?.state?.subObjectiveName);
    }, [location?.state?.subObjectiveName]);

    //Triggers whenever user takes an action on close or back navigation click
    const onBackOrCloseButtonClick = () => {
        const state = location?.state;

        navigate("/assessment/data-analysis/desk-review", { state: {
            ...state
        } });
    };

    // Triggers whenever user clicks on download uploaded diagram button
    const onDownloadDiagram = () => {
        if (diagramFor === Constants.Diagrams.Objective) {
            analyticalOutputService
                .downloadObjectiveDiagrams(assessmentId, strategyId);
        } else if (diagramFor === Constants.Diagrams.SubObjective) {
            analyticalOutputService
                .downloadSubObjectiveDiagrams(assessmentId, strategyId);
        }
    }

    return (
        <>
            <section className="page-full-section page-full-assess-section">
                <div className="assess-header-section d-flex pt-2 pb-2">
                    {diagramFor === Constants.Diagrams.Objective &&
                        <Breadcrumbs
                            parentLabel={t("Assessment.DataCollection.DeskReviewDiagram.TechnicalProcess")}
                            label={t("Assessment.DataCollection.DeskReviewDiagram.UploadDiagram")}
                            onNavigate={onBackOrCloseButtonClick}
                        />
                    }
                    {diagramFor === Constants.Diagrams.SubObjective &&
                        <Breadcrumbs
                            parentLabel={t("Assessment.DataCollection.DeskReviewDiagram.InformationSystem")}
                            label={t("Assessment.DataCollection.DeskReviewDiagram.UploadDiagram")}
                            onNavigate={onBackOrCloseButtonClick}
                        />
                    }

                    <div className="ml-auto">
                        <IconButton onClick={onBackOrCloseButtonClick} title="Close" className="close-modal">
                            <CancelIcon />
                        </IconButton>
                    </div>
                </div>

                <div className="page-response-section page-report-section">
                    <div className="response-wrapper h-400">
                        {
                            diagrams &&
                            <DiagramWithTabs diagramDetails={diagrams} />
                        }
                    </div>

                    <div className="response-action-wrapper">
                        <div className="button-action-section d-flex justify-content-center p-3">
                            <button className="btn app-btn-primary" onClick={onDownloadDiagram}>
                                {t("Common.Export")}
                            </button>
                        </div>
                    </div>
                </div>
            </section>
        </>
    )
}

export default AnalyticalOutputDiagramContainer;