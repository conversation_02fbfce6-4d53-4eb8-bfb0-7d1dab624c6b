﻿import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import WHOTabs from "../../controls/WHOTabs";
import { TabModel } from "../../../models/TabModel";
import DQAContainer from "./data-quality-assessment/DQAContainer";
import { useNavigate,  useLocation } from "react-router-dom";
import { AssessmentApproach, DataCollectionStepper, StrategiesEnum } from "../../../models/Enums";
import DeskReviewContainer from "./desk-review/DeskReviewContainer";
import ShellTableContainer from "./shell-table/ShellTableContainer";

/** Data Analysis Container which renders steppers and other components */
const DataAnalysisContainer = () => {
    const { t } = useTranslation();
    document.title = t("app.DataAnalysis");

    const navigate = useNavigate();
    const location: any = useLocation();
    const state = location?.state;
    const assessmentId = location?.state?.assessmentId;
    const strategyId = location?.state?.strategyId;
    const subTabIndex = location?.state?.subTabIndex;
    const approach = location?.state?.approach;

    const [currentTab, setCurrentTab] = useState<number>(0);

    // Triggers whenever tab is changed
    const onTabChange = (event: React.ChangeEvent<{}>, tabIndex: number) => {
        setCurrentTab(tabIndex);
        onNavigate(tabIndex);
    };

    // Helps in redirection to other route of data collection based on the tab index 
    const onNavigate = (currentTabIndex: number) => {
        let url: string = "";
        switch (currentTabIndex) {
            case DataCollectionStepper.DeskReview:
                url = "/assessment/data-analysis/desk-review";
                break;
            case DataCollectionStepper.QuestionBank:
                url = "/assessment/data-analysis/question-bank";
                break;
            case DataCollectionStepper.DQA:
                url = "/assessment/data-analysis/dqa";
                break;
        }

        navigate(url, { state: {
            ...state,
            subTabIndex: currentTabIndex
        } });
    };

    // Renders tabs for Data Quality Assessment
    const dataAnalysisTabs: Array<TabModel> = approach !== AssessmentApproach.Rapid ? [
        new TabModel(
            DataCollectionStepper.DeskReview,
            t("Assessment.DataCollection.DeskReview"),
            <DeskReviewContainer />
        ),
        new TabModel(
            DataCollectionStepper.DQA,
            t("Assessment.DataCollection.DQA"),
            <DQAContainer
                strategyId={strategyId}
                assessmentId={assessmentId}
            />
        ),
        new TabModel(
            DataCollectionStepper.QuestionBank,
            t("Assessment.DataCollection.SurveyResult"),
            <ShellTableContainer />
        ),
    ] : [
        new TabModel(
            DataCollectionStepper.DeskReview,
            t("Assessment.DataCollection.DeskReview"),
            <DeskReviewContainer />
        ),
        new TabModel(
            DataCollectionStepper.DQA,
            t("Assessment.DataCollection.DQA"),
            <DQAContainer
                strategyId={strategyId}
                assessmentId={assessmentId}
            />
        )
    ];

    useEffect(() => {
        if (subTabIndex > 0) {
            onNavigate(subTabIndex);
            setCurrentTab(subTabIndex);
        }
    }, []);

    return (
        <section className="page-full-section">
            <>
                <div className="app-tab-wrapper">
                    <WHOTabs
                        tabs={dataAnalysisTabs}
                        value={currentTab}
                        onChange={onTabChange}
                        scrollable={false}
                    >
                        <> {dataAnalysisTabs[currentTab].children} </>
                    </WHOTabs>
                </div>
            </>
        </section>
    )
}

export default DataAnalysisContainer;