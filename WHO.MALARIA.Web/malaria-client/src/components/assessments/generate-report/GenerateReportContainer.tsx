import React, { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next';
import { useNavigate,  useLocation } from "react-router-dom";
import { ReportGenerationStepper } from '../../../models/Enums';
import { CanScoreCardBeGenerated, FinalizeScoreCardModel } from '../../../models/ScoreCard/ScoreCardModel';
import { TabModel } from '../../../models/TabModel';
import { scoreCardService } from '../../../services/scoreCardService';
import WHOTabs from '../../controls/WHOTabs';
import GenerateReport from './GenerateReport';
import ScoreCardComponent from "./ScoreCardComponent";

/** Report Generation Container which renders steppers and other components */
const GenerateReportContainer = () => {
    const { t } = useTranslation();
    document.title = t("app.GenerateReport");

    const navigate = useNavigate();
    const location: any = useLocation();
    const state = location?.state;
    const subTabIndex = location?.state?.subTabIndex;
    const assessmentId = location?.state?.assessmentId;
    const [currentTab, setCurrentTab] = useState<number>(0);

    // Triggers whenever tab is changed
    const onTabChange = (event: React.ChangeEvent<{}>, tabIndex: number) => {
        setCurrentTab(tabIndex);
        onNavigate(tabIndex);
    };

    // Helps in redirection to other route of report generation based on the tab index 
    const onNavigate = (currentTabIndex: number) => {
        let url: string = "";
        switch (currentTabIndex) {
            case ReportGenerationStepper.Report:
                url = "/assessment/report-generation/report";
                break;

            case ReportGenerationStepper.ScoreCard:
                url = "/assessment/report-generation/score-card";
                break;
        }

        navigate(url, { state: {
            ...state,
            subTabIndex: currentTabIndex
        } });
    };

    // Renders tabs for Only Report Generation
    const reportGenerationTabs: Array<TabModel> = [
        new TabModel(
            ReportGenerationStepper.ScoreCard,
            t("Assessment.DataCollection.ScoreCard"),
            <ScoreCardComponent />
        ),
        new TabModel(
            ReportGenerationStepper.Report,
            t("Common.Report"),
            <GenerateReport />
        )
    ]

    useEffect(() => {
        if (subTabIndex > 0) {
            onNavigate(subTabIndex);
            setCurrentTab(subTabIndex);
        }
    }, []);

    return (
        <section className="page-full-section">
            <>
                <div className="app-tab-wrapper">
                    <WHOTabs
                        tabs={reportGenerationTabs}
                        value={currentTab}
                        onChange={onTabChange}
                        scrollable={false}
                    >
                        <> {reportGenerationTabs[currentTab].children} </>
                    </WHOTabs>
                </div>
            </>
        </section>
    )
}

export default GenerateReportContainer;
