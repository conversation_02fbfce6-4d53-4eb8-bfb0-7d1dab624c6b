﻿import React, { useEffect, useState } from "react";
import { useNavigate,  useLocation } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { SubObjectiveIndicatorModel, IndicatorModel, SubObjectiveModel, ObjectiveModel, ScoreCardRequestModel, FinalizeScoreCardModel, ScoreCardDetailModel } from "../../../models/ScoreCard/ScoreCardModel";
import Tabs, { WHOTab } from "../../controls/Tabs";
import { scoreCardService } from "../../../services/scoreCardService";
import Table from "../data-collection/desk-review/responses/Table";
import TableBody from "../data-collection/desk-review/responses/TableBody";
import TableRow from "../data-collection/desk-review/responses/TableRow";
import TableCell from "../data-collection/desk-review/responses/TableCell";
import Dropdown from "../../controls/Dropdown";
import { AssessmentApproach, AssessmentStatus, DialogAction, MetNotMetStatus } from "../../../models/Enums";
import MultiSelectModel from "../../../models/MultiSelectModel";
import { KeyValuePair } from "../../../models/DeskReview/KeyValueType";
import { UserAssessmentPermission } from "../../../models/PermissionModel";
import { useSelector } from "react-redux";
import ConfirmationDialog from "../../common/ConfirmationDialog";
import ScoreCardValidationRules from "./ScoreCardValidationRules";
import useFormValidation from "../../common/useFormValidation";
import { UtilityHelper } from "../../../utils/UtilityHelper";
import DownloadInProgressModal from "../../controls/DownloadInProgressModal";
import { Objective } from "../data-collection/desk-review/responses/useIndicatorResponseLoader";
import TextBox from "../../controls/TextBox";
import parse from "html-react-parser";

/** Renders the generated score card */
const ScoreCardComponent = () => {
    const { t } = useTranslation();
    document.title = t("app.ScoreCardTitle");

    const location: any = useLocation();
    const navigate = useNavigate();
    const state = location?.state;
    const assessmentId = location?.state?.assessmentId;
    const [metNotMetIndicatorStatus, setMetNotMetIndicatorStatus] = useState<Array<KeyValuePair<number, string>>>([]);
    const [metNotMetStatus, setMetNotMetStatus] = useState<Array<KeyValuePair<string, string>>>([]);

    const [currentObjectiveTabIndex, setCurrentObjectiveTabIndex] = useState<number>(0);
    const [currentSubObjectiveIndex, setCurrentSubObjectiveIndex] = useState<number>(0);
    const [objectives, setObjectives] = useState<Array<ObjectiveModel>>([]);
    const [filteredObjectives, setFilteredObjectives] = useState<Array<ObjectiveModel>>([]);

    const [subObjectiveId, setSubObjectiveId] = useState("");
    const [objectiveId, setObjectiveId] = useState("");
    const [indicatorId, setIndicatorId] = useState("");
    const [subObjectives, setSubObjectives] = useState<Array<SubObjectiveModel>>([]);
    const [filteredSubObjectives, setfilteredSubObjectives] = useState<Array<SubObjectiveModel>>([]);
    const [indicators, setIndicators] = useState<Array<IndicatorModel>>([]);
    const [filteredIndicators, setfilteredIndicators] = useState<Array<IndicatorModel>>([]);
    const [isSaved, setSaved] = useState<boolean>(false);
    const [isFinalized, setFinalize] = useState<boolean>(false);
    const [scoreCardTab, setScoreCardTab] = useState<boolean>(false);
    const [showActionButtons, setActionButtons] = useState<boolean>(false);
    const [isFileDownloading, setIsFileDownloading] = useState<boolean>(false);

    const [openFinalizeConfirmation, setOpenFinalizeConfirmation] = useState<boolean>(false);
    const userPermission: UserAssessmentPermission = useSelector((state: any) => state.userPermission.assessment);
    const { canExportScoreCard, canSaveScoreCard, canFinalizeScoreCard } = userPermission;
    const validate = useFormValidation(ScoreCardValidationRules);
    const errors = useSelector((state: any) => state.error);

    const assessmentStatus = state?.status;
    const approachId = location?.state?.approach;

    const [scoreCardDetail, setScoreCardDetail] = useState<Array<ScoreCardDetailModel>>([]);

    // check score data is saved or not
    const hasScoreCardData = (assessmentId: string) => {
        scoreCardService.hasScoreCardData(assessmentId)
            .then((response) => {
                setSaved(
                    response as boolean
                );
            });
    }

    //Triggers if selected assessment is published. If it is published then show score card unless keep Score card tab hidden.
    const canScoreCardBeGenerated = (assessmentId: string) => {
        scoreCardService.canScoreCardBeGenerated(assessmentId)
            .then((response) => {
                if (response) {
                    setScoreCardTab(
                        response as boolean
                    );
                }
            });
    }

    //Trigger if all assessment survey indicators are true. Show action buttons only if isSurveyIndicator property is true.
    const showOrHideActionButtons = (indicators: Array<IndicatorModel>) => {
        const showButtons = indicators.some((indicator) => {
            return indicator.isSurveyIndicator === true;
        });
        setActionButtons(showButtons);
    }

    const currentlanguage = UtilityHelper.getCookieValue("i18next");

    useEffect(() => {
        canScoreCardBeGenerated(assessmentId);
        hasScoreCardData(assessmentId);
    }, [currentlanguage]);

    // bind objective sub-objectives and indicators based on assessmentId
    const bindObjectivesSubObjectivesIndicators = () => {
        scoreCardService
            .getScoreCardSurvey(assessmentId)
            .then((response: SubObjectiveIndicatorModel) => {
                setObjectives(response?.objectives);
                setSubObjectives(response.subObjectives);
                setIndicators(response.indicators);
                filterObjective(response.objectives, response.subObjectives, response.indicators);
                showOrHideActionButtons(response.indicators);
                setFinalize(response?.isFinalized);
            });
    };

    // get the filtered objectives by respondent Type 
    const filterObjective = (objectives: Array<ObjectiveModel>, subObjectives: Array<SubObjectiveModel>, indicators: Array<IndicatorModel>) => {
        setFilteredObjectives(objectives);
        setObjectiveId(objectives[currentObjectiveTabIndex]?.id);
        setCurrentObjectiveTabIndex(currentObjectiveTabIndex);
        filterSubObjective(objectives[currentObjectiveTabIndex]?.id, subObjectives, indicators, currentSubObjectiveIndex);
    }

    // get the filtered sub-objective by objectives 
    const filterSubObjective = (objectiveId: string, subObjectives: Array<SubObjectiveModel>, indicators: Array<IndicatorModel>, subObjectivesIndex: number) => {
        const filteredSubObjectives = subObjectives.filter(
            (subObjective: SubObjectiveModel) =>
                //indicator 1.3.7 should not be shown on score board so filtering out.
                subObjective.objectiveId === objectiveId && objectiveId !== Objective.Objective1.Indicator_1_3_7
        );

        setfilteredSubObjectives(filteredSubObjectives);

        if (filteredSubObjectives.length) {
            filterIndicators(filteredSubObjectives[subObjectivesIndex].id, indicators);
            setSubObjectiveId(filteredSubObjectives[subObjectivesIndex].id);
        }

        setCurrentSubObjectiveIndex(subObjectivesIndex);
    }

    // get the filtered indicators by sub-objective   
    const filterIndicators = (subObjectiveId: string, indicators: Array<IndicatorModel>) => {
        const filteredIndicators = indicators.filter(
            (indicator: IndicatorModel) =>
                indicator.subObjectiveId === subObjectiveId
        );

        setfilteredIndicators(filteredIndicators);
        setIndicatorId(filteredIndicators[0].id);
    }

    // triggers whenever user changes the objective tab
    const onTabObjectiveChange = (currentTabIndex: number) => {
        errors["surveyMetNotMetStatus"] = "";
        setObjectiveId(filteredObjectives[currentTabIndex].id);
        setCurrentObjectiveTabIndex(currentTabIndex);

        filterSubObjective(filteredObjectives[currentTabIndex]?.id, subObjectives, indicators, 0);
    };

    // triggers whenever user changes the sub-objective tab
    const onTabSubObjectiveChange = (currentSubObjectiveIndex: number) => {
        errors["surveyMetNotMetStatus"] = "";
        const _subObjectivesId = filteredSubObjectives[currentSubObjectiveIndex].id

        setCurrentSubObjectiveIndex(currentSubObjectiveIndex);
        setSubObjectiveId(_subObjectivesId);
        filterIndicators(_subObjectivesId, indicators);
    };

    // renders the objectives tabs
    const renderObjectivesTab =
        filteredObjectives.map((objective: ObjectiveModel): WHOTab => {
            return { id: objective.id, label: objective.name };
        });

    // renders the sub-objectives tabs
    const renderSubObjectivesTab =
        filteredSubObjectives.map((subObjective: SubObjectiveModel): WHOTab => {
            return { id: subObjective.id, label: `${subObjective.sequence} ${subObjective.name}` };
        });

    useEffect(() => {
        bindMetNotMetDropdown();
        bindObjectivesSubObjectivesIndicators();
    }, [currentlanguage]);

    //Bind indicator's met/not met status dropdown
    const bindMetNotMetDropdown = () => {
        const metNotmetValues: Array<KeyValuePair<number, string>> =
            [
                { key: MetNotMetStatus.Met, value: t("Common.Met") },
                { key: MetNotMetStatus.NotMet, value: t("Common.NotMet") },
                { key: MetNotMetStatus.PartiallyMet, value: t("Common.PartiallyMet") },
                { key: MetNotMetStatus.NotAssessed, value: t("Common.NotAssessed") },
            ];

        setMetNotMetIndicatorStatus(metNotmetValues);
    }

    // Hide survey score column for Rapid assessment approach
    const getSurveyColumn = (indicator: IndicatorModel) => {
        if (indicator.isSurveyIndicator == true && approachId !== AssessmentApproach.Rapid) {
            return (
                <TableCell align="center">

                    <Dropdown
                        style={{ "width": "auto" }}
                        name="surveyMetNotMetStatus"
                        id="surveyMetNotMetStatus"
                        size="small"
                        options={metNotMetIndicatorStatus.map((metNotMetIndicatorStatus) => {
                            return new MultiSelectModel(
                                metNotMetIndicatorStatus.key,
                                metNotMetIndicatorStatus.value,
                                false,
                                false
                            );
                        })}
                        variant="outlined"
                        value={indicator?.surveyMetNotMetStatus}
                        onChange={(evt: React.ChangeEvent<HTMLInputElement>) => {
                            onDropdownChange(evt, indicator?.id, indicator?.reasonForResult, indicator?.recommendation);
                        }}
                        disabled={isFinalized}
                        error={(indicator.isSurveyIndicator == true && indicator.surveyMetNotMetStatus == null && indicator.subObjectiveId == subObjectiveId) ? errors["surveyMetNotMetStatus"] && errors["surveyMetNotMetStatus"] : ""}
                        helperText={(indicator.isSurveyIndicator == true && indicator.surveyMetNotMetStatus == null && indicator.subObjectiveId == subObjectiveId) ? errors["surveyMetNotMetStatus"] && errors["surveyMetNotMetStatus"] : ""}
                    />
                </TableCell>
            )
        } else if (approachId === AssessmentApproach.Rapid) {
            return (
                <></>
            );
        } else {
            return (
                <TableCell>{ }</TableCell>
            );
        }
    }

    // Triggers whenever user tries to modify the value of indicator's met status
    const onDropdownChange = (evt: React.ChangeEvent<HTMLInputElement>, indicatorId: string, reasonForResult: string, recommendation: string) => {
        let value = evt?.currentTarget?.value;
        if (value) {
            if (!scoreCardDetail.find(t => t.indicatorId == indicatorId)) {
                let surveyMetNotMetStatus: ScoreCardDetailModel = {
                    indicatorId: indicatorId,
                    subObjectiveId: subObjectiveId,
                    indicatorMetNotMetStatus: parseInt(evt.target.value),
                    indicatorReasonForResult: reasonForResult,
                    indicatorRecommendation: recommendation
                };
                setScoreCardDetail([...scoreCardDetail, surveyMetNotMetStatus]);
            }
            else {
                let stateSurveyMetNotMetStatus = scoreCardDetail.find((indicator: ScoreCardDetailModel) => indicator.indicatorId === indicatorId);
                if (stateSurveyMetNotMetStatus) {
                    let surveyMetNotMetStatus: ScoreCardDetailModel = {
                        indicatorId: stateSurveyMetNotMetStatus.indicatorId,
                        subObjectiveId: subObjectiveId,
                        indicatorMetNotMetStatus: parseInt(evt.target.value),
                        indicatorReasonForResult: stateSurveyMetNotMetStatus.indicatorReasonForResult,
                        indicatorRecommendation: stateSurveyMetNotMetStatus.indicatorRecommendation
                    };
                    let index = scoreCardDetail.findIndex(p => p.indicatorId == indicatorId);
                    scoreCardDetail.splice(index, 1);
                    setScoreCardDetail([...scoreCardDetail, surveyMetNotMetStatus]);
                }
            }

            setIndicators((prevState: Array<IndicatorModel>) => {
                const indicators = [...prevState];
                const indicator = indicators.find((indicator: IndicatorModel) => indicator.id === indicatorId);

                if (indicator) {
                    indicator.surveyMetNotMetStatus = +value;
                }
                return indicators;
            });
        }
    }

    // Triggers whenever user clicks on generate score card button
    const onGenerateTemplate = () => {

        const downloadedTemplateName = t("Assessment.DataCollection.ScoreCard");

        scoreCardService
            .generateScoreCardTemplate(assessmentId).then((response: any) => {
                UtilityHelper.download(response, downloadedTemplateName);
                setIsFileDownloading(false);
            }).catch((error: any) => {
                setIsFileDownloading(false);
            });
    }

    // Triggered whenever finalized button is clicked
    const onFinalizeConfirmationBtnClick = (action: DialogAction) => {
        switch (action) {
            case DialogAction.Add:
                onFinalize();
                break;
            case DialogAction.Close:
                setOpenFinalizeConfirmation(false);
                break;
        }
    };

    // triggers whenever user clicks on 'Yes' button of confirmation dialog button after Finalize Score Card button click.
    const onFinalize = () => {
        setOpenFinalizeConfirmation(false);
        const finalizeScoreCardRequest: FinalizeScoreCardModel = new FinalizeScoreCardModel(
            assessmentId
        );

        let changedIndicators: Array<IndicatorModel> = indicators.filter(
            (indicator: IndicatorModel) =>
                indicator.isSurveyIndicator === true
        );
        const isFormValid = validate(changedIndicators);
        scoreCardService.finalizeScoreCardSurvey(finalizeScoreCardRequest, isFormValid
        )
            .then((response) => {
                if (response) {
                    setFinalize(
                        response as boolean
                    );
                }
            });
    };

    // Triggers whenever user clicks on Save button
    const onSave = () => {
        let changedIndicators: Array<IndicatorModel> = indicators.filter(
            (indicator: IndicatorModel) =>
                indicator.isSurveyIndicator === true
        );
        const _indicators = changedIndicators.filter(t => t.subObjectiveId == subObjectiveId)
        const isFormValid = validate(_indicators);

        //if (isFormValid) {
        let scoreCardIndicators: Array<ScoreCardDetailModel> = scoreCardDetail.filter(t => t.subObjectiveId == subObjectiveId)
        if (scoreCardIndicators.length == 0) {
            scoreCardIndicators.push(setDefaultScore(subObjectiveId))
        }
        const saveScoreCardRequest: ScoreCardRequestModel = new ScoreCardRequestModel(
            assessmentId,
            subObjectiveId,
            scoreCardIndicators
        );
        scoreCardService.saveScoreCardSurvey(saveScoreCardRequest)
            .then((response) => {
                if (response) {
                    setSaved(
                        response as boolean
                    );

                    bindObjectivesSubObjectivesIndicators();
                }
            });
        //}
    }

    const setDefaultScore = (subObjectiveId: string) => {
        const indicator = indicators.filter(t => t.subObjectiveId == subObjectiveId)[0];
        let scoreCardResult: ScoreCardDetailModel = {
            indicatorId: indicatorId,
            subObjectiveId: subObjectiveId,
            indicatorMetNotMetStatus: approachId != AssessmentApproach.Rapid ? indicator.surveyMetNotMetStatus : null,
            indicatorReasonForResult: indicator.reasonForResult,
            indicatorRecommendation: indicator.recommendation
        };
        return scoreCardResult;
    }

    // Get CSS class for indicator status to highlight percentage value
    const getCssClassForIndicatorStatus = (value: number) => {
        switch (value) {
            case MetNotMetStatus.Met:
                return t("Common.Met");
            case MetNotMetStatus.PartiallyMet:
                return t("Common.PartiallyMet");
            case MetNotMetStatus.NotMet:
                return t("Common.NotMet");
            case MetNotMetStatus.NotAssessed:
                return "-";
            default: return "-";
        }
    }

    const getStyleBasedOnMetNotMet = (metNotMetStatus: number) => {

        return metNotMetStatus === MetNotMetStatus.Met
            ? { color: "green" }
            : metNotMetStatus === MetNotMetStatus.PartiallyMet
                ? { color: "orange" }
                : metNotMetStatus === MetNotMetStatus.NotMet
                    ? { color: "red" }
                    : { color: "black" };
    }

    const onReasonChange = (evt: React.ChangeEvent<HTMLInputElement>, indicatorId: string, surveyMetNotMetStatus: string, recommendation: string) => {
        let value = evt?.currentTarget?.value;
        if (value) {
            if (!scoreCardDetail.find(t => t.indicatorId == indicatorId)) {
                let surveyReasonForResult: ScoreCardDetailModel = {
                    indicatorId: indicatorId,
                    subObjectiveId: subObjectiveId,
                    indicatorMetNotMetStatus: parseInt(surveyMetNotMetStatus),
                    indicatorReasonForResult: evt.target.value,
                    indicatorRecommendation: recommendation
                };
                setScoreCardDetail([...scoreCardDetail, surveyReasonForResult]);
            }
            else {
                let stateSurveyReasonForResult = scoreCardDetail.find((indicator: ScoreCardDetailModel) => indicator.indicatorId === indicatorId);
                if (stateSurveyReasonForResult) {
                    let surveyReasonForResult: ScoreCardDetailModel = {
                        indicatorId: stateSurveyReasonForResult.indicatorId,
                        subObjectiveId: subObjectiveId,
                        indicatorMetNotMetStatus: stateSurveyReasonForResult.indicatorMetNotMetStatus,
                        indicatorReasonForResult: evt.target.value,
                        indicatorRecommendation: stateSurveyReasonForResult.indicatorRecommendation
                    };
                    let index = scoreCardDetail.findIndex(p => p.indicatorId == indicatorId);
                    scoreCardDetail.splice(index, 1);
                    setScoreCardDetail([...scoreCardDetail, surveyReasonForResult]);
                }
            }

            setIndicators((prevState: Array<IndicatorModel>) => {
                const indicators = [...prevState];
                const indicator = indicators.find((indicator: IndicatorModel) => indicator.id === indicatorId);

                if (indicator) {
                    indicator.reasonForResult = value;
                }
                return indicators;
            });
        }
    }

    const onRecommendationChange = (evt: React.ChangeEvent<HTMLInputElement>, indicatorId: string, surveyMetNotMetStatus: string, reasonForResult: string) => {
        let value = evt?.currentTarget?.value;
        if (value) {
            if (!scoreCardDetail.find(t => t.indicatorId == indicatorId)) {
                let surveyRecommendation: ScoreCardDetailModel = {
                    indicatorId: indicatorId,
                    subObjectiveId: subObjectiveId,
                    indicatorMetNotMetStatus: parseInt(surveyMetNotMetStatus),
                    indicatorReasonForResult: reasonForResult,
                    indicatorRecommendation: evt.target.value
                };
                setScoreCardDetail([...scoreCardDetail, surveyRecommendation]);
            }
            else {
                let stateSurveyRecommendation = scoreCardDetail.find((indicator: ScoreCardDetailModel) => indicator.indicatorId === indicatorId);
                if (stateSurveyRecommendation) {
                    let surveyRecommendation: ScoreCardDetailModel = {
                        indicatorId: stateSurveyRecommendation.indicatorId,
                        subObjectiveId: subObjectiveId,
                        indicatorMetNotMetStatus: stateSurveyRecommendation.indicatorMetNotMetStatus,
                        indicatorReasonForResult: stateSurveyRecommendation.indicatorReasonForResult,
                        indicatorRecommendation: evt.target.value
                    };
                    let index = scoreCardDetail.findIndex(p => p.indicatorId == indicatorId);
                    scoreCardDetail.splice(index, 1);
                    setScoreCardDetail([...scoreCardDetail, surveyRecommendation]);
                }
            }

            setIndicators((prevState: Array<IndicatorModel>) => {
                const indicators = [...prevState];
                const indicator = indicators.find((indicator: IndicatorModel) => indicator.id === indicatorId);

                if (indicator) {
                    indicator.recommendation = value;
                }
                return indicators;
            });
        }
    }

    return (
        <>
            {scoreCardTab ?
                <div className="page-response-section p-0">

                    <div className="response-wrapper">
                        <>
                            < div >
                                {(approachId === AssessmentApproach.Comphrehensive)
                                    ? <span>{t("Assessment.ReportGeneration.ScoreCard.ComprehensiveScoreCard")}  </span>
                                    : (approachId === AssessmentApproach.Rapid)
                                        ? <span>{parse(t("Assessment.ReportGeneration.ScoreCard.RapidScoreCard"))} </span>
                                        : ""
                                }

                            </div>
                            <Tabs
                                tabs={renderObjectivesTab}
                                activeTabIndex={currentObjectiveTabIndex}
                                onClick={onTabObjectiveChange}
                            >
                                <div className="category-wrapper">
                                    <div className="h-100 objective-score-wrapper mt-2 px-3 justify-content-left d-flex">
                                        {(approachId === AssessmentApproach.Rapid) &&
                                            <span>
                                                {(approachId === AssessmentApproach.Rapid) ?
                                                    <b>{t("Assessment.ReportGeneration.ScoreCard.ObjectiveNationalScore")}</b>
                                                    : <b>{t("Assessment.ReportGeneration.ScoreCard.NationalScore")}</b>}
                                                - {filteredObjectives[currentObjectiveTabIndex]?.nationalScorePercentage}% </span>
                                        }
                                        {(approachId === AssessmentApproach.Comphrehensive || approachId === AssessmentApproach.Tailored) &&
                                            <span>
                                                <b>{t("Assessment.ReportGeneration.ScoreCard.ObjectiveNationalScore")}: </b>
                                                <b>{t("Assessment.ReportGeneration.ScoreCard.NationalResult")}</b>
                                                - {filteredObjectives[currentObjectiveTabIndex]?.nationalScorePercentage}%
                                            </span>
                                        }

                                        <span>| <b>{t("Assessment.ReportGeneration.ScoreCard.IndicatorsMet")}</b> - {filteredObjectives[currentObjectiveTabIndex]?.totalMetIndicators}
                                            /{filteredObjectives[currentObjectiveTabIndex]?.totalIndicators}
                                            = {filteredObjectives[currentObjectiveTabIndex]?.indicatorsMetPercentage}%</span>
                                    </div>
                                    <div className="d-flex align-items-stretch">
                                        <div className="subcategories-wrapper">
                                            <Tabs
                                                tabs={renderSubObjectivesTab}
                                                activeTabIndex={currentSubObjectiveIndex}
                                                onClick={onTabSubObjectiveChange}
                                            >
                                                <></>
                                            </Tabs>
                                        </div>

                                        <div className="indicators-wrapper">
                                            <div className="warning-panel">{t("Assessment.ReportGeneration.ScoreCard.NoteForResultAndRecommendation")}</div>
                                            <div className="objective-score-wrapper d-flex mt-2 justify-content-left">
                                                {(approachId === AssessmentApproach.Rapid) &&
                                                    <span>
                                                        {(approachId === AssessmentApproach.Rapid) ?
                                                            <b>{t("Assessment.ReportGeneration.ScoreCard.SubObjectiveNationalScore")}</b>
                                                            : <b>{t("Assessment.ReportGeneration.ScoreCard.NationalScore")}</b>}
                                                        - {filteredSubObjectives[currentSubObjectiveIndex]?.nationalScorePercentage}% </span>
                                                }
                                                {(approachId === AssessmentApproach.Comphrehensive || approachId === AssessmentApproach.Tailored) &&
                                                    <span>
                                                        <b>{t("Assessment.ReportGeneration.ScoreCard.SubObjectiveNationalScore")}: </b>
                                                        <b>{t("Assessment.ReportGeneration.ScoreCard.NationalResult")}</b>
                                                        - {filteredSubObjectives[currentSubObjectiveIndex]?.nationalScorePercentage}%
                                                    </span>
                                                }
                                                {(approachId === AssessmentApproach.Comphrehensive || approachId === AssessmentApproach.Tailored) && filteredSubObjectives[currentSubObjectiveIndex]?.surveyScorePercentage > 0 &&
                                                    <span>| <b>{t("Assessment.ReportGeneration.ScoreCard.SurveyResult")}</b> -  {filteredSubObjectives[currentSubObjectiveIndex]?.surveyScorePercentage}% </span>
                                                }

                                            </div>
                                            <fieldset disabled={assessmentStatus === AssessmentStatus.Published} className={assessmentStatus === AssessmentStatus.Published ? "row disableContent" : "row"}>
                                                <div className="survey-table h-300 mt-0">
                                                    <Table id="score-card-table">
                                                        <>
                                                            <thead>
                                                                <tr>
                                                                    <th className="width-100">{t("Assessment.ReportGeneration.ScoreCard.Sequence")}</th>
                                                                    <th>{t("Assessment.ReportGeneration.ScoreCard.Indicator")}</th>
                                                                    <th id="score">
                                                                        {(approachId !== AssessmentApproach.Rapid)
                                                                            ? t("Assessment.ReportGeneration.ScoreCard.NonRapidDQAScore")
                                                                            : t("Assessment.ReportGeneration.ScoreCard.RapidDeskLevelAndServiceDeliveryScore")}
                                                                    </th>
                                                                    {filteredSubObjectives[currentSubObjectiveIndex]?.sequence == 1.2 && approachId !== AssessmentApproach.Rapid ?
                                                                        (<th>{t("Assessment.ReportGeneration.ScoreCard.ServiceDeliveryResult")}</th>)
                                                                        : (<></>)}
                                                                    {(approachId !== AssessmentApproach.Rapid && approachId !== AssessmentApproach.Comphrehensive) && <th>{t("Assessment.ReportGeneration.ScoreCard.SurveyResult")}</th>}
                                                                    {(approachId === AssessmentApproach.Comphrehensive) && <th>{t("Assessment.ReportGeneration.ScoreCard.SurveyResult")}</th>}
                                                                    <th>{t("Assessment.ReportGeneration.ScoreCard.ReasonForResult")}</th>
                                                                    <th>{t("Assessment.ReportGeneration.ScoreCard.Recommendation")}</th>

                                                                </tr>

                                                            </thead>
                                                            <TableBody>
                                                                <>
                                                                    {
                                                                        filteredIndicators.map((indicator: IndicatorModel, index: number) =>
                                                                            <TableRow key={`row_${indicator.id}_${index}`}>
                                                                                <>
                                                                                    <TableCell className="width-100">
                                                                                        <span>{indicator.sequence}</span>
                                                                                    </TableCell>
                                                                                    <TableCell>
                                                                                        {indicator.name}
                                                                                    </TableCell>
                                                                                    {approachId !== AssessmentApproach.Rapid &&
                                                                                        <TableCell align="center" className="status-column">
                                                                                            {(indicator?.indicatorMetNotMetStatus ?? MetNotMetStatus.NotAssessed) == MetNotMetStatus.NotAssessed ?
                                                                                                (
                                                                                                    <label>-</label>
                                                                                                ) :
                                                                                                (
                                                                                                    <label style={getStyleBasedOnMetNotMet(indicator?.indicatorMetNotMetStatus)}>
                                                                                                        {getCssClassForIndicatorStatus(indicator?.indicatorMetNotMetStatus)}
                                                                                                    </label>
                                                                                                )
                                                                                            }
                                                                                        </TableCell>
                                                                                    }

                                                                                    {filteredSubObjectives[currentSubObjectiveIndex]?.sequence == 1.2 && approachId !== AssessmentApproach.Rapid ?
                                                                                        (
                                                                                            <TableCell align="center" className="status-column">
                                                                                                {(indicator?.serviceDeliveryMetNotMetStatus ?? MetNotMetStatus.NotAssessed) == MetNotMetStatus.NotAssessed ?
                                                                                                    (
                                                                                                        <label>-</label>
                                                                                                    ) :
                                                                                                    (
                                                                                                        <label style={getStyleBasedOnMetNotMet(indicator?.serviceDeliveryMetNotMetStatus)}>
                                                                                                            {getCssClassForIndicatorStatus(indicator?.serviceDeliveryMetNotMetStatus)}
                                                                                                        </label>
                                                                                                    )
                                                                                                }
                                                                                            </TableCell>) : (
                                                                                            <></>
                                                                                        )
                                                                                    }
                                                                                    {approachId === AssessmentApproach.Rapid &&
                                                                                        <TableCell align="center" className="status-column">
                                                                                            {(indicator?.sequence === Objective.Objective1.Indicator_1_2_11
                                                                                                || indicator?.sequence === Objective.Objective1.Indicator_1_2_12
                                                                                                || indicator?.sequence === Objective.Objective1.Indicator_1_2_13)
                                                                                                && indicator?.isServiceDeliveryIndicator
                                                                                                ?
                                                                                                (
                                                                                                    <label style={getStyleBasedOnMetNotMet(indicator?.serviceDeliveryMetNotMetStatus)}>
                                                                                                        {getCssClassForIndicatorStatus(indicator?.serviceDeliveryMetNotMetStatus)}
                                                                                                    </label>
                                                                                                )
                                                                                                :
                                                                                                (
                                                                                                    <label style={getStyleBasedOnMetNotMet(indicator?.indicatorMetNotMetStatus)}>
                                                                                                        {getCssClassForIndicatorStatus(indicator?.indicatorMetNotMetStatus)}
                                                                                                    </label>
                                                                                                )
                                                                                            }
                                                                                        </TableCell>
                                                                                    }
                                                                                    {getSurveyColumn(indicator)}
                                                                                    {
                                                                                        <TableCell> <TextBox defaultValue={indicator.reasonForResult} maxLength={256} disabled={isFinalized} multiline={true} rows={1} onBlur={(evt: React.FocusEvent<HTMLInputElement>) => {
                                                                                            onReasonChange(evt, indicator?.id, indicator?.surveyMetNotMetStatus?.toString(), indicator?.recommendation);
                                                                                        }}></TextBox>  </TableCell>

                                                                                    }
                                                                                    {
                                                                                        <TableCell> <TextBox defaultValue={indicator.recommendation} maxLength={256} disabled={isFinalized} multiline={true} rows={1} onBlur={(evt: React.FocusEvent<HTMLInputElement>) => {
                                                                                            onRecommendationChange(evt, indicator?.id, indicator?.surveyMetNotMetStatus?.toString(), indicator?.reasonForResult);
                                                                                        }}></TextBox></TableCell>
                                                                                    }
                                                                                </>
                                                                            </TableRow>
                                                                        )}
                                                                </>
                                                            </TableBody>
                                                        </>
                                                    </Table>
                                                </div>
                                            </fieldset>
                                        </div>

                                        <div className="response-action-wrapper">
                                            <div className="button-action-section d-flex justify-content-center p-3">
                                                {canSaveScoreCard && !isFinalized &&

                                                    <button
                                                        className="btn app-btn-secondary"
                                                        onClick={onSave}
                                                    >
                                                        {t("Common.Save")}
                                                    </button>

                                                }
                                                {canFinalizeScoreCard && isSaved &&
                                                    <button
                                                        className="btn app-btn-secondary"
                                                        onClick={(evt: React.MouseEvent<HTMLButtonElement>) => {
                                                            evt.preventDefault();
                                                            setOpenFinalizeConfirmation(true);
                                                        }}
                                                    >
                                                        {t("Common.Finalize")}
                                                    </button>
                                                }
                                                {canExportScoreCard && isFinalized &&
                                                    <button className="btn app-btn-primary" onClick={onGenerateTemplate}>
                                                        {t("Assessment.ReportGeneration.ScoreCard.GenerateScoreCard")}
                                                    </button>
                                                }
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </Tabs>
                        </>

                        {/* Confirmation dialog shown when finalized button is clicked */}
                        <ConfirmationDialog
                            open={openFinalizeConfirmation}
                            title={t("Common.ConfirmationTitle")}
                            content={t("Assessment.ReportGeneration.ScoreCard.ScoreCardFinalizeDialogContent")}
                            onClick={onFinalizeConfirmationBtnClick}
                        />
                    </div>
                </div>
                :
                <div className="h-300 d-flex align-items-center justify-content-center">
                    {(approachId === AssessmentApproach.Rapid || approachId === AssessmentApproach.Tailored)
                        ? t("Assessment.ReportGeneration.ScoreCard.PrerequisiteToViewScoreCard_Rapid")
                        : t("Assessment.ReportGeneration.ScoreCard.PrerequisiteToViewScoreCard")
                    }
                </div>
            }

            <DownloadInProgressModal isFileDownloading={isFileDownloading} />
        </>
    )
}

export default ScoreCardComponent;