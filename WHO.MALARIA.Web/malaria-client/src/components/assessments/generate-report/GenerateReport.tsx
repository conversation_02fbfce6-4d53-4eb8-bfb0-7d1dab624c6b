﻿import React, { ChangeEvent, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { AssessmentStatus, DialogAction } from "../../../models/Enums";
import ConfirmationDialog from "../../common/ConfirmationDialog";
import { <PERSON><PERSON>, IconButton } from "@mui/material";
import classNames from "classnames";
import { reportService } from "../../../services/reportService";
import { DataAnalysisReportFile, DeleteReportFile, PublishAssessment, ShowAssessmentResultOnGlobalDashboard } from "../../../models/ReportModel/ReportModel";
import { useNavigate,  useLocation } from "react-router-dom";
import { UserAssessmentPermission } from "../../../models/PermissionModel";
import { useDispatch, useSelector } from "react-redux";
import MultiSelectModel from "../../../models/MultiSelectModel";
import RadioButtonGroup from "../../controls/RadioButtonGroup";
import FileUploader from "../../controls/FileUploader";
import { Constants } from "../../../models/Constants";
import Table from "../data-collection/desk-review/responses/Table";
import TableHeader from "../data-collection/desk-review/responses/TableHeader";
import TableBody from "../data-collection/desk-review/responses/TableBody";
import TableRow from "../data-collection/desk-review/responses/TableRow";
import TableCell from "../data-collection/desk-review/responses/TableCell";
import Tooltip from "../../controls/Tooltip";
import InfoIcon from "@mui/icons-material/Info";
import CancelIcon from "@mui/icons-material/Close";
import useAssessmentPermissions from "../useAssessmentPermissions";
import { assessmentService } from "../../../services/assessmentService";
import { setUserAssessmentPermission } from "../../../redux/ducks/user-permission";
import { UtilityHelper } from "../../../utils/UtilityHelper";
import DownloadInProgressModal from "../../controls/DownloadInProgressModal";

/** Renders the generate report component*/
const GenerateReport = () => {
    const { t } = useTranslation();
    const location: any = useLocation();
    const navigate = useNavigate();
    const state = location?.state;
    const assessmentId = state?.assessmentId;
    const assessmentStatus = state?.status;
    const dispatch = useDispatch();
    const userPermission: UserAssessmentPermission = useSelector((state: any) => state.userPermission.assessment);
    const [openPublishConfirmation, setOpenPublishConfirmation] = useState<boolean>(false);
    const [openOptOutConfirmation, setOpenOptOutConfirmation] = useState<boolean>(false);
    const [isOptOut, setIsOptOut] = useState<boolean>(true);
    const [isTemplateUploaded, setIsTemplateUploaded] = useState<boolean>(false);
    const [isFileSizeMaximum, setIsFileSizeMaximum] = useState<boolean>(false);
    const [dataAnalysisReportFiles, setDataAnalysisReportFiles] = useState<Array<DataAnalysisReportFile>>([]);
    const [isFileDownloading, setIsFileDownloading] = useState<boolean>(false);

    useEffect(() => {
        setIsOptOut(true);
        getDataAnalysisReport(assessmentId);
    }, []);


    // Get data analysis report response by assessmentId
    const getDataAnalysisReport = (assessmentId: string) => {
        reportService.getReports(assessmentId).then((response) => {
            setDataAnalysisReportFiles(response.reportFiles);
            setIsOptOut(response.showResultsOnGlobalDashboard);
        });
    }

    // Triggered whenever publish button is clicked
    const onPublishConfirmationBtnClick = (action: DialogAction) => {
        switch (action) {
            case DialogAction.Add:
                onPublish();
                break;
            case DialogAction.Close:
                onCancelPublish();
                break;
        }
    };

    // Triggers whenever user clicks on 'Yes' button of confirmation dialog button for publish
    const onPublish = () => {
        const request: PublishAssessment = new PublishAssessment(assessmentId);
        reportService.publishAssessment(request)
            .then((response) => {
                if (response) {
                    navigate(location.pathname, { state: {
                        ...state,
                        status: AssessmentStatus.Published
                    } });

                    getAssessmentPermission();
                }
            });
        setOpenPublishConfirmation(false);
    }

    // get an assessment permission for user            
    const getAssessmentPermission = () => {
        assessmentService
            .getPermission(assessmentId)
            .then((permission: UserAssessmentPermission) => {
                dispatch(setUserAssessmentPermission(permission))
            });
    }

    // Triggers whenever user clicks on 'Cancel' button of confirmation dialog button
    const onCancelPublish = () => {
        setOpenPublishConfirmation(false);
    }

    //triggers whenever click on view health facilities
    const onOptOutChange = (evt: ChangeEvent<HTMLInputElement>) => {
        const value = evt.target.value === "true" ? true : false;
        setIsOptOut(value);
        setOpenOptOutConfirmation(true);
    }


    // Triggered whenever opt out radio button is clicked
    const onOptOutConfirmationBtnClick = (action: DialogAction) => {
        switch (action) {
            case DialogAction.Add:
                onOptOut();
                break;
            case DialogAction.Close:
                onCancelOptOut();
                break;
        }
    };

    // Triggers whenever user clicks on 'Yes' button of confirmation dialog button for opt out
    const onOptOut = () => {
        const request: ShowAssessmentResultOnGlobalDashboard = new ShowAssessmentResultOnGlobalDashboard(assessmentId,
            isOptOut);
        reportService.optOutAssessmentOnGlobalDashboard(request);
        setOpenOptOutConfirmation(false);
    }

    // Triggers whenever user clicks on 'Cancel' button of confirmation dialog button
    const onCancelOptOut = () => {
        setOpenOptOutConfirmation(false);
        setIsOptOut(!isOptOut);
    }

    // Triggers whenever user clicks on upload button
    const uploadFile = (evt: React.ChangeEvent<HTMLInputElement>) => {
        // check file size
        const fileSize = evt.target.files ? evt.target.files[0].size : 0;

        if (fileSize > Constants.Common.MaxFileSize) //25MB
        {
            setIsFileSizeMaximum(true);
            return;
        } else {
            setIsFileSizeMaximum(false);
        }

        // since we are passing file along with other information for which we are creating 
        // FormData object to pass other properties along with file so it can be mapped at server side
        setIsTemplateUploaded(true);
        const formData = new FormData();
        formData.append("AssessmentId", assessmentId);
        formData.append("File", evt.target.files ? evt.target.files[0] : "");

        reportService.uploadFile(formData).then((response) => {
            if (response) {
                setIsTemplateUploaded(false);
                getDataAnalysisReport(assessmentId);
            }
        }).catch((error: any) => {
            setIsTemplateUploaded(false);
        });
    }

    // Header rows
    const headersRows = [
        t("Report.FileName"),
        t("Report.FileSize"),
        t("Report.Download"),
        t("Report.Delete")
    ];

    // Download report file by report id
    const downloadReportFile = (reportId: string, fileName: string) => {
        reportService
            .downloadReport(reportId).then((response: any) => {
                UtilityHelper.download(response, fileName);
                setIsFileDownloading(false);
            }).catch((error: any) => {
                setIsFileDownloading(false);
            });
    }

    // Delete report file by report id
    const onCloseButtonClick = (reportId: string) => {
        const formData = new DeleteReportFile(assessmentId, reportId);

        reportService.deleteFile(formData).then((response) => {
            if (response) {
                setIsTemplateUploaded(false);
                getDataAnalysisReport(assessmentId);
            }
        }).catch((error: any) => {
            setIsTemplateUploaded(false);
        });
    };

    return (
        <>
            {assessmentStatus != AssessmentStatus.Published && userPermission.canPublish &&
                <>
                    <div className="response-action-wrapper h-300 d-flex align-items-center justify-content-center">
                        <div className={classNames("button-action-section", "button-bottom", "d-flex", "justify-content-center", "p-3")}>
                            <Button
                                className={classNames("btn", "app-btn-secondary")}
                                onClick={(evt: React.MouseEvent<HTMLButtonElement>) => {
                                    evt.preventDefault();
                                    setOpenPublishConfirmation(true);
                                }}
                            >
                                {t("Report.Publish")}
                            </Button>
                        </div>
                    </div>
                </>
            }

            {assessmentStatus == AssessmentStatus.Published &&
                <div className="h-300">
                    <div className="checkbox-list">
                        <h6>
                            {t("Report.OptOut")}

                            <IconButton className="grid-icon-button">
                                <Tooltip
                                    content={t("Report.OptOutInfoText"
                                    )}
                                    isHtml
                                >
                                    <InfoIcon fontSize="small" />
                                </Tooltip>
                            </IconButton>
                        </h6>
                    </div>
                    <div className="px-3 row">
                        <div className="col-sm-6">
                            <RadioButtonGroup
                                id="IsViewHealthFacilityRadioButtonSelected"
                                name="IsViewHealthFacilityRadioButtonSelected"
                                row
                                color="primary"
                                options={[
                                    new MultiSelectModel(
                                        true,
                                        t("indicators-responses:Common:Yes"),
                                        !userPermission?.showResultsOnGlobalDashboard as boolean
                                    ),
                                    new MultiSelectModel(
                                        false,
                                        t("indicators-responses:Common:No"),
                                        !userPermission?.showResultsOnGlobalDashboard as boolean
                                    ),
                                ]}
                                value={isOptOut}
                                onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                                    onOptOutChange(e);
                                }}
                            />
                        </div>
                    </div>
                    <div className={classNames("button-action-section", "d-flex", "justify-content-center")}>
                        {userPermission.showResultsOnGlobalDashboard &&
                            <span>{t("Assessment.DataCollection.DataQualityAssessment.FileUploadDetailText")}</span>
                        }
                    </div>
                    <div className={classNames("button-action-section", "button-bottom", "d-flex", "justify-content-center", "p-3")}>
                        {userPermission.showResultsOnGlobalDashboard &&

                            <FileUploader
                                key={`fileuploader_${Math.random()}`}
                                id="template"
                                linkCss={classNames("btn", "app-btn-secondary", "ms-2")}
                                onChange={uploadFile}
                                accept=".xlsx, .xls , .jpg, .jpeg, .png,.pdf"
                            >
                                <i className="d-inline-flex mx-2 align-items-center">{t("Assessment.DataCollection.DataQualityAssessment.FileUploadSize")}</i>
                            </FileUploader>


                        }
                    </div>
                    <>
                        {isTemplateUploaded &&
                            <span className="d-flex justify-content-center Mui-error"> {t("Assessment.DataCollection.DataQualityAssessment.TemplateUploadMessage")} </span>
                        }
                        {isFileSizeMaximum &&
                            <span className="d-flex justify-content-center Mui-error"> {t("Exception.InvalidFileSize")} </span>
                        }
                    </>
                    <>
                        {dataAnalysisReportFiles.length ?
                            (
                                <Table>
                                    <>
                                        <TableHeader
                                            headers={headersRows.map((header: any) => header)}
                                        />

                                        <TableBody>
                                            <>
                                                {dataAnalysisReportFiles.map((dataAnalysisReportFile: DataAnalysisReportFile, index: number) => {
                                                    return (
                                                        <TableRow key={`file${index}_${Math.random()}`}>
                                                            <>
                                                                <TableCell>{dataAnalysisReportFile.name}</TableCell>
                                                                <TableCell>{dataAnalysisReportFile.size}</TableCell>
                                                                <TableCell>
                                                                    <Button className="btn app-btn-primary" size="small" onClick={() => downloadReportFile(dataAnalysisReportFile.id, dataAnalysisReportFile.name)}>
                                                                        {t("Common.Export")}
                                                                    </Button>
                                                                </TableCell>
                                                                <TableCell>
                                                                    <div className="ml-auto">
                                                                        <IconButton onClick={() => onCloseButtonClick(dataAnalysisReportFile.id)} title="Close" className="close-modal">
                                                                            <CancelIcon />
                                                                        </IconButton>
                                                                    </div>
                                                                </TableCell>
                                                            </>
                                                        </TableRow>
                                                    )
                                                }
                                                )}
                                            </>
                                        </TableBody>
                                    </>
                                </Table>
                            ) :
                            <>
                                <p className="p-4">
                                    {t("Report.Message.NoFileUploaded")}
                                </p>
                            </>
                        }
                    </>
                </div>
            }

            {/* Confirmation dialog shown when publish button is clicked */}
            <ConfirmationDialog
                open={openPublishConfirmation}
                title={t("Common.ConfirmationTitle")}
                content={t("Report.PublishDialogContent")}
                onClick={onPublishConfirmationBtnClick}
            />

            {/* Confirmation dialog shown when opt out radio button is clicked */}
            <ConfirmationDialog
                open={openOptOutConfirmation}
                title={t("Common.ConfirmationTitle")}
                content={isOptOut ? t("Report.OptOutDialogContent") : t("Report.OptInDialogContent")}
                onClick={onOptOutConfirmationBtnClick}
            />

            <DownloadInProgressModal isFileDownloading={isFileDownloading} />
        </>
    )
}

export default GenerateReport;