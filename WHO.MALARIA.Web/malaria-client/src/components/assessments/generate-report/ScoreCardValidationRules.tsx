import { Constants } from "../../../models/Constants";
import { DataType } from "../../../models/Enums";
import ValidationRuleModel, { IValidationRuleProvider } from "../../../models/ValidationRuleModel";

/** Score Card validation rules */
const ScoreCardValidationRules: IValidationRuleProvider = {
    "indicators": new ValidationRuleModel(DataType.ArrayOfObject, true),
    "surveyMetNotMetStatus": new ValidationRuleModel(DataType.Number, true, 
        `${Constants.Common.RootObjectNameSubstitute}.surveyMetNotMetStatus!== null && 
        ${Constants.Common.RootObjectNameSubstitute}.isSurveyIndicator && 
        ${Constants.Common.RootObjectNameSubstitute}.isSurveyIndicator === true`)
}

export default ScoreCardValidationRules;