import { useEffect, useState } from "react";
import StrategyContainer from "./strategy-selection/StrategyContainer";
import IndicatorSelectionContainer from "./indicator-selection/IndicatorSelectionContainer";
import classes from "./assessment.module.scss";
import {
    AssessmentApproach,
    AssessmentStatus,
    StrategyStepper,
    MetNotMetEnum
} from "../../models/Enums";
import WHOStepper from "../controls/WHOStepper";
import { StepperModel } from "../../models/StepperModel";
import classNames from "classnames";
import { useNavigate,  useLocation } from "react-router-dom";
import { StrategySelectionModel } from "../../models/ScopeDefinitionModel";
import { useTranslation } from "react-i18next";
import { UtilityHelper } from "../../utils/UtilityHelper";
import { Constants } from "../../models/Constants";
import { useDispatch, useSelector } from "react-redux";
import InfoIcon from "@mui/icons-material/Info";
import {
    updateDiagramsStatus,
    updateIndicatorSelection,
    updateStrategySelection,
} from "../../redux/ducks/scopedefinition";
import { setMetNotMetStatus } from "../../redux/ducks/met-not-met";
import { SaveIndicatorSelectionRequestModel } from "../../models/RequestModels/ScopeDefinitionRequestModel";
import UserMessage from "../common/UserMessage";
import { UserAssessmentPermission } from "../../models/PermissionModel";
import { DiagramsStatusModel } from "../../models/DiagramsStatusModel";

type ScopeDefinitionContainerProps = {
    currentStep: StrategyStepper;
    assessmentApproach: number;
};

/** Renders Assessment Scope Definition */
const ScopeDefinitionContainer = (props: ScopeDefinitionContainerProps) => {
    const { t } = useTranslation();
    document.title = t("app.ScopeDefinitionTitle");

    const { currentStep, assessmentApproach } = props;
    const permission: UserAssessmentPermission = useSelector((state: any) => state.userPermission.assessment);
    const { canUpdateIndicators, canUpdateStrategies } = permission;
    const [activeStep, setActiveStep] = useState<number>(currentStep);

    const location: any = useLocation();
    const navigate = useNavigate();
    const dispatch = useDispatch();

    useEffect(() => {
        // cleaning stuff
        return () => {
            UtilityHelper.clearSessionStorage(
                Constants.SessionStorageKey.STRATEGY_SELECTION_NAV_IDS
            );
            // clean up redux-store
            cleanupStore();
        };
    }, []);

    // cleanup redux store once component is unloaded
    const cleanupStore = () => {
        dispatch(updateIndicatorSelection(new SaveIndicatorSelectionRequestModel("", [], AssessmentApproach.Rapid)));

        dispatch(updateStrategySelection(new StrategySelectionModel([], false, [])));

        dispatch(setMetNotMetStatus(MetNotMetEnum.NotMet));

        dispatch(updateDiagramsStatus(DiagramsStatusModel.init()));
    };

    // triggered by a child component whenever strategies are saved for an assessment
    const onStrategySave = () => {
        const assessmentId = location?.state?.assessmentId;
        const approach = location?.state?.approach;
        const country = location?.state?.country;
        const status = location?.state?.status;
        navigate("/assessment/scope-definition/indicator", { state: {
            assessmentId,
            approach,
            country,
            status,
        } });
        setActiveStep(StrategyStepper.Indicator);
    };
    // triggered by a child component whenever previous button click on indicator.
    const onPreviousButtonClick = () => {
        const assessmentId = location?.state?.assessmentId;
        const approach = location?.state?.approach;
        const country = location?.state?.country;
        const status = location?.state?.status;
        navigate("/assessment/scope-definition/strategy", { state: {
            assessmentId,
            approach,
            country,
            status,
        } });
        setActiveStep(StrategyStepper.Strategy);
    };

    const canDisable = (location?.state?.status <= AssessmentStatus.Finalized);

    return (
        <WHOStepper
            steps={[
                new StepperModel(
                    StrategyStepper.Strategy,
                    `${t("Assessment.ScopeDefinition.StrategySelection")}`
                ),
                new StepperModel(
                    StrategyStepper.Indicator,
                    `${t("Assessment.ScopeDefinition.IndicatorSelection")}`
                ),
            ]}
            className={classNames(classes.stepWrapper)}
            activeStep={activeStep}
        >
            <>
                {canDisable && (
                    <>
                        <UserMessage
                            className={classNames(
                                "d-flex",
                                "justify-content-center",
                                classes.alert
                            )}
                            renderContent={
                                <>
                                    <InfoIcon />
                                    <span>
                                        {t("Assessment.ScopeDefinition.FinalizeUserMessage")}
                                    </span>
                                </>
                            }
                        />
                    </>
                )}
                <fieldset>
                    {activeStep === StrategyStepper.Strategy && (
                        <StrategyContainer
                            canDisable={!canUpdateStrategies}
                            onStrategySelectionSave={onStrategySave}
                        />
                    )}
                    {activeStep === StrategyStepper.Indicator && (
                        <>
                            <IndicatorSelectionContainer
                                canDisable={!canUpdateIndicators}
                                assessmentApproach={assessmentApproach}
                                onPreviousButtonClick={onPreviousButtonClick}
                            />
                        </>
                    )}
                </fieldset>
            </>
        </WHOStepper>
    );
};

export default ScopeDefinitionContainer;
