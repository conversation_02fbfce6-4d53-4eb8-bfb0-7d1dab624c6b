import React, { useState, useEffect } from "react";
import { useSelector } from "react-redux";
import { useLocation } from "react-router-dom";
import { But<PERSON> } from "@mui/material";
import { useTranslation } from "react-i18next";
import classNames from "classnames";

import MultiSelectModel from "../../../models/MultiSelectModel";
import RadioButtonGroup from "../../controls/RadioButtonGroup";

import { assessmentService } from "../../../services/assessmentService";
import { StrategyModel } from "../../../models/StrategyModel";
import Checkbox from "../../controls/Checkbox";
import classes from "../assessment.module.scss";
import InfoIcon from "@mui/icons-material/Info";
import { IconButton } from "@mui/material";
import Tooltip from "../../controls/Tooltip";
import useStrategySelection from "./useStrategySelection";
import { StrategySelectionModel } from "../../../models/ScopeDefinitionModel";
import { AssessmentStatus, AssessmentStrategies } from "../../../models/Enums";
import { UtilityHelper } from "../../../utils/UtilityHelper";

type StrategySelectionProps = {
    canDisable: boolean;
    onStrategySelectionSave: () => void;
};

/** Renders Stragey Selection Container */
const StrategyContainer = (props: StrategySelectionProps) => {
    const { onStrategySelectionSave, canDisable } = props;

    const { t } = useTranslation();
    document.title = t("app.AssessmentStrategySelectionTitle");
    const location: any = useLocation();
    const [strategies, setStrategies] = useState<Array<StrategyModel>>([]);
    const [strategySelectionId, setStrategySelectionId] = useState<string>("");
   
    const {
        getAssessmentStrategies,
        onCaseStrategyChange,
        setDefaultStrategy,
        onStrategyFormSubmit,
        onMalariaControlStrategyChange,
    } = useStrategySelection();

    const strategySelection: StrategySelectionModel = useSelector(
        (state: any) => state.scopeDefinition.strategySelection
    );
    const status = location?.state?.status;

    const currentlanguage = UtilityHelper.getCookieValue("i18next");

    useEffect(() => {
        assessmentService
            .getStrategies()
            .then((strategies: Array<StrategyModel>) => {
                setStrategies(strategies);
                getAssessmentStrategies();
            });
    }, [currentlanguage]);

    const {
        caseStrategyIds,
        isMalariaControlStrategy,
        malariaControlStrategyIds,
    } = strategySelection;

    // get all the mandatory strategies
    let mandatoryStrategies = [
        ...strategies
            .filter(
                (strategy: StrategyModel) => strategy.type === AssessmentStrategies.Case && strategy.id != '21b7e764-7e3f-4fb4-9791-c22b7c1d2a11' //TODO: needs to remove && condition later. This is done for time being to launch just Rapid assessment only with Burdenreduction or elimination 
            )
            .map(
                (strategy: StrategyModel): MultiSelectModel =>
                    new MultiSelectModel(strategy.id, strategy.name, false, false)
            ),
    ];

    // get other strategies
    const otherStrategies = strategies
        .filter(
            (strategy: StrategyModel) =>
                strategy.type === AssessmentStrategies.MalariaControl
        )
        .map(
            (strategy: StrategyModel): MultiSelectModel =>
                new MultiSelectModel(strategy.id, strategy.name, false, false)
        );

    // triggers when user submit the forms
    const handleFormSubmit = (evt: React.FormEvent<HTMLFormElement>) => {
        onStrategyFormSubmit(evt).then((response: any) => {
            if (response) {
                // call props
                onStrategySelectionSave();
            }
        });
    };

    useEffect(() => {
        let strategySelectionIds = "";
        if (caseStrategyIds.length === 0 && mandatoryStrategies.length > 0) {
            strategySelectionIds = mandatoryStrategies[0].id;
            // update reducer about the defualt value
            if (strategySelectionIds) setDefaultStrategy(strategySelectionIds);
        } else if (caseStrategyIds.length === 2) {
            strategySelectionIds = caseStrategyIds.join(",");
        } else {
            strategySelectionIds = caseStrategyIds[0];
        }
        setStrategySelectionId(strategySelectionIds);

    }, [caseStrategyIds])

    return (
        <form onSubmit={handleFormSubmit}>
            <fieldset className="d-flex-column" disabled={canDisable}>
                <div className={classNames("mt-2")}>
                    <div className={classNames("p-3")}>
                        {t("Common.SelectCaseServillance")}
                        <RadioButtonGroup
                            name="caseStrategyIds"
                            row
                            color="primary"
                            className="d-flex"
                            options={mandatoryStrategies}
                            value={strategySelectionId}
                            onChange={onCaseStrategyChange}
                        />
                    </div>
                    {/*TODO: Below lines needs to be uncomment later. This is done for time being to launch just Rapid assessment only with Burdenreduction or elimination*/} 
                    {/*<div className={classNames("border", "rounded", "p-3", "app-list-group", classes.br_b)}>*/}
                    {/*    {t("Common.MaleriaStrategyControl")}*/}
                    {/*    <IconButton className="grid-icon-button">*/}
                    {/*        <Tooltip*/}
                    {/*            content={t(*/}
                    {/*                "Common.MaleriaStrategyControlTooltip"*/}
                    {/*            )}*/}
                    {/*            isHtml*/}
                    {/*        >*/}
                    {/*            <InfoIcon fontSize="small" />*/}
                    {/*        </Tooltip>*/}
                    {/*    </IconButton>*/}
                    {/*    <div className="d-flex">*/}
                    {/*        <RadioButtonGroup*/}
                    {/*            name="isMalariaControlStrategy"*/}
                    {/*            row*/}
                    {/*            color="primary"*/}
                    {/*            className={classNames("d-flex")}*/}
                    {/*            options={[*/}
                    {/*                new MultiSelectModel(true, t("Common.Yes"), false, false),*/}
                    {/*                new MultiSelectModel(false, t("Common.No"), false, false),*/}
                    {/*            ]}*/}
                    {/*            value={isMalariaControlStrategy}*/}
                    {/*            onChange={onCaseStrategyChange}*/}
                    {/*        />*/}
                    {/*    </div>*/}
                    {/*    <ul className="list-group list-group-flush">*/}
                    {/*        {otherStrategies.map((os: MultiSelectModel) => (*/}
                    {/*            <li key={`${os.id}_${os.text}`} className="list-group-item">*/}
                    {/*                <Checkbox*/}
                    {/*                    id="malariaControlStrategyIds"*/}
                    {/*                    name="malariaControlStrategyIds"*/}
                    {/*                    label={os.text}*/}
                    {/*                    color="primary"*/}
                    {/*                    value={os.id}*/}
                    {/*                    checked={malariaControlStrategyIds.includes(os.id)}*/}
                    {/*                    disabled={!isMalariaControlStrategy}*/}
                    {/*                    onChange={onMalariaControlStrategyChange}*/}
                    {/*                />*/}
                    {/*            </li>*/}
                    {/*        ))}*/}
                    {/*    </ul>*/}
                    {/*</div>*/}
                </div>
            </fieldset>
            <div className="button-action-section d-flex justify-content-center p-3">
                {!canDisable && status !== AssessmentStatus.Finalized && status !== AssessmentStatus.Published ?
                    (
                        <Button
                            type="submit"
                            className={classNames("btn", "app-btn-primary")}
                        >

                            {t("Common.SaveAndContinue")}

                        </Button>

                    ) :
                    (
                        <Button
                            className={classNames("btn", "app-btn-secondary")}
                            onClick={() => onStrategySelectionSave()}
                        >
                            {t("Common.Next")}
                        </Button>
                    )
                }
            </div>
        </form>
    );
};

export default StrategyContainer;
