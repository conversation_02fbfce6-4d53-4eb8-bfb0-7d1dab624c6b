import React from "react";
import { useDispatch, useSelector } from "react-redux";
import { useLocation } from "react-router-dom";
import { Constants } from "../../../models/Constants";
import { AssessmentStrategies, StatusCode } from "../../../models/Enums";
import { BaseMessageModel, ErrorModel } from "../../../models/ErrorModel";
import { SaveStrategyRequestModel } from "../../../models/RequestModels/ScopeDefinitionRequestModel";
import { StrategySelectionModel } from "../../../models/ScopeDefinitionModel";
import { StrategyModel } from "../../../models/StrategyModel";
import { updateStrategySelection } from "../../../redux/ducks/scopedefinition";
import { assessmentService } from "../../../services/assessmentService";
import { notificationService } from "../../../services/notificationService";
import { <PERSON>tilityHelper } from "../../../utils/UtilityHelper";
import { useTranslation } from "react-i18next";

/** Custom hook which handles events related to scope definition
 * @param scopeDefinitionProperty A string value of a property defined under ScopeDefinitionModel class
 */
function useStrategySelection() {
  const location: any = useLocation();
   const { t } = useTranslation();
  const strategySelection: StrategySelectionModel = useSelector(
    (state: any) => state.scopeDefinition.strategySelection
  );
  const assessmentId = location.state.assessmentId;
  const dispatch = useDispatch();

  /** Set an default strategy for the first time */
  const setDefaultStrategy = (defaultCaseId: string) => {
    const _strategySelection: StrategySelectionModel = {
      ...strategySelection,
      caseStrategyIds: [defaultCaseId],
    };

    dispatch(updateStrategySelection(_strategySelection));
  };

  /** called from component whenever onChange event of any HTMLInputControl is Triggered
   * @param evt React.ChangeEvent<HTMLInputElement>
   */
  const onCaseStrategyChange = (evt: React.ChangeEvent<HTMLInputElement>) => {
    // for boolean field convert value to boolean
    console.log(evt.target.textContent);
    const value =
      evt.target.name === "isMalariaControlStrategy"
        ? evt.target.value === "true"
        : [evt.target.value];

    const _strategySelection: StrategySelectionModel = {
      ...strategySelection,
      [evt.target.name]: value,
    };
    procesMalariaControlStrategies(
      evt.target.name === "isMalariaControlStrategy",
      _strategySelection
    );
  };

  // handle the change events and based on that strategyIds
  const procesMalariaControlStrategies = (
    isMalariaControlStrategy: boolean,
    strategySelectionModel: StrategySelectionModel
  ) => {
    if (
      isMalariaControlStrategy &&
      !strategySelectionModel.isMalariaControlStrategy
    ) {
      const updatedStrategySelectionModel = {
        ...strategySelectionModel,
        malariaControlStrategyIds: [],
      };
      dispatch(updateStrategySelection(updatedStrategySelectionModel));
    } else {
      dispatch(updateStrategySelection(strategySelectionModel));
    }
  };
  /** called from component whenever onChange event of any HTMLInputControl(Checkbox) is Triggered
   * @param evt React.ChangeEvent<HTMLInputElement>
   */
  const onMalariaControlStrategyChange = (
    evt: React.ChangeEvent<HTMLInputElement>
  ) => {
    // add or remove strategy ids based on checkbox 'checked' or 'unchecked'
    const strategyIds = evt.target.checked
      ? [...strategySelection.malariaControlStrategyIds, evt.target.value]
      : [...strategySelection.malariaControlStrategyIds].filter(
        (id: string) => id !== evt.target.value
      );

    // prepare the object model
    const _strategySelection: StrategySelectionModel = {
      ...strategySelection,
      [evt.target.name]: strategyIds,
    };

    dispatch(updateStrategySelection(_strategySelection));
  };

  // validate the form
  const validate = () => {
    if (!assessmentId) {
      notificationService.sendMessage(
        new ErrorModel( t("Errors.AssessmentIdShouldNotBeEmpty"), 0)
      );
      return false;
    }
    if (
      strategySelection.caseStrategyIds.length === 0 &&
      strategySelection.malariaControlStrategyIds.length === 0
    ) {
      notificationService.sendMessage(
        new ErrorModel(t("Errors.PleaseSelectSrategies"), 0)
      );
      return false;
    }

    return true;
  };

  /** Triggers when strategy form is submitted
   * @param evt React.FormEvent<HTMLFormElement>
   * @returns thenable boolean value
   */
  const onStrategyFormSubmit = (evt: React.FormEvent<HTMLFormElement>) =>
    new Promise((resolve, reject) => {
      evt.preventDefault();

      if (!validate()) {
        resolve(false);
        return;
      }
      const param = createParam();
      assessmentService.saveStrategies(param).then((success: boolean) => {
        notificationService.sendMessage(
          new BaseMessageModel(
            t("Assessment.ScopeDefinition.StrategiessavedMessage"),
            StatusCode.Created
          )
        );
        // save to session storage to manage reload or hard refresh on indicator page
        UtilityHelper.clearSessionStorage(
          Constants.SessionStorageKey.STRATEGY_SELECTION_NAV_IDS
        );
        UtilityHelper.saveToSessionStorage(
          Constants.SessionStorageKey.STRATEGY_SELECTION_NAV_IDS,
          strategySelection
        );

        resolve(success);
      });
    });

  // create parameter for request model
  const createParam = () => {
    // check if user has selected 'Both' option
    let strategyIds: Array<string> = [];
    if (
      strategySelection.caseStrategyIds.length > 0 &&
      strategySelection.caseStrategyIds[0].indexOf(",") > -1
    ) {
      strategyIds = strategySelection.caseStrategyIds[0].split(",");
    } else {
      strategyIds = strategySelection.caseStrategyIds;
    }

    strategyIds = [
      ...strategyIds,
      ...strategySelection.malariaControlStrategyIds,
    ];

    return new SaveStrategyRequestModel(assessmentId, strategyIds);
  };

  /** Triggered when list of strategies are being fetched for an assessment */
  const getAssessmentStrategies = () => {
    assessmentService
      .getAssessmentStrategies(assessmentId)
      .then((strategies: Array<StrategyModel>) => {
        convertToStrategySelectionModel(strategies);
      });
  };

  // processing logic which converts StrategyModel to StrategySelectionModel
  const convertToStrategySelectionModel = (
    strategies: Array<StrategyModel>
  ) => {
    const caseIndicatorIds: Array<string> = strategies
      .filter(
        (strategy: StrategyModel) => strategy.type === AssessmentStrategies.Case
      )
      .map((strategy: StrategyModel) => strategy.id);

    const malariaControlIndicatorIds: Array<string> = strategies
      .filter(
        (strategy: StrategyModel) =>
          strategy.type === AssessmentStrategies.MalariaControl
      )
      .map((strategy: StrategyModel) => strategy.id);

    // check if strategies has already been saved previously
    const canUpdate =
      caseIndicatorIds.length > 0 || malariaControlIndicatorIds.length > 0;

    const _strategySelection = new StrategySelectionModel(
      caseIndicatorIds,
      malariaControlIndicatorIds.length > 0,
      malariaControlIndicatorIds,
      canUpdate
    );

    // save to session storage to manage reload or hard refresh on indicator page
    UtilityHelper.saveToSessionStorage(
      Constants.SessionStorageKey.STRATEGY_SELECTION_NAV_IDS,
      canUpdate ? _strategySelection : strategySelection
    );

    dispatch(
      updateStrategySelection(
        canUpdate ? _strategySelection : strategySelection
      )
    );
  };

  return {
    getAssessmentStrategies,
    onCaseStrategyChange,
    setDefaultStrategy,
    onMalariaControlStrategyChange,
    onStrategyFormSubmit,
  };
}

export default useStrategySelection;
