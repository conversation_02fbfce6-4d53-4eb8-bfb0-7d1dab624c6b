﻿import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { assessmentService } from "../../services/assessmentService";
import { UserAssessmentPermission } from "../../models/PermissionModel";
import { setUserAssessmentPermission } from "../../redux/ducks/user-permission";

/** Custom hook to get the assessment permissions based on the assessmentId
 *  @param assessmentId A string guid value
*/
function useAssessmentPermissions(assessmentId: string) {
    const dispatch = useDispatch();
    const userPermission: UserAssessmentPermission = useSelector((state: any) => state.userPermission.assessment);

    useEffect(() => {
        if (userPermission && Object.keys(userPermission).every(key => userPermission[key] === null)) {            
            getAssessmentPermission();
        }
    }, [])

    // get an assessment permission for user
    const getAssessmentPermission = () => {
        if (!assessmentId) return;

        assessmentService
            .getPermission(assessmentId)
            .then((permission: UserAssessmentPermission) => {
                dispatch(setUserAssessmentPermission(permission))
            });
    };
}

export default useAssessmentPermissions;