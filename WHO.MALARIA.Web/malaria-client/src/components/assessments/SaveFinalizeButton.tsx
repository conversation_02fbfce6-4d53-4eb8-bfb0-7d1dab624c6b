import classNames from "classnames";
import React from "react";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import { UserAssessmentPermission } from "../../models/PermissionModel";
import { useLocation } from "react-router-dom";
import { DeskReviewAssessmentResponseStatus } from "../../models/Enums";

interface SaveFinalizeButtonProps {
    onSave: () => void;
    onFinalize: () => void;
}

/** Renders the save/finalize button for desk review indicators */
const SaveFinalizeButton = (props: SaveFinalizeButtonProps) => {
    const { t } = useTranslation();
    const { onSave, onFinalize } = props;
    const location: any = useLocation();
    const assessmentResponseStatus = location?.state?.assessmentResponseStatus;
    const userPermission: UserAssessmentPermission = useSelector((state: any) => state.userPermission.assessment);

    return (
        <div className="response-action-wrapper">
            {userPermission?.canSaveOrFinalizeDRIndicatorResponse &&
                <div className="button-action-section d-flex justify-content-center p-3">
                    {
                        assessmentResponseStatus !== DeskReviewAssessmentResponseStatus.Completed &&
                        <button className={classNames("btn", "app-btn-secondary")}
                            onClick={onSave}
                        >
                            {t("indicators-responses:Common:Save")}
                        </button>
                    }

                    <button className={classNames("btn", "app-btn-primary")}
                        onClick={onFinalize}
                    >
                        {t("indicators-responses:Common:Finalize")}
                    </button>
                </div>
            }
        </div>
    )
}

export default SaveFinalizeButton;