import React, { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import AssessmentHeader from "./indicator-selection/AssessmentHeader";
import Tabs from "../controls/Tabs";
import classes from "./assessment.module.scss";
import { AssessmentStatus, AssessmentTabs } from "../../models/Enums";
import classNames from "classnames";
import { assessmentService } from "../../services/assessmentService";
import ScopeDefinitionContainer from "./ScopeDefinitionContainer";
import { useNavigate,  useLocation } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { useEffect } from "react";
import DataCollectionContainer from "./data-collection/DataCollectionContainer";
import DataAnalysisContainer from "./data-analysis/DataAnalysisContainer"
import { UserAssessmentPermission } from "../../models/PermissionModel";
import { setAssessmentTabIndex } from "../../redux/ducks/assessment-tab";
import useAssessmentPermissions from "./useAssessmentPermissions";
import { UtilityHelper } from "../../utils/UtilityHelper";
import GenerateReportContainer from "./generate-report/GenerateReportContainer";

type AssessmentContainerProps = {
    country: string;
    currentTabIndex: number;
    currentStepIndex: number;
};

/**Renders the add assessment screen component
 * @param props AssessmentContainerProps
 */
const AssessmentContainer = (props: AssessmentContainerProps) => {
    const { t } = useTranslation();
    const location: any = useLocation();
    const navigate = useNavigate();
    const dispatch = useDispatch();
    const assessmentId = location?.state?.assessmentId;
    const { country, currentStepIndex, currentTabIndex } = props;
    const [completedTabIndex, setCompletedTabIndex] = useState<Array<number>>([]);
    const assessmentPermission: UserAssessmentPermission = useSelector((state: any) => state.userPermission.assessment);
    const tabIndex: number = useSelector((state: any) => state.assessmentTab);
    // Called custom hook to get the assessment permissions based on the assessmentId
    useAssessmentPermissions(assessmentId);

    useEffect(() => {
        getCompletedtabIndex();

        return () => { cleanupStore(); }
    }, []);

    // cleanup redux store once component is unloaded
    const cleanupStore = () => {
        dispatch(setAssessmentTabIndex(AssessmentTabs.ScopeDefinition))
    };

    // get completed tab Index
    const getCompletedtabIndex = () => {
        const status = location?.state?.status;
        const { ScopeDefinition, DataCollection, DataAnalysis, ReportGeneration } =
            AssessmentTabs;
        const pathName = location?.pathname;

        switch (status) {
            case AssessmentStatus.Finalized:

                if (pathName.indexOf("data-analysis") > -1) {
                    dispatch(setAssessmentTabIndex(DataAnalysis));
                }
                else if (pathName.indexOf("report-generation") > -1) {
                    dispatch(setAssessmentTabIndex(ReportGeneration));
                }
                else {
                    dispatch(setAssessmentTabIndex(DataCollection));
                }
                setCompletedTabIndex([ScopeDefinition]);

                break;
            case AssessmentStatus.Published:
                if (pathName.indexOf("data-analysis") > -1) {
                    dispatch(setAssessmentTabIndex(DataAnalysis));
                }
                else if (pathName.indexOf("report-generation") > -1) {
                    dispatch(setAssessmentTabIndex(ReportGeneration));
                }
                else {
                    dispatch(setAssessmentTabIndex(DataCollection));
                }
                setCompletedTabIndex([ScopeDefinition, DataCollection, DataAnalysis, ReportGeneration]);
                break;
            default:
                setCompletedTabIndex([]); // no completed tab index
                break;
        }
    };

    // triggers whenever tab is clicked
    const onTabClick = (currentIndex: number) => {
        const status = location?.state?.status;
        const { DataCollection, DataAnalysis, ReportGeneration } = AssessmentTabs;
        switch (status) {
            // if already 'Finalized' only 'ScopeDefinition' and 'DataCollection' is clickable
            // TODO: 'Datacollection' should be finalize to enable 'DataAnalysis' and this shoud be handled once datacollection completed
            case AssessmentStatus.Finalized:
                // todo: (currentIndex <= DataCollection
                if (currentIndex <= ReportGeneration) {
                    dispatch(setAssessmentTabIndex(currentIndex));
                    onNavigate(currentIndex);
                }

                //Tracking assessment finalized event in analytics
                UtilityHelper.onEventAnalytics("Assessment", "Finalized", "Finalized");

                break;
            // if already 'Published' only 'ScopeDefinition', 'DataCollection' and 'DataAnalysis' are clickable
            case AssessmentStatus.Published:
                if (currentIndex <= ReportGeneration) {
                    dispatch(setAssessmentTabIndex(currentIndex));
                    onNavigate(currentIndex);
                }

                //Tracking assessment published event in analytics
                UtilityHelper.onEventAnalytics("Assessment", "Published", "Published");
                break;
        }
    };

    // Helps in redirection to other route based on the tab index
    const onNavigate = (currentTabIndex: number) => {
        const country = location?.state?.country;
        const assessmentId = location?.state?.assessmentId;
        const strategyId = location?.state?.strategyId;
        const status = location?.state?.status;
        const approach = location?.state?.approach;

        let url: string = "";
        switch (currentTabIndex) {
            case AssessmentTabs.ScopeDefinition:
                url = "/assessment/scope-definition/strategy";
                break;
            case AssessmentTabs.DataCollection:
                url = "/assessment/data-collection/desk-review";
                break;
            case AssessmentTabs.DataAnalysis:
                url = "/assessment/data-analysis/desk-review";
                break;
            case AssessmentTabs.ReportGeneration:
                url = "/assessment/report-generation/score-card";
                break;
        }

        navigate(url, { state: {
            country,
            assessmentId,
            status,
            approach,
            strategyId
        } });
    };

    // render component based on tab selection and their status
    const renderComponent = () => {
        const { ScopeDefinition, DataCollection, DataAnalysis, ReportGeneration } =
            AssessmentTabs;

        switch (tabIndex) {
            case ScopeDefinition:
                return (
                    <ScopeDefinitionContainer
                        currentStep={currentStepIndex}
                        assessmentApproach={location?.state?.approach as number}
                    />
                );

            case DataCollection:
                return (
                    <DataCollectionContainer
                        currentStepIndex={currentStepIndex}
                    />
                );

            case DataAnalysis:
                return (
                    <DataAnalysisContainer />
                );

            case ReportGeneration:
                return (
                    <GenerateReportContainer />
                );
        }
    };

    return (
        <section className="page-full-section">
            <AssessmentHeader country={country || ""} />
            <Tabs
                className={classNames("page-sticky-head-section", classes.br_b)}
                tabs={assessmentService.getAssessmentTabs(currentTabIndex)}
                activeTabIndex={currentTabIndex}
                completedTabIndex={completedTabIndex}
                onClick={onTabClick}
            >
                <>{renderComponent()}</>
            </Tabs>
        </section>
    );
};

export default AssessmentContainer;
