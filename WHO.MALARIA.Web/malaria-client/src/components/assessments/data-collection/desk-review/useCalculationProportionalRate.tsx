import { useRef } from "react";
import { useTranslation } from "react-i18next";
import useCalculation from "../desk-review/responses/useCalculation"

/**Custom hook to handle the proportion calculation rate for the 1.3.3 Indicator*/
const useCalculationProportionalRate = () => {
    const { t } = useTranslation();
    const { calculatePercentage } = useCalculation();

    const proportionalRateForNationalLevelRef = useRef<boolean>(true);
    const proportionalRateForRegionalLevelRef = useRef<boolean>(true);
    const proportionalRateForDistrictRef = useRef<boolean>(true)

    const isProportionRateValid = () => proportionalRateForNationalLevelRef.current && proportionalRateForRegionalLevelRef.current && proportionalRateForDistrictRef.current;

    //Check condition for proportion calculation rate and validate and shows message if value less than 0 or greater than 100 
    const calculateProportionRateProperty = (propertyName1: number, propertyName2: number, response: any) => {
        const proportionRateValue = calculatePercentage(propertyName1, propertyName2);

        const proportionRateExceptionContent =
            <span className="Mui-error d-flex mb-2">
                * {t("indicators-responses:Common:ResponseProportionError")}
            </span>

        switch (propertyName1) {
            case response?.nationalLevel?.noOfMeetingsOccured:
                if (proportionRateValue >= 0 && proportionRateValue <= 100) {
                    proportionalRateForNationalLevelRef.current = true;
                    return proportionRateValue + '%';
                }

                proportionalRateForNationalLevelRef.current = false;
                return proportionRateExceptionContent;

            case response?.regionalLevel?.noOfMeetingsOccured:
                if (proportionRateValue >= 0 && proportionRateValue <= 100) {
                    proportionalRateForRegionalLevelRef.current = true;
                    return proportionRateValue + '%';
                }

                proportionalRateForRegionalLevelRef.current = false;
                return proportionRateExceptionContent;

            case response?.districtLevel?.noOfMeetingsOccured:
                if (proportionRateValue >= 0 && proportionRateValue <= 100) {
                    proportionalRateForDistrictRef.current = true;
                    return proportionRateValue + '%';
                }

                proportionalRateForDistrictRef.current = false;
                return proportionRateExceptionContent;
        }
    }

    return {
        calculateProportionRateProperty,
        isProportionRateValid
    }
}

export default useCalculationProportionalRate;