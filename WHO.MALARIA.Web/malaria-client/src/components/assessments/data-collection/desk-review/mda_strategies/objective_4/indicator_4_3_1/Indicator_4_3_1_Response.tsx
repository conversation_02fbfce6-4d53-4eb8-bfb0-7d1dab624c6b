﻿import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@mui/material";
import React, { ChangeEvent, useEffect, useRef } from "react";
import classNames from "classnames";
import { useTranslation } from "react-i18next";
import TextBox from "../../../../../../controls/TextBox";
import Table from "../../../responses/Table";
import TableHeader from "../../../responses/TableHeader";
import TableBody from "../../../responses/TableBody";
import TableRow from "../../../responses/TableRow";
import TableCell from "../../../responses/TableCell";
import Checkbox from "../../../../../../controls/Checkbox";
import parse from "html-react-parser";
import useFormValidation from "../../../../../../common/useFormValidation";
import ValidationRules from "../../../mda_strategies/objective_4/indicator_4_3_1/ValidationRules";
import useIndicatorResponseCapture from "../../../responses/useIndicatorResponseCapture";
import { Response_1 } from "../../../../../../../models/DeskReview/Objective_4/Indicator_4_3_1/Response_1";
import { useSelector } from "react-redux";
import RadioButtonGroup from "../../../../../../controls/RadioButtonGroup";
import MultiSelectModel from "../../../../../../../models/MultiSelectModel";
import useCalculation from "../../../responses/useCalculation";
import TableFooter from "../../../responses/TableFooter";
import Tooltip from "../../../../../../controls/Tooltip";
import InfoIcon from "@mui/icons-material/Info";
import { IValidationRuleProvider } from '../../../../../../../models/ValidationRuleModel';
import { CannotBeAssessedReasonValidationRule } from "../../../CannotBeAssessedReasonValidationRule";
import SaveFinalizeButton from "../../../../../../assessments/SaveFinalizeButton";

/** Renders the indicator 4.3.1 response for desk review */
const Indicator_4_3_1_Response = () => {
    const { t } = useTranslation(["indicators-responses"]);
    document.title = t("indicators-responses:app:DR_Objective_4_Indicator_4_3_1_Title");
    const { calculatePercentageOfYesNo } = useCalculation();

    //Contains all the excluded properties in the array which are not needed
    const excludedRadioButtonProperties: Array<string> = [
        "cannotBeAssessed",
        "cannotBeAssessedReason",
        "descriptionOfSupervisionPlan",
        "levelOfSupervision",
        "metNotMetStatus",
    ];

    //Contains all the excluded properties in the array which are not needed
    const excludedTextboxProperties: Array<string> = [
        "cannotBeAssessed",
        "cannotBeAssessedReason",
        "supervisionGuidelines",
        "supervisionVisitsChecklists",
        "supervisionVisitSchedule",
        "metNotMetStatus",
    ];

    //Holds the validation rules and it gets changed when cannot be assessed is set to true.
    const validationRulesRef = useRef<IValidationRuleProvider>(ValidationRules);

    const validate = useFormValidation(validationRulesRef.current);

    const {
        response,
        onChange,
        onCannotBeAssessed,
        onChangeWithKey,
        onSave,
        onFinalize,
        getResponse,
        setTrueFlagOnFinalizeButtonClick
    } = useIndicatorResponseCapture<Response_1>(Response_1.init(), validate);

    const errors = useSelector((state: any) => state.error);

    // triggers on click of finalize button, performs validations and then action is performed
    const onResponseFinalize = () => {
        setTrueFlagOnFinalizeButtonClick();
        const isFormValid = validate(response);
        if (isFormValid) {
            onFinalize();
        }
    };

    useEffect(() => {
        getResponse();
    }, []);

    useEffect(() => {
        validationRulesRef.current =
            response?.cannotBeAssessed === true
                ? CannotBeAssessedReasonValidationRule
                : ValidationRules;

    }, [response?.cannotBeAssessed]);

    //Triggers onChange of cannotBeAssessed checkbox 
    const onCannotBeAssessedChange = (evt: ChangeEvent<HTMLInputElement>) => {
        //If indicator is can not be assessed then the other validation rules doesn't make sense except for can not be assessed reason rule.
        //If we do not change the validation rules and use all the rules in useFormValidation hook then that hook will validate on all the rules
        //for all the response data which is not needed and this behavior prevents form submitting even user has added the data in cannot be assessed
        //reason field. Hence, to prevent it we are setting validation rule only for can not be assessed reason field.
        validationRulesRef.current =
            evt.currentTarget.checked
                ? CannotBeAssessedReasonValidationRule
                : ValidationRules;

        onCannotBeAssessed(evt);
    }

    const headers = [
        {
            field: "supervision",
            label: t("indicators-responses:DRObjective_4_Responses:Indicator_4_3_1:Supervision"),
        },
        {
            field: "fromNationalLevelToSubnationalLevel",
            label: t("indicators-responses:DRObjective_4_Responses:Indicator_4_3_1:FromNationalLevelToSubnationalLevel"),
        },
        {
            field: "fromSubnationalLevelToServiceDeliveryLevel",
            label: t("indicators-responses:DRObjective_4_Responses:Indicator_4_3_1:FromSubnationalLevelToServiceDeliveryLevel"),
        },
    ];

    const textboxRowLabel: any = {
        "descriptionOfSupervisionPlan": {
            field: "descriptionOfSupervisionPlan",
            label: t(
                "indicators-responses:DRObjective_4_Responses:Indicator_4_3_1:DescriptionOfSupervisionPlan"
            ),
            placeholderFirstColumn: t(
                "indicators-responses:DRObjective_4_Responses:Indicator_4_3_1:SupervisonPlanProcessesPlaceholder"
            ),
            placeholderSecondColumn: t(
                "indicators-responses:DRObjective_4_Responses:Indicator_4_3_1:SupervisonPlanProcessesPlaceholder"
            ),
        },
        "levelOfSupervision": {
            field: "levelOfSupervision",
            label: t(
                "indicators-responses:DRObjective_4_Responses:Indicator_4_3_1:LevelOfSupervision"
            ),
            placeholderFirstColumn: t(
                "indicators-responses:DRObjective_4_Responses:Indicator_4_3_1:CentralDecentralizedPlaceholder"
            ),
            placeholderSecondColumn: t(
                "indicators-responses:DRObjective_4_Responses:Indicator_4_3_1:CentralDecentralizedPlaceholder"
            ),
        },
    };

    const radioButtonsRowLabel: any = {
        supervisionGuidelines: {
            field: "supervisionGuidelines",
            label: t(
                "indicators-responses:DRObjective_4_Responses:Indicator_4_3_1:SupervisionGuidelines"
            ),
        },
        supervisionVisitsChecklists: {
            field: "supervisionVisitsChecklists",
            label: t(
                "indicators-responses:DRObjective_4_Responses:Indicator_4_3_1:SupervisionVisitsChecklists"
            ),
        },
        supervisionVisitSchedule: {
            field: "supervisionVisitSchedule",
            label: t(
                "indicators-responses:DRObjective_4_Responses:Indicator_4_3_1:SupervisionVisitSchedule"
            ),
        },
    };

    //Method creates an array of properties that have checked "Yes"
    const calculateSurveillanceTaskYesNoPercentage = (propertyName: string) => {
        const propertyArray: boolean[] = Object.keys(response)
            .filter((key: string) => !excludedRadioButtonProperties.includes(key))
            .map((modelKeyName: string) => response[modelKeyName][propertyName]);

        return calculatePercentageOfYesNo(propertyArray);
    };

    return (
        <>
            <div className="response-assess-wrapper">
                <Checkbox
                    label={t("indicators-responses:Common:IndicatorNoAssess")}
                    onChange={onCannotBeAssessedChange}
                    checked={response?.cannotBeAssessed}
                />
            </div>

            {!response?.cannotBeAssessed ? (
                <div className="response-wrapper">
                    <p>
                        {t("indicators-responses:DRObjective_4_Responses:Indicator_4_3_1:MDAResponseDesc")}
                    </p>
                    <p>
                        {parse(t("indicators-responses:DRObjective_4_Responses:Indicator_4_3_1:ResponseDesc1"))}
                    </p>

                    {
                        // User does not provide details for national to sub-national or sub-national to service delivery for supervision
                        (errors["descriptionOfSupervisionPlan.national"] ||
                            errors["descriptionOfSupervisionPlan.subNational"] ||
                            errors["levelOfSupervision.national"] ||
                            errors["levelOfSupervision.subNational"]) &&
                        <span className="Mui-error d-flex mb-2">
                            * {t("indicators-responses:DRObjective_4_Responses:Indicator_4_3_1:ResponseErrorText")}
                        </span>
                    }
                    {
                        // User does not select 'Yes' or 'No' for all national to sub-national or sub-national to service delivery
                        (errors["supervisionGuidelines.national"] ||
                            errors["supervisionGuidelines.subNational"] ||
                            errors["supervisionVisitsChecklists.national"] ||
                            errors["supervisionVisitsChecklists.subNational"] ||
                            errors["supervisionVisitSchedule.national"] ||
                            errors["supervisionVisitSchedule.subNational"]) &&
                        <span className="Mui-error d-flex mb-2">
                            * {t("indicators-responses:DRObjective_4_Responses:Indicator_4_3_1:ResponseErrorRadio")}
                        </span>
                    }
                    <Table>
                        <>
                            <TableHeader
                                headers={headers.map((header: any) => header.label)}
                            />
                            <TableBody>
                                <>
                                    {Object.keys(response)
                                        .filter(
                                            (key: string) => !excludedTextboxProperties.includes(key)
                                        )
                                        .map((modelKeyName: string, index: number) => (
                                            <TableRow key={`row_${modelKeyName}_${index}`}>
                                                <>
                                                    <TableCell>
                                                        <>{textboxRowLabel[modelKeyName]?.label}</>
                                                    </TableCell>

                                                    <TableCell>
                                                        <TextBox
                                                            id={`${textboxRowLabel[modelKeyName]?.field
                                                                }_${index}_${Math.random()}`}
                                                            name="national"
                                                            placeholder={
                                                                textboxRowLabel[modelKeyName]
                                                                    ?.placeholderFirstColumn
                                                            }
                                                            fullWidth
                                                            multiline
                                                            rows={3}
                                                            value={response[modelKeyName]?.national}
                                                            onChange={(
                                                                e: React.ChangeEvent<HTMLInputElement>
                                                            ) => onChangeWithKey(e, modelKeyName)}
                                                            error={errors[`${modelKeyName}.national`] && errors[`${modelKeyName}.national`]}
                                                        />
                                                    </TableCell>

                                                    <TableCell>
                                                        <TextBox
                                                            id={`${textboxRowLabel[modelKeyName]?.field
                                                                }_${index}_${Math.random()}`}
                                                            name="subNational"
                                                            placeholder={
                                                                textboxRowLabel[modelKeyName]
                                                                    ?.placeholderFirstColumn
                                                            }
                                                            fullWidth
                                                            multiline
                                                            rows={3}
                                                            value={response[modelKeyName]?.subNational}
                                                            onChange={(
                                                                e: React.ChangeEvent<HTMLInputElement>
                                                            ) => onChangeWithKey(e, modelKeyName)}
                                                            error={errors[`${modelKeyName}.subNational`] && errors[`${modelKeyName}.subNational`]}
                                                        />
                                                    </TableCell>
                                                </>
                                            </TableRow>
                                        ))}

                                    {Object.keys(response)
                                        .filter(
                                            (key: string) => !excludedRadioButtonProperties.includes(key)
                                        )
                                        .map((modelKeyName: string, index: number) => (
                                            <TableRow key={`row_${modelKeyName}_${index}`}>
                                                <>
                                                    <TableCell>
                                                        <>{radioButtonsRowLabel[modelKeyName]?.label}</>
                                                    </TableCell>
                                                    <TableCell>
                                                        <RadioButtonGroup
                                                            id="national"
                                                            name="national"
                                                            color="primary"
                                                            options={[
                                                                new MultiSelectModel(
                                                                    true,
                                                                    t("indicators-responses:Common:Yes")
                                                                ),
                                                                new MultiSelectModel(
                                                                    false,
                                                                    t("indicators-responses:Common:No")
                                                                ),
                                                            ]}
                                                            value={response[modelKeyName]?.national}
                                                            onChange={(
                                                                e: React.ChangeEvent<HTMLInputElement>
                                                            ) => onChangeWithKey(e, modelKeyName)}
                                                            error={errors[`${modelKeyName}.national`] && errors[`${modelKeyName}.national`]}
                                                        />
                                                    </TableCell>
                                                    <TableCell>
                                                        <RadioButtonGroup
                                                            id="subNational"
                                                            name="subNational"
                                                            color="primary"
                                                            options={[
                                                                new MultiSelectModel(
                                                                    true,
                                                                    t("indicators-responses:Common:Yes")
                                                                ),
                                                                new MultiSelectModel(
                                                                    false,
                                                                    t("indicators-responses:Common:No")
                                                                ),
                                                            ]}
                                                            value={response[modelKeyName]?.subNational}
                                                            onChange={(
                                                                e: React.ChangeEvent<HTMLInputElement>
                                                            ) => onChangeWithKey(e, modelKeyName)}
                                                            error={errors[`${modelKeyName}.subNational`] && errors[`${modelKeyName}.subNational`]}
                                                        />
                                                    </TableCell>
                                                </>
                                            </TableRow>
                                        ))}
                                </>
                            </TableBody>
                            <TableFooter>
                                <>
                                    <TableCell>
                                        <span>
                                            {t(
                                                "indicators-responses:DRObjective_4_Responses:Indicator_4_3_1:ProportionOfSupervisonDocuments"
                                            )}
                                        </span>
                                    </TableCell>
                                    <TableCell>
                                        <label>{calculateSurveillanceTaskYesNoPercentage("national")}%</label>
                                    </TableCell>
                                    <TableCell>
                                        <label>{calculateSurveillanceTaskYesNoPercentage("subNational")}%</label>
                                    </TableCell>
                                </>
                            </TableFooter>
                        </>
                    </Table>
                </div>
            ) : (
                <div className="response-wrapper d-flex">
                    <TextBox
                        id="cannotBeAssessedReason"
                        name="cannotBeAssessedReason"
                        label={t("indicators-responses:Common:IndicatorNoAssessReasons")}
                        multiline
                        rows={10}
                        variant="outlined"
                        fullWidth
                        value={response?.cannotBeAssessedReason}
                        onChange={onChange}
                        error={
                            errors["cannotBeAssessedReason"] &&
                            errors["cannotBeAssessedReason"]
                        }
                        helperText={
                            errors["cannotBeAssessedReason"] &&
                            errors["cannotBeAssessedReason"]
                        }
                    />
                </div>
            )}
            <SaveFinalizeButton onSave={onSave} onFinalize={onResponseFinalize} />
        </>
    );
};

export default Indicator_4_3_1_Response;