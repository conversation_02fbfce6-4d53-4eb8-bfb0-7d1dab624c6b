import { DataType } from "../../../../models/Enums";
import { Constants } from "../../../../models/Constants";
import ValidationRuleModel, {
  IValidationRuleProvider,
} from "../../../../models/ValidationRuleModel";

export const CannotBeAssessedReasonValidationRule: IValidationRuleProvider = {
    cannotBeAssessedReason: new ValidationRuleModel(
      DataType.String,
      false,
      `${Constants.Common.RootObjectNameSubstitute}.cannotBeAssessed === true && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.cannotBeAssessedReason)`
    ),
  }