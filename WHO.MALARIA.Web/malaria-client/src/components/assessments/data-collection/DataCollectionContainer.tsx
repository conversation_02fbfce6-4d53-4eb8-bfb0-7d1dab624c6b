import classNames from "classnames";
import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { StepperModel } from "../../../models/StepperModel";
import WHOStepper from "../../controls/WHOStepper";
import classes from "../assessment.module.scss";
import { AssessmentApproach, DataCollectionStepper, StrategiesEnum, ValidationStatus } from "../../../models/Enums";
import { useNavigate,  useLocation } from "react-router-dom";
import DeskReviewContainer from "./desk-review/DeskReviewContainer";
import { notificationService } from "../../../services/notificationService";
import { ErrorModel } from "../../../models/ErrorModel";
import { assessmentService } from "../../../services/assessmentService";
import { StrategyModel } from "../../../models/StrategyModel";
import WHOTabs from "../../controls/WHOTabs";
import { TabModel } from "../../../models/TabModel";
import DQAContainer from "./data-quality-assessment/DQAContainer";
import { useDispatch, useSelector } from "react-redux";
import { cleanup } from "../../../redux/ducks/error";
import { DiagramsStatusModel } from "../../../models/DiagramsStatusModel";
import { updateDiagramsStatus } from "../../../redux/ducks/scopedefinition";
import ConfigureRespondentType from "./question-bank/ConfigureRespondentType";
import { reset } from "../../../redux/ducks/form-change-tracker";
import { reset as resetUserGuideDrawer } from "../../../redux/ducks/user-guide-drawer";
import { UserAssessmentPermission } from "../../../models/PermissionModel";
import SurveyResult from "./SurveyResult";
import { UtilityHelper } from "../../../utils/UtilityHelper";

type DataCollectionContainerProps = {
    currentStepIndex: DataCollectionStepper;
};

/** DataCollection Container which renders steppers and other components
 * @param of type DataCollectionContainerProps
 */
const DataCollectionContainer = (props: DataCollectionContainerProps) => {
    const { currentStepIndex } = props;
    const dispatch = useDispatch();
    const { t } = useTranslation();
    const location: any = useLocation();
    const navigate = useNavigate();
    const assessmentId = location?.state?.assessmentId;
    const status = location?.state?.status;
    const approach = location?.state?.approach;
    const country = location?.state?.country;
    // Used to set the count in the history and based on this how to configure survey questionnaire information modal is shown (In Question Bank) for first time
    const showHowToConfigureQuestionBankModal = location?.state?.showHowToConfigureQuestionBankModal;
    const [activeStepIndex, setActiveStepIndex] =
        useState<number>(currentStepIndex);
    const [assessmentStrategies, setAssessmentStrategies] = useState<
        Array<TabModel>
    >([]);
    const [strategyId, setStrategyId] = useState<string>(location?.state?.strategyId);
    const userPermission: UserAssessmentPermission = useSelector((state: any) => state.userPermission.assessment);

    const currentlanguage = UtilityHelper.getCookieValue("i18next");

    useEffect(() => {
        getAssessmentStrategies();
        return () => { cleanupStore(); }
    }, [currentlanguage]);

    // cleanup redux store once component is unloaded
    const cleanupStore = () => {
        dispatch(cleanup());
        dispatch(reset());
        dispatch(resetUserGuideDrawer());
    };

    useEffect(() => {
        if (strategyId) {
            getDiagramStatus(assessmentId, strategyId);
        }
    }, [strategyId]);

    // get strategies for assessment
    const getAssessmentStrategies = () => {
        if (!assessmentId) {
            notificationService.sendMessage(
                new ErrorModel(
                    t("Assessment.DataCollection.MissingAssessmentSelection"),
                    ValidationStatus.Failed
                )
            );
            return;
        }

        assessmentService
            .getAssessmentStrategies(assessmentId)
            .then((strategies: Array<StrategyModel>) => {
                const options = strategies.map(
                    (strategy: StrategyModel): TabModel =>   
                        new TabModel(strategy.id, strategy.shortName, <></>)
                );

                options.forEach(function (val: TabModel) {                   
                   val.label = t(`Common.${val.label}`);
                });
                       
                
                setAssessmentStrategies(options);
                if (!strategyId) {
                    const strategyId = options[0]?.id as string;
                    setStrategyId((options[0]?.id as string) || "");
                    navigate(location.pathname, { state: {
                        assessmentId,
                        strategyId,
                        status,
                        approach,
                        country
                    } });
                }
            });
    };

    // triggers whenever user changes the strategy dropdown
    const onChange = (event: React.ChangeEvent<{}>, newValue: any) => {
        const strategyId = assessmentStrategies[newValue].id as string;
        setStrategyId(strategyId);
        navigate(location.pathname, { state: {
            assessmentId,
            strategyId,
            status,
            approach,
            country
        } });

    };

    // get diagram status 
    const getDiagramStatus = (assessmentId: string, strategyId: string) => {
        assessmentService.getDiagramsStatus(assessmentId, strategyId).then((data: DiagramsStatusModel) => {
            dispatch(updateDiagramsStatus(data));
        });
    };

    let commonDataCollectionSteppers: Array<StepperModel> = [
        new StepperModel(
            DataCollectionStepper.DeskReview,
            `${t("Assessment.DataCollection.DeskReview")}`
        )
    ];

    //Checks if the strategy is of type case strategy
    const isCaseStrategy: boolean = (strategyId === StrategiesEnum.BurdenReduction.toLowerCase() || strategyId === StrategiesEnum.Both.toLowerCase() || strategyId === StrategiesEnum.Elimination.toLowerCase());

    // Set question bank stepper index for the tab model to render tabs on the UI
    const surveyResultStepperIndex: number = isCaseStrategy ? DataCollectionStepper.SurveyResult : (DataCollectionStepper.SurveyResult - 1);

    // Desk review, DQA and question bank stepper
    const deskReviewDQAQuestionBankStepper: Array<StepperModel> = [...commonDataCollectionSteppers,
    new StepperModel(
        DataCollectionStepper.DQA,
        `${t("Assessment.DataCollection.DQA")}`
    ),
    new StepperModel(
        DataCollectionStepper.QuestionBank,
        `${t("Assessment.DataCollection.QuestionBank")}`
    ),
    new StepperModel(
        surveyResultStepperIndex,
        `${t("Assessment.DataCollection.SurveyResult")}`
    )];

    // Desk review and DQA stepper
    const deskReviewDQAStepper: Array<StepperModel> = [...commonDataCollectionSteppers,
    new StepperModel(
        DataCollectionStepper.DQA,
        `${t("Assessment.DataCollection.DQA")}`
    )]

    // All data collection steps    
    const dataCollectionSteppers: Array<StepperModel> =
        approach !== AssessmentApproach.Rapid ?
            (isCaseStrategy ?
                deskReviewDQAQuestionBankStepper :
                commonDataCollectionSteppers
            ) :
            isCaseStrategy ?
                deskReviewDQAStepper :
                commonDataCollectionSteppers;

    // find the tabIndex
    const tabIndex =
        assessmentStrategies.findIndex((tab: TabModel) => tab.id === strategyId) >
            -1
            ? assessmentStrategies.findIndex((tab: TabModel) => tab.id === strategyId)
            : 0;

    // Triggers when step is changed
    const onStepChange = (currentStep: number) => {
        setActiveStepIndex(currentStep);
        onNavigate(currentStep);
    };

    // Helps in redirection to other route of data collection based on the tab index 
    const onNavigate = (currentTabIndex: number) => {
        let url: string = "";
        switch (currentTabIndex) {
            case DataCollectionStepper.DeskReview:
                url = "/assessment/data-collection/desk-review";
                break;
            case DataCollectionStepper.QuestionBank:
                url = "/assessment/data-collection/question-bank";
                break;
            case DataCollectionStepper.DQA:
                url = "/assessment/data-collection/dqa";
                break;
            case surveyResultStepperIndex:
                url = "/assessment/data-collection/survey-result";
                break;
        }

        navigate(url, { state: { assessmentId, strategyId, status, approach, country, showHowToConfigureQuestionBankModal } });
    };

    return (
        <section className="page-full-section">
            <WHOStepper
                steps={dataCollectionSteppers}
                className={classNames(classes.stepWrapper)}
                // Checks if activeStepIndex is greater and sets the question bank stepper index for the tab model to show activeStep for question bank and for all other modules it renders the activeStep based on the currentStepIndex coming from props
                // The activeStep for the stepper gets applied based on the number of steps in the steppermodel and steps gets generated based on the conditions(e.g. 2 steps or 3 steps) inorder to set proper step for question bank stepper index based on the steppermodel to render activeStep
                activeStep={activeStepIndex}
                enableStepClick
                alternativeLabel={false}
                nonLinear={true}
                onStepChange={onStepChange}
            >
                <>
                    {activeStepIndex === DataCollectionStepper.DeskReview && (
                        <>
                            <div className="app-tab-wrapper">
                                <WHOTabs
                                    tabs={assessmentStrategies}
                                    scrollable={true}
                                    value={tabIndex}
                                    onChange={onChange}
                                >
                                    <DeskReviewContainer
                                        strategyId={strategyId}
                                        strategyShortName={assessmentStrategies[tabIndex]?.label}
                                        strategies={assessmentStrategies}
                                    />
                                </WHOTabs>
                            </div>
                        </>
                    )}

                    {activeStepIndex === DataCollectionStepper.DQA && isCaseStrategy && (
                        <>
                            <DQAContainer
                                strategyId={strategyId}
                                assessmentId={assessmentId}
                            />
                        </>
                    )}

                    {activeStepIndex === DataCollectionStepper.QuestionBank && (
                        <ConfigureRespondentType />
                    )}

                    {activeStepIndex === surveyResultStepperIndex && isCaseStrategy && (
                        <>
                            <SurveyResult />
                        </>
                    )}
                </>
            </WHOStepper>
        </section>
    );
};

export default DataCollectionContainer;