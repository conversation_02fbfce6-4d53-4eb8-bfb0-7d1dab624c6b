﻿import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { IndicatorModel, SurveyQuestionModel } from "../../../../models/QuestionBank/SurveyQuestionModel";
import { Table, TableBody, TableCell, TableRow } from "@mui/material";
import Checkbox from "../../../controls/Checkbox";
import TextBox from "../../../controls/TextBox";
import { Question, SurveyQuestionRequestModel } from "../../../../models/QuestionBank/SurveyQuestionRequestModel";
import { questionBankService } from "../../../../services/questionBankService";
import { useNavigate,  useLocation } from "react-router-dom";
import { RespondentTypeModel } from "../../../../models/QuestionBank/RespondentTypeModel";
import { SurveyQuestionFinalizeModel } from "../../../../models/QuestionBank/SurveyQuestionFinalizeModel";
import ConfirmationDialog from "../../../common/ConfirmationDialog";
import { DialogAction, QuestionIds, StatusCode } from "../../../../models/Enums";
import { useSelector } from "react-redux";
import { UserAssessmentPermission } from "../../../../models/PermissionModel";
import useAssessmentPermissions from "../../useAssessmentPermissions";
import { UtilityHelper } from "../../../../utils/UtilityHelper";
import DownloadInProgressModal from "../../../controls/DownloadInProgressModal";
import { notificationService } from "../../../../services/notificationService";
import { BaseMessageModel } from "../../../../models/ErrorModel";
import i18next from "../../../../i18n";
import { Constants } from "../../../../models/Constants";

type ConfigureQuestionsProps = {
    indicators: IndicatorModel[];
    subObjectiveId: string;
    indicatorId: string;
    respondentType: number;
    getSurveyQuestions: (respondentType: number) => Array<SurveyQuestionModel>;
    hasSurveyQuestions: boolean;
    selectedRespondentTypeName: string;
};

/** Renders the Question Bank component to configure questions */
const ConfigureQuestions = (props: ConfigureQuestionsProps) => {
    const { t } = useTranslation();
    document.title = t("app.QuestionBankTitle");
    const location: any = useLocation();
    const navigate = useNavigate();
    const state = location?.state;
    const assessmentId = location?.state?.assessmentId;
    const respondentTypeData: Array<RespondentTypeModel> = location?.state?.respondentTypes;
    const [openFinalizeConfirmation, setOpenFinalizeConfirmation] = useState<boolean>(false);
    const [isFileDownloading, setIsFileDownloading] = useState<boolean>(false);

    const userPermission: UserAssessmentPermission = useSelector((state: any) => state.userPermission.assessment);
    // Called custom hook to get the assessment permissions based on the assessmentId
    useAssessmentPermissions(assessmentId);

    const {
        indicators,
        subObjectiveId,
        indicatorId,
        respondentType,
        getSurveyQuestions,
        hasSurveyQuestions,
        selectedRespondentTypeName
    } = props;

    const [surveyQuestions, setSurveyQuestions] = useState<Array<SurveyQuestionModel>>([]);

    // filtered survey questions based on the indicatorId, subObjectiveId and respondentType
    const filteredSurveyQuestions = surveyQuestions.filter(
        (surveyQuestion: SurveyQuestionModel) =>
            surveyQuestion.indicatorId === indicatorId && surveyQuestion.subObjectiveId === subObjectiveId
    );

    const requiredQuestionsCode = [...Constants.Common.RequiredResponseCodeSubnational,
    ...Constants.Common.RequiredResponseCodeServiceDelivery,
    ...Constants.Common.RequiredResponseCodeCommunity
    ];

    // triggers whenever HTMLInputElement change event is triggered to modify question
    const onQuestionChange = (evt: React.ChangeEvent<HTMLInputElement>, questionId: string) => {
        const value: string = evt.currentTarget.value;

        setSurveyQuestions((prevState: Array<SurveyQuestionModel>) => {
            const questions = [...prevState];
            const question = questions.find((question: SurveyQuestionModel) => question.id === questionId);

            if (question) question.modifiedQuestion = value;
            return questions;
        });
    }

    // triggers whenever HTMLInputElement change event is triggered for response options
    const onResponseOptionChange = (evt: React.ChangeEvent<HTMLInputElement>, questionId: string) => {
        const value: string = evt.currentTarget.value;

        setSurveyQuestions((prevState: Array<SurveyQuestionModel>) => {
            const questions = [...prevState];
            const question = questions.find((question: SurveyQuestionModel) => question.id === questionId);

            if (question) question.responseOptions = value;
            return questions;
        });
    }

    // Triggers when individual question checkbox on a grid is selected
    const onQuestionCheckboxChange = (evt: React.ChangeEvent<HTMLInputElement>, questionId: string) => {
        setSurveyQuestions((prevState: Array<SurveyQuestionModel>) => {
            const questions = [...prevState];
            const question = questions.find((question: SurveyQuestionModel) => question.id === questionId);

            if (question) {
                question.isSelected = evt.target.checked;
                // Sets the areAllSelected flag to false if we uncheck any of the individual question under the selected IndicatorId
                if (evt.target.checked === false) question.areAllSelected = false;
                // Checks if filteredSurveyQuestions has only one question and if it's checked areAllSelected flag is set to true
                if (filteredSurveyQuestions.length === 1 && evt.target.checked === true)
                    question.areAllSelected = true;
            }
            return questions;
        });
    };

    // Triggers when select all checkbox on a grid is selected
    const onAllQuestionsSelected = (evt: React.ChangeEvent<HTMLInputElement>) => {
        setSurveyQuestions((prevState: Array<SurveyQuestionModel>) => {
            const questions = prevState.filter((question: SurveyQuestionModel) => question.indicatorId !== indicatorId);

            const selectedQuestions = filteredSurveyQuestions.map((question: SurveyQuestionModel) =>
                ({ ...question, isSelected: evt.target.checked, areAllSelected: evt.target.checked }));

            return [...questions, ...selectedQuestions];
        });
    };

    // Saves the configured survey questions
    const onSurveyQuestionsSave = () => {
        // get the survey question based on the seletion of the question
        const selectedQuestions: Array<Question> = surveyQuestions.filter((question: SurveyQuestionModel) => (question.isMandatory || question.isSelected)).map(
            (surveyQuestion: SurveyQuestionModel) =>
                new Question(surveyQuestion.id,
                    surveyQuestion.modifiedQuestion,
                    surveyQuestion.responseOptions,
                    surveyQuestion.questionCode
                )
        );

        questionBankService
            .saveSurveyQuestions(new SurveyQuestionRequestModel(
                assessmentId,
                respondentType,
                selectedQuestions
            ))
            .then((response) => {
                if (response) {
                    const updatedRespondentTypeData: Array<RespondentTypeModel> = respondentTypeData.map(
                        (respondentData: RespondentTypeModel) => {
                            if (respondentData.respondentType === respondentType) {
                                respondentData.isSaved = true;
                            }
                            return respondentData;
                        }
                    );

                    navigate(location.pathname, { state: {
                        ...state,
                        respondentTypes: updatedRespondentTypeData
                    } });
                }
            });
    }

    // Finalize the configured survey questions
    const onSurveyQuestionsFinalize = () => {
        // finalize the survey questions
        const request: SurveyQuestionFinalizeModel = new SurveyQuestionFinalizeModel(assessmentId, respondentType);

        questionBankService
            .finalizeSurveyQuestions(request)
            .then((response) => {
                if (response) {
                    const updatedFinalizeSurveyQuestions: Array<RespondentTypeModel> = respondentTypeData.map(
                        (respondentData: RespondentTypeModel) => {
                            if (respondentData.respondentType === respondentType) {
                                respondentData.isFinalized = true;
                                respondentData.isSaved = true;
                            }
                            return respondentData;
                        }
                    );

                    navigate(location.pathname, { state: {
                        ...state,
                        respondentTypes: updatedFinalizeSurveyQuestions
                    } });
                }
            });

        setOpenFinalizeConfirmation(false);
    }

    // Checks if questionnaire is saved and sets the flag    
    const isQuestionnaireSaved: boolean = respondentTypeData?.find((respondentData: RespondentTypeModel) => respondentData.respondentType === respondentType)?.isSaved === true;

    // Checks if questionnaire is finalized and sets the flag
    const isQuestionnaireFinalized: boolean = respondentTypeData?.find((respondentData: RespondentTypeModel) => respondentData.respondentType === respondentType)?.isFinalized === true;

    // Checks if all questionnaires are finalized and sets the flag
    const areAllQuestionnairesFinalized: boolean = respondentTypeData.every((respondentData: RespondentTypeModel) => respondentData.isFinalized === true);

    // Check all questions are selected 
    const areAllQuestionsSelected: boolean = filteredSurveyQuestions.every((question: SurveyQuestionModel) => question.isSelected || question.isMandatory);

    // Checks if all questions are mandatory for the indicatorId
    const areAllQuestionsMandatory: boolean = filteredSurveyQuestions.every((question: SurveyQuestionModel) => question.isMandatory);

    const canEditQuestions = (!userPermission.canCreateOrUpdateQuestionBank || (isQuestionnaireFinalized && !userPermission.canFinalizeQuestionBank)) || false;

    // Triggered whenever finalized button is clicked
    const onFinalizeConfirmationBtnClick = (action: DialogAction) => {
        switch (action) {
            case DialogAction.Add:
                if (isQuestionnaireFinalized) {
                    onSurveyQuestionsSave();
                    setOpenFinalizeConfirmation(false);
                } else {
                    onSurveyQuestionsFinalize();
                }
                break;
            case DialogAction.Close:
                setOpenFinalizeConfirmation(false);
                break;
        }
    };

    // Triggers whenever user clicks on Generate Questionnaire button
    const onGenerateQuestionnaire = () => {
        setIsFileDownloading(true);
        questionBankService
            .generateSurveyQuestionnaire(assessmentId).then((response: any) => {
                UtilityHelper.download(response, "QuestionBank.zip");
                setIsFileDownloading(false);
            }).catch((error: any) => {
                setIsFileDownloading(false);
            });
    }

    useEffect(() => {
        const questions = getSurveyQuestions(respondentType);
        setSurveyQuestions(questions);

    }, [respondentType, hasSurveyQuestions]);

    // Question ids to show place holder for response option
    const questionIds: Array<string> = [QuestionIds.Que_2_2_1_1, QuestionIds.Que_3_3_1_1, QuestionIds.Que_3_4_1_1, QuestionIds.Que_3_5_1_3, QuestionIds.Que_3_2_1_1,];
    //for some questionsIds, placeholder text is different. Add such questionIds here.
    //Also add En and Fr PlaceHolder text in respective Jsons. Constant Variable has surveyIds (guids) of Question
    const placeHolderQuestionIds: Array<string> = [QuestionIds.Que_2_2_1_1, QuestionIds.Que_3_3_1_1, QuestionIds.Que_3_4_1_1, QuestionIds.Que_3_2_1_1];

    // Validate the response for required Questions
    const doResponceValidation = () => {

        let i = 0;
        let questionCodeList: string[];
        let errorMsg = i18next.t("Assessment.DataCollection.QuestionBankSurvey.Message.ErrorMsgRequired");
        switch (respondentType) {
            case 1:
                questionCodeList = Constants.Common.RequiredResponseCodeSubnational;
                break;
            case 2:
                questionCodeList = Constants.Common.RequiredResponseCodeServiceDelivery;
                break;
            case 3:
                questionCodeList = Constants.Common.RequiredResponseCodeCommunity;
                break;
            default:
                questionCodeList = [];
                break;
        }

        let appendErrorMsg = '';

        questionCodeList.forEach((questionCode) => {
            const surveyQuestion = surveyQuestions.find(surveyQuestions => surveyQuestions.respondentType === respondentType && surveyQuestions.questionCode === questionCode);

            if (surveyQuestion) {
                if (surveyQuestion.responseOptions === "" || surveyQuestion.responseOptions == null) {
                    const indicator = indicators.find(x => x.id === surveyQuestion.indicatorId);
                    appendErrorMsg = appendErrorMsg + ' ' + indicator?.sequence+',';
                    surveyQuestion.showError = true;
                    i++;
                } else {
                    surveyQuestion.showError = false;
                }
            }
        });

        // remove last comma
        appendErrorMsg = appendErrorMsg.substring(0, appendErrorMsg.length - 1);
        
        if (i > 0) {
            setSurveyQuestions(surveyQuestions);
            notificationService.sendMessage(
                new BaseMessageModel(
                    errorMsg + appendErrorMsg,
                    StatusCode.NotFound
                )
            );
        }
        else {
            setOpenFinalizeConfirmation(true);
        }
    }

    return (
        <>
            <fieldset disabled={canEditQuestions} className={canEditQuestions ? "disableContent" : ""}>
                <div className="survey-table h-400">
                    <Table>
                        <thead>
                            <tr>
                                <th>
                                    <Checkbox
                                        color="primary"
                                        className="p-0"
                                        label=""
                                        name="areAllSelected"
                                        inputProps={{ "aria-label": "secondary checkbox" }}
                                        checked={areAllQuestionsSelected}
                                        disabled={areAllQuestionsMandatory}
                                        onChange={(evt: React.ChangeEvent<HTMLInputElement>) => {
                                            onAllQuestionsSelected(evt);
                                        }}
                                    />
                                </th>
                                <th>{t("Assessment.DataCollection.QuestionBankSurvey.Question"
                                )}</th>
                                <th>{t("Assessment.DataCollection.QuestionBankSurvey.ResponseOptions"
                                )}</th>
                                <th>{t("Assessment.DataCollection.QuestionBankSurvey.InterviewerNotes"
                                )}</th>
                            </tr>
                        </thead>
                        <TableBody>
                            <>
                                {
                                    filteredSurveyQuestions.map((surveyQuestion: SurveyQuestionModel, index: number) =>

                                        <TableRow key={`row_${surveyQuestion.id}_${index}`}>
                                            <>
                                                <TableCell>
                                                    <label>
                                                        <Checkbox
                                                            id={`isMandatory_${surveyQuestion.id}_${index}`}
                                                            color="primary"
                                                            className="p-0"
                                                            label=""
                                                            name="surveyQuestion.isMandatory"
                                                            disabled={surveyQuestion.isMandatory}
                                                            disableRipple
                                                            disableFocusRipple
                                                            disableTouchRipple
                                                            inputProps={{ "aria-label": "secondary checkbox" }}
                                                            checked={surveyQuestion.isMandatory || surveyQuestion.isSelected}
                                                            onChange={(evt: React.ChangeEvent<HTMLInputElement>) => {
                                                                onQuestionCheckboxChange(evt, surveyQuestion.id);
                                                            }}
                                                        />
                                                    </label>
                                                </TableCell>
                                                <TableCell>
                                                    <TextBox
                                                        id={`question_${surveyQuestion.id}_${index}`}
                                                        name="question"
                                                        rows={3}
                                                        multiline
                                                        fullWidth
                                                        disabled={canEditQuestions}
                                                        value={surveyQuestion.modifiedQuestion !== null ? surveyQuestion.modifiedQuestion : surveyQuestion.question}
                                                        onChange={(evt: React.ChangeEvent<HTMLInputElement>) => {
                                                            onQuestionChange(evt, surveyQuestion.id);
                                                        }}
                                                    />
                                                </TableCell>
                                                <TableCell>
                                                    {
                                                        (requiredQuestionsCode.includes(surveyQuestion.questionCode)) &&
                                                        <span className="text-danger required-star">** </span>
                                                    }
                                                    <TextBox
                                                        id={`options_${surveyQuestion.id}_${index}`}
                                                        data-id={surveyQuestion.id}
                                                        name="options"
                                                        rows={(surveyQuestion.id).toUpperCase() === (QuestionIds.Que_3_2_1_1).toUpperCase() ? 3 : 6}
                                                        multiline
                                                        className={(surveyQuestion.id).toUpperCase() === (QuestionIds.Que_3_2_1_1).toUpperCase() ? 'customtextarea' : ''}
                                                        fullWidth
                                                        value={surveyQuestion.responseOptions !== null ? surveyQuestion.responseOptions : ((surveyQuestion.id).toUpperCase() !== (QuestionIds.Que_3_2_1_1).toUpperCase() ? surveyQuestion.options : "")}
                                                        disabled={surveyQuestion.canEditOptions === false}
                                                        onChange={(evt: React.ChangeEvent<HTMLInputElement>) => {
                                                            onResponseOptionChange(evt, surveyQuestion.id);
                                                        }}
                                                        placeholder={questionIds?.includes(surveyQuestion.id.toUpperCase()) ? placeHolderQuestionIds?.includes(surveyQuestion.id.toUpperCase()) ? t(`Assessment.DataCollection.QuestionBankSurvey.Placeholder${surveyQuestion.id.toUpperCase()}`) : t("Assessment.DataCollection.QuestionBankSurvey.ResponsePlaceholderText") : ""}
                                                        error={surveyQuestion.showError}
                                                    />
                                                    {
                                                        (surveyQuestion.id).toUpperCase() === (QuestionIds.Que_3_2_1_1).toUpperCase() &&
                                                        <div className="que_3-2-1 ">
                                                            {surveyQuestion.staticResponseOptions.split("\n").map((i, key) => {
                                                                return <div className={surveyQuestion.showError === true ? 'text-danger' : ''} key={key}>{i}</div>;
                                                            })}
                                                        </div>
                                                    }
                                                </TableCell>
                                                <TableCell className="notes-text">
                                                    <label>
                                                        {surveyQuestion.notes}
                                                    </label>
                                                </TableCell>
                                            </>
                                        </TableRow>
                                    )}
                            </>
                        </TableBody>
                    </Table>
                </div>

            </fieldset>

            <div className="response-action-wrapper">
                <div className="button-action-section d-flex justify-content-center p-3">
                    {
                        !isQuestionnaireFinalized && userPermission?.canCreateOrUpdateQuestionBank &&
                        <button
                            className="btn app-btn-secondary"
                            onClick={onSurveyQuestionsSave}>
                            {t("indicators-responses:Common:Save")}
                        </button>
                    }
                    {
                        isQuestionnaireSaved && !isQuestionnaireFinalized && userPermission?.canFinalizeQuestionBank &&
                        <button
                            className="btn app-btn-primary"
                            onClick={(evt: React.MouseEvent<HTMLButtonElement>) => {
                                evt.preventDefault();
                                //put validation here
                                doResponceValidation();
                                ;
                            }}
                        >
                            {t("Assessment.DataCollection.QuestionBankSurvey.FinalizeQuestionnaire")}
                        </button>
                    }
                    {
                        isQuestionnaireFinalized && userPermission?.canFinalizeQuestionBank &&
                        <button
                            className="btn app-btn-primary"
                            onClick={() => doResponceValidation()}
                        >
                            {t("Assessment.DataCollection.QuestionBankSurvey.FinalizeQuestionnaire")}
                        </button>
                    }
                    {
                        areAllQuestionnairesFinalized && userPermission.canUploadFile &&
                        <button
                            className="btn app-btn-primary"
                            onClick={onGenerateQuestionnaire}
                        >
                            {t("Assessment.DataCollection.QuestionBankSurvey.GenerateQuestionnaire")}
                        </button>
                    }
                </div>
            </div>

            {/* Confirmation dialog shown when finalized button is clicked */}
            <ConfirmationDialog
                open={openFinalizeConfirmation}
                title={t("Common.ConfirmationTitle")}
                content={isQuestionnaireFinalized ? `${t("Assessment.DataCollection.QuestionBankSurvey.QuestionsReFinalizeDialogContent")} ${selectedRespondentTypeName} ?` : `${t("Assessment.DataCollection.QuestionBankSurvey.QuestionsFinalizeDialogContent")} ${selectedRespondentTypeName} ${t("Assessment.DataCollection.QuestionBankSurvey.BeforeSaving")} ?`}
                onClick={onFinalizeConfirmationBtnClick}
            />

            <DownloadInProgressModal isFileDownloading={isFileDownloading} />
        </>
    );
};

export default ConfigureQuestions;