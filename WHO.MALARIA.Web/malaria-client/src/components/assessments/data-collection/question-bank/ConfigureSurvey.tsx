﻿import { Icon<PERSON>utton } from "@mui/material";
import React, { useEffect, useState } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import CancelIcon from "@mui/icons-material/Close";
import LeftIcon from "@mui/icons-material/KeyboardArrowLeft";
import { useTranslation } from "react-i18next";
import Tabs, { WHOTab } from "../../../controls/Tabs";
import { TabModel } from "../../../../models/TabModel";
import { RespondentType } from "../../../../models/Enums";
import WHOTabs from "../../../controls/WHOTabs";
import Tooltip from "../../../controls/Tooltip";
import InfoIcon from "@mui/icons-material/Info";
import InformationDialog from "../../../common/InformationDialog";
import { RespondentTypeModel } from "../../../../models/QuestionBank/RespondentTypeModel";
import { questionBankService } from "../../../../services/questionBankService";
import {
  SurveyQuestionModel,
  SubObjectiveIndicatorModel,
  IndicatorModel,
  SubObjectiveModel,
  ObjectiveModel,
} from "../../../../models/QuestionBank/SurveyQuestionModel";
import ConfigureQuestions from "./ConfigureQuestions";

/** Renders the survey questions to be configured */
const ConfigureSurvey = () => {
  const { t } = useTranslation();
  document.title = t("app.QuestionBankTitle");

  const location: any = useLocation();
  const navigate = useNavigate();
  const state = location?.state;
  const assessmentId = location?.state?.assessmentId;
  const showHowToConfigureQuestionBankModal =
    location?.state?.showHowToConfigureQuestionBankModal;

  const [currentObjectiveTabIndex, setCurrentObjectiveTabIndex] =
    useState<number>(0);
  const [currentSubObjectiveIndex, setCurrentSubObjectiveIndex] =
    useState<number>(0);
  const [currentIndicatorTabIndex, setCurrentIndicatorTabIndex] =
    useState<number>(0);
  const [objectives, setObjectives] = useState<Array<ObjectiveModel>>([]);
  const [filteredObjectives, setFilteredObjectives] = useState<
    Array<ObjectiveModel>
  >([]);
  const [selectedRespondentType, setSelectedRespondentType] =
    useState<number>(1);
  const [currentTab, setCurrentTab] = useState<number>(0);

  const [subObjectiveId, setSubObjectiveId] = useState("");
  const [objectiveId, setObjectiveId] = useState("");
  const [indicatorId, setIndicatorId] = useState("");
  const [subObjectives, setSubObjectives] = useState<Array<SubObjectiveModel>>(
    []
  );
  const [filteredSubObjectives, setfilteredSubObjectives] = useState<
    Array<SubObjectiveModel>
  >([]);
  const [assessmentIndicators, setAssessmentIndicators] = useState<
    Array<TabModel>
  >([]);
  const [indicators, setIndicators] = useState<Array<IndicatorModel>>([]);
  const [openConfigureDialog, setOpenConfigureDialog] = useState<boolean>(true);

  const respondentTypeData: Array<RespondentTypeModel> =
    location?.state?.respondentTypes;
  const [respondentType, setRespondentType] = useState<Array<TabModel>>([]);
  const [surveyQuestions, setSurveyQuestions] = useState<
    Array<SurveyQuestionModel>
  >([]);

  // Renders tabs labels for respondent types tab
  const respondentTypeTabs = (respondentType: number): string => {
    switch (respondentType) {
      case RespondentType.SubnationalLevel:
        return t(
          "Assessment.DataCollection.QuestionBankSurvey.SubnationalLevel"
        );
      case RespondentType.ServiceDeliveryLevel:
        return t(
          "Assessment.DataCollection.QuestionBankSurvey.ServiceDeliveryLevel"
        );
      case RespondentType.CommunityLevel:
        return t("Assessment.DataCollection.QuestionBankSurvey.CommunityLevel");
      default:
        return "";
    }
  };

  useEffect(() => {
    bindSurveyQuestions();
    bindObjectivesSubObjectivesIndicators();
  }, []);

  useEffect(() => {
    if (respondentTypeData?.length > 0) {
      const respondentTabs: Array<TabModel> = respondentTypeData.map(
        (respondentTab: RespondentTypeModel) => {
          return new TabModel(
            respondentTab.respondentType,
            respondentTypeTabs(respondentTab?.respondentType),
            <></>
          );
        }
      );

      setRespondentType(respondentTabs);
      setSelectedRespondentType(+respondentTabs[0].id);
    }
  }, []);

  // Triggers whenever respondent type tab is changed
  const onRespondentTabChange = (
    event: React.ChangeEvent<{}>,
    newValue: number
  ) => {
    setCurrentTab(newValue);
    const respondentId = respondentType[newValue].id;
    setSelectedRespondentType(+respondentId);
    filterObjective(objectives, subObjectives, indicators, +respondentId);
  };

  // bind objective sub-objectives and indicators based on assessmentId
  const bindObjectivesSubObjectivesIndicators = () => {
    questionBankService
      .getObjectivesSubobjectivesIndicators(assessmentId)
      .then((response: SubObjectiveIndicatorModel) => {
        setObjectives(response.objectives);
        setSubObjectives(response.subObjectives);
        setIndicators(response.indicators);
        filterObjective(
          response.objectives,
          response.subObjectives,
          response.indicators,
          respondentTypeData[0].respondentType
        );
      });
  };

  // get the filtered objectives by respondent Type
  const filterObjective = (
    objectives: Array<ObjectiveModel>,
    subObjectives: Array<SubObjectiveModel>,
    indicators: Array<IndicatorModel>,
    selectedRespondentType: number
  ) => {
    const filteredObjectives = objectives.filter(
      (objective: ObjectiveModel) =>
        objective.respondentType === selectedRespondentType
    );

    setFilteredObjectives(filteredObjectives);
    setObjectiveId(filteredObjectives[0].id);
    setCurrentObjectiveTabIndex(0);
    filterSubObjective(
      filteredObjectives[0].id,
      subObjectives,
      indicators,
      selectedRespondentType
    );
  };

  // get the filtered sub-objective by objectives
  const filterSubObjective = (
    objectiveId: string,
    subObjectives: Array<SubObjectiveModel>,
    indicators: Array<IndicatorModel>,
    selectedRespondentType: number
  ) => {
    const filteredSubObjectives = subObjectives.filter(
      (subObjective: SubObjectiveModel) =>
        subObjective.objectiveId === objectiveId &&
        subObjective.respondentType === selectedRespondentType
    );

    setfilteredSubObjectives(filteredSubObjectives);

    if (filteredSubObjectives.length) {
      filterIndicators(
        filteredSubObjectives[0].id,
        indicators,
        selectedRespondentType
      );
      setSubObjectiveId(filteredSubObjectives[0].id);
    }

    setCurrentSubObjectiveIndex(0);
  };

  // get the filtered indicators by sub-objective
  const filterIndicators = (
    subObjectiveId: string,
    indicators: Array<IndicatorModel>,
    selectedRespondentType: number
  ) => {
    const filteredIndicators = indicators.filter(
      (indicator: IndicatorModel) =>
        indicator.subObjectiveId === subObjectiveId &&
        indicator.respondentType === selectedRespondentType
    );

    const options = filteredIndicators.map(
      (filteredIndicator: IndicatorModel): TabModel =>
        new TabModel(
          filteredIndicator.id,
          `${filteredIndicator.sequence} ${filteredIndicator.name}`,
          <></>
        )
    );

    setAssessmentIndicators(options);
    setIndicatorId(filteredIndicators[0].id);
    setCurrentIndicatorTabIndex(0);
  };

  // triggers whenever user changes the objective tab
  const onTabObjectiveChange = (currentTabIndex: number) => {
    setObjectiveId(filteredObjectives[currentTabIndex].id);
    setCurrentObjectiveTabIndex(currentTabIndex);

    filterSubObjective(
      filteredObjectives[currentTabIndex].id,
      subObjectives,
      indicators,
      selectedRespondentType
    );
  };

  // triggers whenever user changes the sub-objective tab
  const onTabSubObjectiveChange = (currentSubObjectiveIndex: number) => {
    const _subObjectivesId = filteredSubObjectives[currentSubObjectiveIndex].id;

    setCurrentSubObjectiveIndex(currentSubObjectiveIndex);
    setSubObjectiveId(_subObjectivesId);
    filterIndicators(_subObjectivesId, indicators, selectedRespondentType);
  };

  // triggers whenever user changes the indicator tab
  const onIndicatorTabChange = (
    event: React.ChangeEvent<{}>,
    newValue: any
  ) => {
    const indicatorId = assessmentIndicators[newValue].id;

    setIndicatorId(indicatorId.toString());
    setCurrentIndicatorTabIndex(newValue);
  };

  // triggers whenever user takes an action on 'Close'
  const onBackOrClose = (evt: React.MouseEvent<HTMLButtonElement>) => {
    const state = location?.state;
    navigate("/assessment/data-collection/question-bank", {
      state: {
        ...state,
        showHowToConfigureQuestionBankModal:
          showHowToConfigureQuestionBankModal + 1,
      },
    });
  };

  // renders the objectives tabs
  const renderObjectivesTab = filteredObjectives.map(
    (objective: ObjectiveModel): WHOTab => {
      return { id: objective.id, label: objective.name };
    }
  );

  // renders the sub-objectives tabs
  const renderSubObjectivesTab = filteredSubObjectives.map(
    (subObjective: SubObjectiveModel): WHOTab => {
      return {
        id: subObjective.id,
        label: `${subObjective.sequence} ${subObjective.name}`,
      };
    }
  );

  // bind survey questions by assessmentId
  const bindSurveyQuestions = () => {
    questionBankService
      .getSurveyQuestionsById(assessmentId)
      .then((response: Array<SurveyQuestionModel>) => {
        if (response.length > 0) {
          // Sets the isSaved flag for selected respondentType by checking if any question isSelected
          respondentTypeData.forEach((respondentData: RespondentTypeModel) => {
            const respondentDataIsSelected: boolean = response.some(
              (data: SurveyQuestionModel) =>
                data.isSelected === true &&
                data.respondentType === respondentData.respondentType
            );

            respondentData.isSaved = respondentDataIsSelected;

            if (!respondentDataIsSelected) {
              response.forEach((question: SurveyQuestionModel) => {
                if (question.respondentType === respondentData.respondentType) {
                  question.isSelected = true;
                  question.areAllSelected = true;
                }
              });
            }
          });

          setSurveyQuestions(response);
        }
      });
  };

  // get the survey question based on the selected respondentType
  const getRespondentTypeQuestions = (respondentType: number) => {
    const respondentTypeQuestions: Array<SurveyQuestionModel> =
      surveyQuestions.filter(
        (surveyQuestion: SurveyQuestionModel) =>
          surveyQuestion.respondentType === respondentType
      );

    return respondentTypeQuestions;
  };

  return (
    <>
      <section className="page-full-section page-full-assess-section">
        <div className="header-section">
          <div className="row">
            <div className="col-sm-4 d-flex justify-content-left align-items-center">
              <IconButton onClick={onBackOrClose}>
                <LeftIcon />
              </IconButton>
              <span className="fw-bold">
                {t("Assessment.DataCollection.QuestionBankSurvey.SurveySetup")}
              </span>
            </div>
            <div className="col-md-4 d-flex justify-content-center align-items-center">
              <span className="d-block py-2">
                {t(
                  "Assessment.DataCollection.QuestionBankSurvey.ConfigureSurveyQuestionnaire"
                )}
              </span>
            </div>
            <div className="col-md-4 d-flex justify-content-end">
              <IconButton
                onClick={onBackOrClose}
                title="Close"
                className="close-modal"
              >
                <CancelIcon />
              </IconButton>
            </div>
          </div>

          <div className="row">
            <div className="col-sm-12 d-flex justify-content-center">
              <span>
                {t(
                  "Assessment.DataCollection.QuestionBankSurvey.HowToConfigure"
                )}

                <IconButton className="grid-icon-button">
                  <Tooltip
                    content={t(
                      "Assessment.DataCollection.QuestionBankSurvey.HowToConfigureTooltip"
                    )}
                    isHtml
                  >
                    <InfoIcon fontSize="small" />
                  </Tooltip>
                </IconButton>
              </span>
            </div>
          </div>
        </div>

        <div className="page-response-section p-0">
          <div className="response-wrapper">
            <div className="app-tab-wrapper">
              <WHOTabs
                tabs={respondentType}
                value={currentTab}
                onChange={onRespondentTabChange}
                scrollable={false}
              >
                <>
                  <Tabs
                    tabs={renderObjectivesTab}
                    activeTabIndex={currentObjectiveTabIndex}
                    onClick={onTabObjectiveChange}
                  >
                    <div className="category-wrapper mt-3">
                      <div className="d-flex align-items-stretch">
                        <div className="subcategories-wrapper">
                          <Tabs
                            tabs={renderSubObjectivesTab}
                            activeTabIndex={currentSubObjectiveIndex}
                            onClick={onTabSubObjectiveChange}
                          >
                            <></>
                          </Tabs>
                        </div>

                        <div className="indicators-wrapper">
                          <div className="app-tab-wrapper mb-3">
                            <WHOTabs
                              tabs={assessmentIndicators}
                              scrollable={true}
                              value={currentIndicatorTabIndex}
                              onChange={onIndicatorTabChange}
                            >
                              <>
                                <ConfigureQuestions
                                  indicators={indicators}
                                  subObjectiveId={subObjectiveId}
                                  indicatorId={indicatorId}
                                  respondentType={selectedRespondentType || 0}
                                  getSurveyQuestions={
                                    getRespondentTypeQuestions
                                  }
                                  hasSurveyQuestions={
                                    surveyQuestions.length > 0 ? true : false
                                  }
                                  selectedRespondentTypeName={respondentTypeTabs(
                                    selectedRespondentType
                                  )}
                                />
                              </>
                            </WHOTabs>
                          </div>
                        </div>
                      </div>
                    </div>
                  </Tabs>
                </>
              </WHOTabs>
            </div>
          </div>
        </div>

        {/* Modal to show how to configure questionnaire */}
        {showHowToConfigureQuestionBankModal === 1 && (
          <InformationDialog
            title={t(
              "Assessment.DataCollection.QuestionBankSurvey.HowToConfigureSurveyQuestionnaire"
            )}
            content={t(
              "Assessment.DataCollection.QuestionBankSurvey.HowToConfigureTooltip"
            )}
            open={openConfigureDialog}
            onClick={() => {
              setOpenConfigureDialog(false);
            }}
          />
        )}
      </section>
    </>
  );
};

export default ConfigureSurvey;
