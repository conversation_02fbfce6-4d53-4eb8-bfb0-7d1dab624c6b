﻿import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@mui/material";
import React, { ChangeEvent, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate,  useLocation } from "react-router-dom";
import { DialogAction, RespondentType, StatusCode } from "../../../../models/Enums";
import MultiSelectModel from "../../../../models/MultiSelectModel";
import CheckboxList from "../../../controls/CheckboxList";
import RadioButtonGroup from "../../../controls/RadioButtonGroup";
import Tooltip from "../../../controls/Tooltip";
import InfoIcon from "@mui/icons-material/Info";
import { questionBankService } from "../../../../services/questionBankService";
import { RespondentTypeModel } from "../../../../models/QuestionBank/RespondentTypeModel";
import { useSelector } from "react-redux";
import { RespondentTypeRequestModel } from "../../../../models/QuestionBank/RespondentTypeRequestModel";
import useFormValidation from "../../../common/useFormValidation";
import ValidationRules from "./ValidationRules";
import ConfirmationDialog from "../../../common/ConfirmationDialog";
import { UserAssessmentPermission } from "../../../../models/PermissionModel";
import classNames from "classnames";
import { UtilityHelper } from "../../../../utils/UtilityHelper";
import { Constants } from "../../../../models/Constants";
import { notificationService } from "../../../../services/notificationService";
import { BaseMessageModel } from "../../../../models/ErrorModel";

/** Renders the Question Bank component to configure respondent type */
const ConfigureRespondentType = () => {
    const { t } = useTranslation();
    document.title = t("app.QuestionBankTitle");

    const location: any = useLocation();
    const navigate = useNavigate();
    const state = location?.state;
    const assessmentId = location?.state?.assessmentId;
    const showHowToConfigureQuestionBankModal = location?.state?.showHowToConfigureQuestionBankModal;
    let selectedCountryId = sessionStorage.getItem(Constants.SessionStorageKey.SELECTED_COUNTRY);

    const validate = useFormValidation(ValidationRules);
    const errors = useSelector((state: any) => state.error);

    const [hasSelfAssessmentQuestions, setHasSelfAssessmentQuestions] = useState<boolean | null>(null);
    const [IsViewHealthFacilityRadioButtonSelected, setIsViewHealthFacilityRadioButtonSelected] = useState<boolean>(false);
    const [respondentTypes, setRespondentTypes] = useState<Array<RespondentTypeModel>>([RespondentTypeModel.init()]);
    const [respondentTypeValues, setRespondentTypeValues] = useState<Array<string | number>>([RespondentType.ServiceDeliveryLevel]);
    const [prevRespondentTypeData, setPrevRespondentTypeData] = useState<Array<RespondentTypeModel>>([]);
    const [openConfigureDialog, setOpenConfigureDialog] = useState<boolean>(false);
    const [hasHealthFacilityData, setHasHealthFacilityData] = useState<boolean>(false);

    const userPermission: UserAssessmentPermission = useSelector((state: any) => state.userPermission.assessment);

    const isAllRespondentTypeFinalized = !(prevRespondentTypeData.every((respondentTypeData: RespondentTypeModel) =>
        respondentTypeData.isFinalized === true
    ));
    const canEditRespondentType = (userPermission?.canEditQuestionBankRespondentType) || false;

    // triggers onRespondentType checkbox change event
    const onRespondentTypeCheckboxChange = (values: Array<string | number>) => {
        const _respondentTypes: Array<RespondentTypeModel> = values.map((value: string | number) => {
            const respondentType = respondentTypes.find((type: RespondentTypeModel) => type.respondentType === +value);

            if (respondentType) return respondentType;

            return new RespondentTypeModel(+value, false, false, null);
        });

        setRespondentTypeValues(values);
        setRespondentTypes(_respondentTypes);
    }

    //triggers on change event for self assessment
    const onSelfAssessmentChange = (evt: ChangeEvent<HTMLInputElement>) => {
        const value = evt.target.value === "true" ? true : false;

        setHasSelfAssessmentQuestions(value);
    }

    //triggers whenever click on view health facilities
    const onViewHealthFacilityChange = (evt: ChangeEvent<HTMLInputElement>) => {
        const value = evt.target.value === "true" ? true : false;

        setIsViewHealthFacilityRadioButtonSelected(value);
    }

    // Triggered whenever configure survey questionnaires is clicked
    const onConfigureRespondentTypeClick = (action: DialogAction) => {
        switch (action) {
            case DialogAction.Add:
                onRespondentTypeSave();
                break;
            case DialogAction.Close:
                setOpenConfigureDialog(false);
                break;
        }
    };

    // triggers whenever user takes an action to configure survey questionniares
    const onConfigureSurveyQuestionsClick = () => {
        if (hasHealthFacilityData) {
            const isFormValid = validate({ hasSelfAssessmentQuestions: hasSelfAssessmentQuestions });

            if (isFormValid) {
                let hasRespondentTypeDataChanged: boolean = false;
                const _respondentTypes = respondentTypeValues.map((value: string | number) => +value);

                if (prevRespondentTypeData.length > 0) {
                    // checks if the value is changed for self assessment
                    const isValueChangedForSelfAssessment: boolean = prevRespondentTypeData.every((respondentData: RespondentTypeModel) => respondentData.hasSelfAssessmentQuestions === hasSelfAssessmentQuestions);

                    if (!isValueChangedForSelfAssessment) {
                        hasRespondentTypeDataChanged = true;
                    }

                    if (hasRespondentTypeDataChanged === false) {
                        // loop through the prevRespondentTypeData and checks if value is changed for respondent type 
                        for (let index = 0; index < prevRespondentTypeData.length; index++) {
                            if (_respondentTypes.includes(prevRespondentTypeData[index].respondentType)) {
                                hasRespondentTypeDataChanged = false;
                            } else {
                                hasRespondentTypeDataChanged = true;
                                break;
                            }
                        }
                    }
                }

                if (hasRespondentTypeDataChanged) {
                    setOpenConfigureDialog(true);
                }
                else {
                    onRespondentTypeSave();
                }
            }
        }
        else {
            notificationService.sendMessage(
                new BaseMessageModel(
                    t("Assessment.DataCollection.QuestionBankSurvey.Message.HealtFacilityNoDataMessage"),
                    StatusCode.PreConditionFailed
                )
            );
        }
    }

    // saves the respondent type to configure survey question
    const onRespondentTypeSave = () => {
        questionBankService
            .saveRespondentTypes(new RespondentTypeRequestModel(
                assessmentId,
                respondentTypeValues.map((value: string | number) => +value),
                hasSelfAssessmentQuestions
            ))
            .then((success: boolean) => {
                if (success) {
                    redirectToConfigureSurveyForm();
                }
            });
    }

    // bind respondent types
    const bindRespondentTypes = () => {
        questionBankService
            .getRespondentTypesById(assessmentId)
            .then((response: Array<RespondentTypeModel>) => {
                if (response.length > 0) {
                    setRespondentTypes(response);
                    bindRespondentTypeData(response);
                    setHasSelfAssessmentQuestions(response[0].hasSelfAssessmentQuestions);
                    setPrevRespondentTypeData(response);
                }
            });
    };

    // bind respondent type data
    const bindRespondentTypeData = (response: Array<RespondentTypeModel>) => {
        const selectedRespondentTypes: Array<number> = response.map((surveyRespondentType: RespondentTypeModel) => {
            return surveyRespondentType.respondentType;
        });

        setRespondentTypeValues(selectedRespondentTypes);
    }

    // Redirects to configure survey form on next button click (when user don't have permission)
    const onNextButtonClick = () => {
        redirectToConfigureSurveyForm();
    }

    // Redirects to configure survey form
    const redirectToConfigureSurveyForm = () => {
        const sortedRespondentType: Array<RespondentTypeModel> = respondentTypes.sort((a, b) => a.respondentType - b.respondentType);

        const showHowToConfigureModal = canEditRespondentType ? (isNaN(showHowToConfigureQuestionBankModal) ? 1 : showHowToConfigureQuestionBankModal) : 2

        navigate("/assessment/data-collection/question-bank/questions/configure", { state: {
            ...state,
            assessmentId,
            respondentTypes: sortedRespondentType,
            // Used to set the count in the history and based on this how to configure survey questionnaire information modal is shown for the first time user
            showHowToConfigureQuestionBankModal: showHowToConfigureModal
        } });
    }

    // Triggers whenever user clicks on export/ view existing health facility button
    const exportExcel = () => {
        const fileName: string = t("Assessment.HealthFacilities.HealthFacilityFileName") + ".xlsx";
        questionBankService
            .exportHealthFacilityExcelForAssessment(assessmentId, selectedCountryId ?? '').then((content: any) => {
                UtilityHelper.download(content, fileName);
            });
    }

    // has health faciliy data
    const fnHasHealthFacilityData = () => {
        questionBankService
            .hasHealthFacilityData(assessmentId)
            .then((response: boolean) => {
                setHasHealthFacilityData(response);
            });
    };

    useEffect(() => {
        bindRespondentTypes();
        fnHasHealthFacilityData();
    }, []);

    return (
        <>
            <div className="h-100 row row justify-content-sm-center">
                <div className="col-sm-6">
                    <div className="checkbox-list">
                        <h6>
                            {t("Assessment.DataCollection.QuestionBankSurvey.HealthFacility")}
                            <IconButton className="grid-icon-button">
                                <Tooltip
                                    content={t("Assessment.DataCollection.QuestionBankSurvey.HealthFacilityTooltip"
                                    )}
                                    isHtml
                                >
                                    <InfoIcon fontSize="small" />
                                </Tooltip>
                            </IconButton>
                        </h6>
                        <div className="px-3 row">
                            <div className="col-sm-6">
                                <RadioButtonGroup
                                    id="IsViewHealthFacilityRadioButtonSelected"
                                    name="IsViewHealthFacilityRadioButtonSelected"
                                    row
                                    color="primary"
                                    options={[
                                        new MultiSelectModel(
                                            true,
                                            t("indicators-responses:Common:Yes")
                                        ),
                                        new MultiSelectModel(
                                            false,
                                            t("indicators-responses:Common:No")
                                        ),
                                    ]}
                                    value={IsViewHealthFacilityRadioButtonSelected}
                                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                                        onViewHealthFacilityChange(e);
                                    }}
                                    error={errors["IsViewHealthFacilityRadioButtonSelected"] && errors["IsViewHealthFacilityRadioButtonSelected"]}
                                    helperText={errors["IsViewHealthFacilityRadioButtonSelected"] && errors["IsViewHealthFacilityRadioButtonSelected"]}
                                />
                            </div>
                            <div className="col-sm-6">
                                {IsViewHealthFacilityRadioButtonSelected &&
                                    <div className="button-action-section d-flex justify-content-end">
                                        <Button className={classNames("btn", "app-btn-secondary")}
                                            onClick={exportExcel}>
                                            {t("Assessment.HealthFacilities.ViewHealthFacility")}
                                        </Button>
                                    </div>
                                }
                            </div>
                        </div>
                    </div>
                    <fieldset disabled={!canEditRespondentType} className={!canEditRespondentType ? "disableContent" : ""}>
                        <div className="border p-3">
                            <p>{t("Assessment.DataCollection.QuestionBankSurvey.RespondentTypeHeading")}</p>

                            <div className="checkbox-list">
                                <h6>
                                    {t("Assessment.DataCollection.QuestionBankSurvey.RespondentType")}

                                    <IconButton className="grid-icon-button">
                                        <Tooltip
                                            content={t("Assessment.DataCollection.QuestionBankSurvey.RespondentTypeTooltip"
                                            )}
                                            isHtml
                                        >
                                            <InfoIcon fontSize="small" />
                                        </Tooltip>
                                    </IconButton>
                                </h6>
                                <CheckboxList
                                    options={[
                                        new MultiSelectModel(
                                            RespondentType.SubnationalLevel,
                                            t("Assessment.DataCollection.QuestionBankSurvey.SubnationalLevel"),
                                            !canEditRespondentType
                                        ),
                                        new MultiSelectModel(
                                            RespondentType.ServiceDeliveryLevel,
                                            t("Assessment.DataCollection.QuestionBankSurvey.ServiceDeliveryLevel"),
                                            true
                                        ),
                                        new MultiSelectModel(
                                            RespondentType.CommunityLevel,
                                            t("Assessment.DataCollection.QuestionBankSurvey.CommunityLevel"),
                                            !canEditRespondentType
                                        ),
                                    ]}
                                    selectedItems={respondentTypeValues}
                                    onClick={onRespondentTypeCheckboxChange}
                                />
                            </div>

                            <div className="checkbox-list">
                                <h6>
                                    {t("Assessment.DataCollection.QuestionBankSurvey.SelfAssesment")}

                                    <IconButton className="grid-icon-button">
                                        <Tooltip
                                            content={t("Assessment.DataCollection.QuestionBankSurvey.SelfAssesmentTooltip"
                                            )}
                                            isHtml
                                        >
                                            <InfoIcon fontSize="small" />
                                        </Tooltip>
                                    </IconButton>
                                </h6>

                                <div className="px-3">
                                    <RadioButtonGroup
                                        id="hasSelfAssessmentQuestions"
                                        name="hasSelfAssessmentQuestions"
                                        row
                                        color="primary"
                                        options={[
                                            new MultiSelectModel(
                                                true,
                                                t("indicators-responses:Common:Yes")
                                            ),
                                            new MultiSelectModel(
                                                false,
                                                t("indicators-responses:Common:No")
                                            ),
                                        ]}
                                        value={hasSelfAssessmentQuestions}
                                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                                            onSelfAssessmentChange(e);
                                        }}
                                        error={errors["hasSelfAssessmentQuestions"] && errors["hasSelfAssessmentQuestions"]}
                                        helperText={errors["hasSelfAssessmentQuestions"] && errors["hasSelfAssessmentQuestions"]}
                                    />
                                </div>
                            </div>
                        </div>
                    </fieldset>
                </div>
            </div>

            <div className="button-action-section d-flex justify-content-center p-3">
                {canEditRespondentType || (isAllRespondentTypeFinalized && userPermission?.canCreateOrUpdateQuestionBank) ?
                    (
                        <Button
                            className="btn app-btn-secondary"
                            onClick={() => canEditRespondentType ? onConfigureSurveyQuestionsClick() : onNextButtonClick()}
                        >
                            {t("Assessment.DataCollection.QuestionBankSurvey.ConfigureSurvey")}
                        </Button>
                    ) : (

                        <Button
                            className="btn app-btn-secondary"
                            onClick={() => onNextButtonClick()}
                        >
                            {t("Common.Next")}
                        </Button>
                    )}
            </div>

            {/* Confirmation dialog shown when configure survey is updated */}
            <ConfirmationDialog
                open={openConfigureDialog}
                title={t("Common.ConfirmationTitle")}
                content={t("Assessment.DataCollection.QuestionBankSurvey.ConfigureSurveyDialogContent")}
                onClick={onConfigureRespondentTypeClick}
            />
        </>
    );
};

export default ConfigureRespondentType;