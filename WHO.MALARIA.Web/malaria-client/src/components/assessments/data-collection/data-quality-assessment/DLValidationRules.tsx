﻿import { Constants } from '../../../../models/Constants';
import { DataType } from '../../../../models/Enums';
import ValidationRuleModel, { IValidationRuleProvider } from '../../../../models/ValidationRuleModel';

/** Service level validation rules */
const DLValidationRules: IValidationRuleProvider = {
    "dataSystemsOneId": new ValidationRuleModel(DataType.String, true),
    "dataSystemsTwoId": new ValidationRuleModel(DataType.String, false),
    "priorityVariableIds": new ValidationRuleModel(DataType.ArrayOfObject, true, `${Constants.Common.RootObjectNameSubstitute}.priorityVariableIds === null`),
    "concordanceVariables": new ValidationRuleModel(DataType.String, false, `${Constants.Common.RootObjectNameSubstitute}.dataSystemsTwoId &&${Constants.Common.RootObjectNameSubstitute}.priorityVariableIds.length > 0 && ${Constants.Common.RootObjectNameSubstitute}.concordanceVariables.length === 0`),
    "otherDataSystem1SourceName": new ValidationRuleModel(DataType.String, false, `${Constants.Common.RootObjectNameSubstitute}.isOtherDataSystem1Selected === true &&${Constants.Common.RootObjectNameSubstitute}.otherDataSystem1SourceName && ${Constants.Common.RootObjectNameSubstitute}.otherDataSystem1SourceName.toLowerCase().trim() === ${Constants.Common.RootObjectNameSubstitute}.otherDataSystem2SourceName.toLowerCase().trim()`, "Errors.DifferentOtherDataSystemName"),
    "otherDataSystem2SourceName": new ValidationRuleModel(DataType.String, false, `${Constants.Common.RootObjectNameSubstitute}.isOtherDataSystem2Selected === true && ${Constants.Common.RootObjectNameSubstitute}.otherDataSystem2SourceName && ${Constants.Common.RootObjectNameSubstitute}.otherDataSystem1SourceName.toLowerCase().trim() === ${Constants.Common.RootObjectNameSubstitute}.otherDataSystem2SourceName.toLowerCase().trim()`, "Errors.DifferentOtherDataSystemName"),
}

export default DLValidationRules;
