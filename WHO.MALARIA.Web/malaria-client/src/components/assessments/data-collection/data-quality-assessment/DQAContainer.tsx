﻿import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import WHOTabs from "../../../controls/WHOTabs";
import { TabModel } from "../../../../models/TabModel";
import DeskLevelDeliveryComponent from "./DeskLevelDeliveryComponent";
import ServiceLevelDeliveryComponent from "./ServiceLevelDeliveryComponent";
import { dqaService } from "../../../../services/dqaService";
import { VariableModel } from "../../../../models/VariableModel";
import { DQAVariableApplicableFor, Preference, StrategiesEnum } from "../../../../models/Enums";
import { DataSourceModel } from "../../../../models/DQA/DataSourceModel";
import { But<PERSON> } from "@mui/material";
import { Utility<PERSON>elper } from "../../../../utils/UtilityHelper";
import DownloadInProgressModal from "../../../controls/DownloadInProgressModal";
import parse from "html-react-parser";
import { Constants } from "../../../../models/Constants";

type DQAContainerProps = {
    strategyId: string;
    assessmentId: string;
};

/** Renders the DQA Container for data collection and all components underneath */
const DQAContainer = (props: DQAContainerProps) => {
    const { t } = useTranslation();
    document.title = t("app.DQATitle");
    const { strategyId, assessmentId } = props;
    const [currentTab, setCurrentTab] = useState<number>(0);
    const [priorityVariables, setPriorityVariables] = useState<Array<VariableModel>>([]);
    const [optionalVariables, setOptionalVariables] = useState<Array<VariableModel>>([]);
    const [serviceLevelVariables, setServiceLevelVariables] = useState<Array<VariableModel>>([]);
    const [dataSources, setDataSources] = useState<Array<DataSourceModel>>([]);
    const [isFileDownloading, setIsFileDownloading] = useState<boolean>(false);

    const currentlanguage = UtilityHelper.getCookieValue("i18next");

    useEffect(() => {
        getDQACoreVariables();
        getDataSources();
    }, [strategyId, currentlanguage]);

    // get the core variables for DQA
    const getDQACoreVariables = () => {
        if (!strategyId) return;

        dqaService
            .getDQACoreVariables(strategyId, false)
            .then((response: Array<VariableModel>) => {
                setPriorityVariables(response.filter(variable => variable.priority === Preference.Priority));
                setOptionalVariables(response.filter(variable => variable.priority === Preference.Optional));
                setServiceLevelVariables(response.filter(variable => variable.priority === Preference.Priority && (variable.applicableFor === DQAVariableApplicableFor.Both || variable.applicableFor === DQAVariableApplicableFor.ServiceLevel)));
            });
    };

    // get the data sources for DQA
    const getDataSources = () => {
        dqaService
            .getDataSources()
            .then((response: Array<DataSourceModel>) => {
                setDataSources(response);
            });
    };

    // Triggers whenever tab is changed
    const onTabChange = (event: React.ChangeEvent<{}>, newValue: any) => {
        setCurrentTab(newValue);
    };

    // Triggers whenever user clicks on Generate Template button
    const onGenerateTemplate = () => {
        setIsFileDownloading(true);
        const downloadedTemplateName =
        t("app.DQAEliminationFileName")+".xlsx";
        dqaService
            .exportEliminationExcelTemplate().then((response: any) => {
                UtilityHelper.download(response, downloadedTemplateName);
                setIsFileDownloading(false);
            }).catch((error: any) => {
                setIsFileDownloading(false);
            });
    }

    // Renders tabs for Data Quality Assessment
    const assessmentQualitiestabs: Array<TabModel> = [
        new TabModel(
            "0",
            `${t("Assessment.DataCollection.DataQualityAssessment.DeskLevelDelivery")}`,
            <DeskLevelDeliveryComponent
                strategyId={strategyId}
                variables={priorityVariables}
                optionalVariables={optionalVariables}
                dataSources={dataSources}
                assessmentId={assessmentId}
            />
        ),
        new TabModel(
            "1",
            `${t("Assessment.DataCollection.DataQualityAssessment.ServiceLevelDelivery")}`,
            <ServiceLevelDeliveryComponent
                strategyId={strategyId}
                variables={priorityVariables}
                assessmentId={assessmentId}
            />
        ),
    ];

    // Renders tabs for Data Quality Assessment
    const assessmentQualitiesBothTabs: Array<TabModel> = [
        new TabModel(
            "0",
            `${t("Assessment.DataCollection.DataQualityAssessment.DeskLevelDeliveryBurden")}`,
            <DeskLevelDeliveryComponent
                strategyId={strategyId}
                variables={priorityVariables}
                optionalVariables={optionalVariables}
                dataSources={dataSources}
                assessmentId={assessmentId}
            />
        ),
        new TabModel(
            "1",
            `${t("Assessment.DataCollection.DataQualityAssessment.ServiceLevelDeliveryBurden")}`,
            <ServiceLevelDeliveryComponent
                strategyId={strategyId}
                variables={serviceLevelVariables}
                assessmentId={assessmentId}
            />
        ),
        new TabModel(
            "2",
            `${t("Assessment.DataCollection.DataQualityAssessment.DQAElimination")}`,
            <div className="p-2">
                <div>
                    {parse(t("Assessment.DataCollection.DataQualityAssessment.DQAEliminationDescription"))}
                </div>
                <div className="response-action-wrapper h-300 d-flex align-items-center justify-content-center">
                    <div className="button-action-section d-flex justify-content-center p-3">
                        <Button className="btn app-btn-secondary" onClick={onGenerateTemplate}>
                            {t("Common.DownloadTemplate")}
                        </Button>
                    </div>
                </div>
            </div>
        ),
    ];

    return (
        <section className="page-full-section">
            <>
                {!(strategyId === StrategiesEnum.Elimination.toLowerCase()) ?
                    (
                        strategyId === StrategiesEnum.Both.toLowerCase() ?
                            <div className="app-tab-wrapper">
                                <WHOTabs
                                    tabs={assessmentQualitiesBothTabs}
                                    value={currentTab}
                                    onChange={onTabChange}
                                    scrollable={false}
                                >
                                    <> {assessmentQualitiesBothTabs[currentTab].children} </>
                                </WHOTabs>
                            </div>
                            :
                            <div className="app-tab-wrapper">
                                <WHOTabs
                                    tabs={assessmentQualitiestabs}
                                    value={currentTab}
                                    onChange={onTabChange}
                                    scrollable={false}
                                >
                                    <> {assessmentQualitiestabs[currentTab].children} </>
                                </WHOTabs>
                            </div>
                    )
                    :
                    <>
                        <div className="p-2">
                            <div>
                                {parse(t("Assessment.DataCollection.DataQualityAssessment.DQAEliminationDescription"))}
                            </div>
                            <div className="response-action-wrapper h-300 d-flex align-items-center justify-content-center">
                                <div className="button-action-section d-flex justify-content-center p-3">
                                    <Button className="btn app-btn-secondary" onClick={onGenerateTemplate}>
                                        {t("Common.DownloadTemplate")}
                                    </Button>
                                </div>
                            </div>
                        </div>
                    </>
                }

                <DownloadInProgressModal isFileDownloading={isFileDownloading} />
            </>
        </section>
    )
}

export default DQAContainer;