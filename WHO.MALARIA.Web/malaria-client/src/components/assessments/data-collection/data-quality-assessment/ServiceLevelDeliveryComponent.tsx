﻿import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import classNames from "classnames";
import classes from "../data-quality-assessment/dqa.module.scss";
import VariablesSelection from "./template-generation/VariablesSelection";
import Dropdown from "../../../controls/Dropdown";
import MultiSelectModel from "../../../../models/MultiSelectModel";
import useYears from "../useYears";
import DatePicker from "../../../controls/DatePicker";
import { Button, IconButton } from "@mui/material";
import ConfirmationDialog from "../../../common/ConfirmationDialog";
import FileUploader from "../../../../components/controls/FileUploader";
// import { Date | null } from "@material-ui/pickers/typings/date";
import { VariableModel } from "../../../../models/VariableModel";
import CheckboxList from "../../../controls/CheckboxList";
import { DialogAction, DQASLRegisterType } from "../../../../models/Enums";
import { ServiceLevelFinalizeModel, ServiceLevelRequestModel } from "../../../../models/DQA/ServiceLevelRequestModel";
import { Constants } from "../../../../models/Constants";
import { dqaService } from "../../../../services/dqaService";
import { getMonth, getYear } from "date-fns";
import { UtilityHelper } from "../../../../utils/UtilityHelper";
import { useSelector } from "react-redux";
import useFormValidation from "../../../common/useFormValidation";
import ValidationRules from "./ValidationRules";
import format from "date-fns/format";
import { UserAssessmentPermission } from "../../../../models/PermissionModel";
import Tooltip from "../../../controls/Tooltip";
import InfoIcon from "@mui/icons-material/Info";
import DownloadInProgressModal from "../../../controls/DownloadInProgressModal";
import { AssessmentStatus } from "../../../../models/Enums";
import { useLocation } from "react-router-dom";

type DeskLevelDeliveryProps = {
    strategyId: string;
    variables?: Array<VariableModel>;
    assessmentId: string;
};

/** Renders the Desk Level Delivery Component for Data Quality Assessment */
const ServiceLevelDeliveryComponent = (props: DeskLevelDeliveryProps) => {
    const { t } = useTranslation();
    document.title = t("app.DQATitle");
    const years = useYears();
    const { variables, assessmentId } = props;
    const [serviceLevelData, setServiceLevelData] = useState<ServiceLevelRequestModel>(ServiceLevelRequestModel.init(assessmentId));
    const [openFinalizeConfirmation, setOpenFinalizeConfirmation] = useState<boolean>(false);
    const [isTemplateUploaded, setIsTemplateUploaded] = useState<boolean>(false);
    const [isFileSizeMaximum, setIsFileSizeMaximum] = useState<boolean>(false);
    const [isValidFileExtension, setIsValidFileExtension] = useState<boolean>(false);
    const validate = useFormValidation(ValidationRules);
    const errors = useSelector((state: any) => state.error);
    const userPermission: UserAssessmentPermission = useSelector((state: any) => state.userPermission.assessment);
    const [isFileDownloading, setIsFileDownloading] = useState<boolean>(false);
    let selectedCountryName = sessionStorage.getItem(Constants.SessionStorageKey.SELECTED_COUNTRY_NAME);

    const currentlanguage = UtilityHelper.getCookieValue("i18next");

    const location: any = useLocation();
    const state = location?.state;
    const assessmentStatus = state?.status;

    // User can edit the form when if the user has already uploaded template summary data or the user doesn't have edit permission after finalizing service level data
    const canEditForm = (serviceLevelData?.hasSummaryData || (!userPermission.canFinalizeServiceLevel && serviceLevelData?.isFinalized));

    // Show file upload button when user has finalized the form Data Or user already uploaded the template data also if the user has permission to upload template data    
    const canUploadFile = ((!serviceLevelData?.isReFinalized && serviceLevelData?.isFinalized) || serviceLevelData?.hasSummaryData) && userPermission.canUploadFile;

    useEffect(() => {
        getServiceLevelData(assessmentId);
    }, []);

    // Triggered whenever finalized button is clicked
    const onFinalizeConfirmationBtnClick = (action: DialogAction) => {
        switch (action) {
            case DialogAction.Add:
                onFinalize();
                break;
            case DialogAction.Close:
                onCancelReFinalize();
                break;
        }
    };

    // Trigger whenever any variable selection changed or updated
    const updateServiceLevelVariableData = (variables: Array<string>) => {
        setServiceLevelData({
            ...serviceLevelData,
            variableIds: variables,
            isReFinalized: true //If any changes are being reFinalized, show a cancel button
        });
    }

    // Triggers whenever user tries to modify the from date
    const onFromDateChange = (date: string) => {
        
        let formatedDate :string = UtilityHelper.getValidDateFormat(currentlanguage, date);
        
        const _serviceLevelData = {
            ...serviceLevelData,
            from: formatedDate ,
            isReFinalized: true //If any changes are being reFinalized, show a cancel button
        };

        if (!checkDateIsValid(formatedDate)) {
            const fromDate = UtilityHelper.getFromDate(getYear(new Date(formatedDate)), getMonth(new Date(formatedDate)));
            const endDate = UtilityHelper.getEndDate(formatedDate);
            _serviceLevelData.fromValidationStartDate = fromDate;
            _serviceLevelData.fromStartDate = UtilityHelper.getStartDate(formatedDate);
            _serviceLevelData.toEndDate = endDate;
            _serviceLevelData.to = endDate;
            _serviceLevelData.toValidationEndDate = endDate;
            _serviceLevelData.validationPeriodStartDate = fromDate;
        }

        setServiceLevelData(_serviceLevelData);
    };

    // Triggers whenever user tries to modify the to date
    const onToDateChange = (date: string) => {

        let formatedDate :string = UtilityHelper.getValidDateFormat(currentlanguage, date);
        
        const _serviceLevelData = {
            ...serviceLevelData,
            to: formatedDate,
            isReFinalized: true //If any changes are being reFinalized, show a cancel button
        };

        if (!checkDateIsValid(formatedDate)) {
            if (_serviceLevelData.from) {
                const fromDate = UtilityHelper.getFromDate(getYear(new Date(_serviceLevelData.from)), getMonth(new Date(_serviceLevelData.from)));
                _serviceLevelData.fromValidationStartDate = fromDate;
                _serviceLevelData.validationPeriodStartDate = fromDate;
            }

            if (_serviceLevelData.to) {
                _serviceLevelData.toValidationEndDate = UtilityHelper.getToDate(getYear(new Date(formatedDate)), getMonth(new Date(formatedDate)));
            }
        }
        setServiceLevelData(_serviceLevelData);
    };

    // Check date is valid or not
    const checkDateIsValid = (date: string) => {
        let isValidDate = Date.parse(date);
        return isNaN(isValidDate);
    }


    // Triggers whenever user tries to modify the validation period start date
    const onValidationPeriodStartDateChange = (date: Date | null) => {
        const _serviceLevelData = {
            ...serviceLevelData,
            isReFinalized: true //If any changes are being reFinalized, show a cancel button
        };

        if (date === null) {
            _serviceLevelData.validationPeriodStartDate = "";
        }
        else if (!checkDateIsValid(String(date))) {
            _serviceLevelData.validationPeriodStartDate = format(new Date(String(date)), Constants.Common.DefaultDateFormat);
        }

        setServiceLevelData(_serviceLevelData);
    };

    // Triggers whenever user tries to modify the value
    const onChange = (evt: React.ChangeEvent<HTMLInputElement>) => {
        const name: string = evt.currentTarget.name;
        const value: string = evt.currentTarget.value;
        const _serviceLevelData = {
            ...serviceLevelData,
            [name]: value,
            isReFinalized: true //If any changes are being reFinalized, show a cancel button
        };

        setServiceLevelData(_serviceLevelData);
    };

    // triggers the on checkbox change event 
    const onRegisterCheckboxChange = (values: Array<string | number>) => {
        const _serviceLevelData = {
            ...serviceLevelData,
            registerTypes: values,
            isReFinalized: true //If any changes are being reFinalized, show a cancel button
        };
        setServiceLevelData(_serviceLevelData);
    }



    // Triggers whenever user clicks on Save button
    const onSave = () => {
        const isFormValid = validate(serviceLevelData);
        if (isFormValid) {
            if (serviceLevelData.serviceLevelId) {
                dqaService.updateServiceLevelDQA(serviceLevelData);
            } else {
                dqaService.saveServiceLevelDQA(serviceLevelData)
                    .then((serviceLevelId) => {
                        if (serviceLevelId) {
                            setServiceLevelData({
                                ...serviceLevelData,
                                serviceLevelId: serviceLevelId as unknown as string,
                            });
                        }
                    });
            }
        }
    }

    // Triggers whenever user clicks on 'Yes' button of confirmation dialog button
    const onFinalize = () => {
        if (serviceLevelData.isFinalized) {
            // Re-finalize the service level dqa with update all data
            dqaService.updateServiceLevelDQA(serviceLevelData)
                .then((response) => {
                    setServiceLevelData({
                        ...serviceLevelData,
                        isReFinalized: false,
                    });
                });
        } else {
            // finalize the service level dqa with only update status
            const request: ServiceLevelFinalizeModel = new ServiceLevelFinalizeModel(serviceLevelData.serviceLevelId, assessmentId);

            dqaService.finalizeServiceLevel(request)
                .then((response) => {
                    if (response) {
                        setServiceLevelData({
                            ...serviceLevelData,
                            isFinalized: response as boolean,
                            isReFinalized: false
                        });
                    }
                });
        }
        setOpenFinalizeConfirmation(false);
    };

    // Triggers whenever user clicks on re-finalize button
    const onReFinalize = (evt: React.MouseEvent<HTMLButtonElement>) => {
        evt.preventDefault();
        const isFormValid = validate(serviceLevelData);
        if (isFormValid) {
            setServiceLevelData({
                ...serviceLevelData,
                isReFinalized: true
            });
            setOpenFinalizeConfirmation(true);
        }
    }

    // Triggers whenever user clicks on cancel button
    const onCancelReFinalize = () => {
        if (serviceLevelData.isReFinalized) {
            getServiceLevelData(assessmentId);
        } else {
            setServiceLevelData({
                ...serviceLevelData,
                isReFinalized: false
            });
        }
        setOpenFinalizeConfirmation(false);
    }

    // Triggers whenever user clicks on upload button
    const uploadFile = (evt: React.ChangeEvent<HTMLInputElement>) => {
        // check file size
        const filesize = evt.target.files ? evt.target.files[0].size : 0;
        if (filesize > Constants.Common.MaxFileSize) //25MB
        {
            setIsFileSizeMaximum(true);
            return;
        } else {
            setIsFileSizeMaximum(false);
        }

        serviceLevelData.fileName = evt.target.files ? evt.target.files[0].name : '';
        // check file extension        
        const fileExtension: string | undefined = evt.target.files ? evt.target.files[0].name.split('.').pop() : "";
        if (Constants.Common.ValidExcelExtensions.indexOf(fileExtension as string) === -1) {
            setIsValidFileExtension(true);
            return;
        } else {
            setIsValidFileExtension(false);
        }

        // since we are passing file along with other information for which we are creating 
        // FormData object to pass other properties along with file so it can be mapped at server side
        setIsTemplateUploaded(true);
        const formData = new FormData();
        formData.append("ServiceLevelId", serviceLevelData.serviceLevelId);
        formData.append("AssessmentId", assessmentId);
        formData.append("File", evt.target.files ? evt.target.files[0] : "");

        dqaService.uploadServiceLevelTemplateResponse(formData).then((response) => {
            if (response) {
                setIsTemplateUploaded(false);
                setServiceLevelData({
                    ...serviceLevelData,
                    hasSummaryData: true
                });
            }
        }).catch((error: any) => {
            serviceLevelData.fileName = "";
            setIsTemplateUploaded(false);
        });
    }

    // Triggers whenever user clicks on Generate Template button
    const onGenerateTemplate = () => {
        const startMonth = Constants.Common.Months[getMonth(new Date(serviceLevelData.from))];
        const endMonth = Constants.Common.Months[getMonth(new Date(serviceLevelData.to))];
        const year = getYear(new Date(serviceLevelData.to));

        const downloadedTemplateName =
        t("app.DqaFileName") +
        "_" +
        selectedCountryName +
        "_" +
        t(`Month.${startMonth}`) +
        "_" +
        t(`Month.${endMonth}`) +
        "_" +
        year;

        setIsFileDownloading(true);
        dqaService
            .generateTemplateServiceLevelDQA(serviceLevelData.serviceLevelId).then((response: any) => {
                UtilityHelper.download(response, downloadedTemplateName);
                setIsFileDownloading(false);
            }).catch((error: any) => {
                setIsFileDownloading(false);
            });
    }

    // Get service level dqa response by assessmentId
    const getServiceLevelData = (assessmentId: string) => {
        dqaService.getServiceLevelData(assessmentId).then((response) => {

            if (response) {
                setServiceLevelData({
                    ...response,
                    assessmentId: assessmentId,
                    fromStartDate: UtilityHelper.getStartDate(response.from),
                    toEndDate: UtilityHelper.getEndDate(response.from),
                    fromValidationStartDate: response.from,
                    toValidationEndDate: UtilityHelper.getToDate(getYear(new Date(response.to)), getMonth(new Date(response.to)))
                });
            }
        });
    }

    return (
        <>
            <div
                className={classNames(
                    "d-flex",
                    "align-items-stretch",
                    "mt-4",
                    classes.categoryWrapper
                )}
            >

                <div className={classNames("indicators-wrapper sl-wrapper", classes.indicatorsWrapper)}>
                    <fieldset disabled={canEditForm} className={canEditForm ? "row disableContent" : "row"}>
                        <div className="col-xs-12 col-md-5">
                            {/*Time Period Section*/}
                            <div className="time-period-wrapper mt-2 mb-3">
                                <small className="fw-lighter d-flex align-items-center">
                                    * {t("Assessment.DataCollection.DataQualityAssessment.SelectTimePeriod")}
                                    <IconButton className="grid-icon-button">
                                        <Tooltip
                                            content={t("Assessment.DataCollection.DataQualityAssessment.SelectTimePeriodTooltip"
                                            )}
                                            isHtml
                                        >
                                            <InfoIcon fontSize="small" />
                                        </Tooltip>
                                    </IconButton>
                                </small>

                                <div className="row">
                                    <div className={canEditForm ? "col-sm-6 disableCalendarContent" : "col-sm-6"}>
                                        <DatePicker
                                            id="from"
                                            name="from"
                                            value={serviceLevelData?.from}
                                            label={t("Common.From")}
                                            onChange={(
                                                date: Date | null | null,
                                                value?: string | null
                                            ) => onFromDateChange(value as string)}
                                            InputLabelProps={{ required: false, shrink: true }}
                                            size="small"
                                            disableToolbar={true}
                                            placeholder={t("Common.MonthYear")}
                                            views={["year", "month"]}
                                            format={"MMM/yyyy"}
                                            openTo="date"
                                            error={errors[`from`] && errors[`from`]}
                                            helperText={errors[`from`] && errors[`from`]}
                                        />
                                    </div>

                                    <div className={canEditForm ? "col-sm-6 disableCalendarContent" : "col-sm-6"}>
                                        <DatePicker
                                            id="to"
                                            name="to"
                                            value={serviceLevelData?.to}
                                            label={t("Common.To")}
                                            onChange={(
                                                date: Date | null | null,
                                                value?: string | null
                                            ) => onToDateChange(value as string)}
                                            InputLabelProps={{ required: false, shrink: true }}
                                            size="small"
                                            disableToolbar={true}
                                            placeholder={t("Common.MonthYear")}
                                            views={["year", "month"]}
                                            format={"MMM/yyyy"}
                                            openTo="date"
                                            minDate={serviceLevelData?.fromStartDate}
                                            maxDate={serviceLevelData?.toEndDate}
                                            error={errors[`to`] && errors[`to`]}
                                            helperText={errors[`to`] && errors[`to`]}
                                        />
                                    </div>
                                </div>

                                <div className="row mt-3">
                                    <div className={canEditForm ? "col-sm-12 disableCalendarContent" : "col-sm-12"}>
                                        <DatePicker
                                            id="validationPeriodStartDate"
                                            name="validationPeriodStartDate"
                                            value={serviceLevelData?.validationPeriodStartDate}
                                            label={t("Assessment.DataCollection.DataQualityAssessment.ValidationPeriodStartDate")}
                                            onChange={(
                                                date: Date | null | null,
                                                value?: string | null
                                            ) => onValidationPeriodStartDateChange(date)}
                                            InputLabelProps={{ required: false, shrink: true }}
                                            size="small"
                                            disableToolbar={true}
                                            placeholder={t("Common.MonthDate")}
                                            views={["date", "month"]}
                                            format={"MMM/dd"}
                                            openTo="date"
                                            minDate={serviceLevelData?.fromValidationStartDate}
                                            maxDate={serviceLevelData?.toValidationEndDate}
                                            helperText={errors[`validationPeriodStartDate`] && errors[`validationPeriodStartDate`]}
                                            error={errors[`validationPeriodStartDate`] && errors[`validationPeriodStartDate`]}
                                        />
                                    </div>
                                </div>

                                <div className="row mt-3 d-none">
                                    <div className="col-sm-6">
                                        <Dropdown
                                            name="year"
                                            id="year"
                                            variant="outlined"
                                            size="small"
                                            label={t("Common.SelectYear")}
                                            options={years.map((year) => {
                                                return new MultiSelectModel(
                                                    year,
                                                    year.toString(),
                                                    false,
                                                    false
                                                );
                                            })}
                                            onChange={onChange}
                                            value={serviceLevelData?.year}
                                            error={errors[`year`] && errors[`year`]}
                                            helperText={errors[`year`] && errors[`year`]}
                                        />
                                    </div>
                                </div>
                            </div>

                            {/*Register Type Section*/}
                            <div className="register-type-wrapper mt-5 mb-3">
                                <small className="fw-lighter d-flex align-items-center">
                                    * {t("Assessment.DataCollection.DataQualityAssessment.SelectRegisterType")}
                                    <IconButton className="grid-icon-button">
                                        <Tooltip
                                            content={t("Assessment.DataCollection.DataQualityAssessment.SelectRegisterTypeTooltip"
                                            )}
                                            isHtml
                                        >
                                            <InfoIcon fontSize="small" />
                                        </Tooltip>
                                    </IconButton>
                                </small>

                                <div className="checkbox-list">
                                    <CheckboxList
                                        options={[
                                            new MultiSelectModel(
                                                DQASLRegisterType.OutpatientRegister,
                                                t("Assessment.DataCollection.DataQualityAssessment.OutpatientRegister")
                                            ),
                                            new MultiSelectModel(
                                                DQASLRegisterType.InpatientRegister,
                                                t("Assessment.DataCollection.DataQualityAssessment.InpatientRegister")
                                            ),
                                            new MultiSelectModel(
                                                DQASLRegisterType.LabRegister,
                                                t("Assessment.DataCollection.DataQualityAssessment.LabRegister")
                                            ),
                                        ]}
                                        selectedItems={serviceLevelData?.registerTypes}
                                        onClick={onRegisterCheckboxChange}
                                    />
                                </div>
                                {
                                    errors["registerTypes"] &&
                                    <small className="Mui-error d-flex mt-2 mx-3">
                                        {t("Assessment.DataCollection.DataQualityAssessment.RegisterTypeError")}
                                    </small>
                                }
                            </div>
                        </div>

                        <div className="col-xs-12 col-md-7">
                            <small className="fw-lighter d-flex align-items-center">
                                * {t("Assessment.DataCollection.DataQualityAssessment.SelectCoreVariables")}
                                <IconButton className="grid-icon-button">
                                    <Tooltip
                                        content={t("Assessment.DataCollection.DataQualityAssessment.SelectCoreVariablesTooltip"
                                        )}
                                        isHtml
                                    >
                                        <InfoIcon fontSize="small" />
                                    </Tooltip>
                                </IconButton>
                            </small>
                            <VariablesSelection showConcordance={false} variableIds={serviceLevelData.variableIds} updateVariableIds={updateServiceLevelVariableData} variables={variables?.filter((variable: VariableModel) => variable.isSystemDefined === false)} concordanceVariables={[]} updateConcordanceVariables={() => { }} />
                            {errors["variableIds"] &&
                                <small className="Mui-error d-flex mt-2 mx-3">
                                    {t("Assessment.DataCollection.DataQualityAssessment.SLVariableSelectError")}
                                </small>
                            }
                            <div className=" pt-md-2 d-flex align-items-center justify-content-center">      {
                                assessmentStatus === AssessmentStatus.Published &&
                                <p className="m-0"><span className=" text-dark d-inline-flex mx-2 align-items-center"><b className="me-2">{t("Common.LastUploadedFile")}: </b>{serviceLevelData?.fileName}</span></p>
                            }
                            </div>
                        </div>
                    </fieldset>
                </div>
            </div>
            <div className={classNames("button-action-section", "button-bottom", "d-flex", "justify-content-center", "p-3")}>
                {serviceLevelData?.isFinalized === false && userPermission.canCreateOrUpdateServiceLevel &&
                    <Button className={classNames("btn", "app-btn-secondary")} onClick={onSave}>
                        {t("Common.Save")}
                    </Button>
                }

                {serviceLevelData?.isFinalized === false && serviceLevelData?.serviceLevelId && userPermission.canFinalizeServiceLevel &&
                    <Button
                        className={classNames("btn", "app-btn-secondary")}
                        onClick={(evt: React.MouseEvent<HTMLButtonElement>) => {
                            evt.preventDefault();
                            setOpenFinalizeConfirmation(true);
                        }}
                    >
                        {t("Common.Finalize")}
                    </Button>
                }

                {serviceLevelData?.isFinalized && userPermission.canFinalizeServiceLevel &&
                    <>
                        {!serviceLevelData?.hasSummaryData &&
                            <>
                                <Button
                                    className={classNames("btn", "app-btn-secondary")}
                                    onClick={(evt: React.MouseEvent<HTMLButtonElement>) => {
                                        onReFinalize(evt);
                                    }}
                                >
                                    {t("Common.Finalize")}

                                </Button>

                                {serviceLevelData?.isReFinalized &&
                                    <Button
                                        className={classNames("btn", "app-btn-secondary")}
                                        onClick={onCancelReFinalize}
                                    >
                                        {t("Common.Cancel")}

                                    </Button>
                                }
                            </>
                        }
                    </>
                }

                {canUploadFile &&
                    <>
                        <Button className={classNames("btn", "app-btn-secondary")} onClick={onGenerateTemplate}>
                            {t("Assessment.DataCollection.DataQualityAssessment.GenerateTemplate")}
                        </Button>

                        <FileUploader
                            key={`fileuploader_${Math.random()}`}
                            id="template"
                            linkCss={classNames("btn", "app-btn-secondary", "ms-2")}
                            onChange={uploadFile}
                            accept=".xlsx, .xls">
                            <div style={{ display: "grid" }}>
                                {
                                    serviceLevelData?.fileName &&
                                    <>
                                        <p className="m-0"> <i className="d-inline-flex mx-2 align-items-center">{t("Assessment.DataCollection.DataQualityAssessment.FileUploadSize")}</i></p>

                                        <p className="m-0"><span className="d-inline-flex mx-2 align-items-center"><b className="me-2">{t("Common.LastUploadedFile")}:</b> {serviceLevelData?.fileName}</span></p>
                                    </>}
                                {
                                    !serviceLevelData?.fileName &&
                                    <>
                                        <i className="d-inline-flex mx-2 align-items-center">{t("Assessment.DataCollection.DataQualityAssessment.FileUploadSize")}</i>
                                    </>
                                }
                            </div>
                        </FileUploader>

                    </>
                }

            </div>
            <>
                {isTemplateUploaded &&
                    <span className="d-flex justify-content-center Mui-error"> {t("Assessment.DataCollection.DataQualityAssessment.TemplateUploadMessage")} </span>
                }
                {isFileSizeMaximum &&
                    <span className="d-flex justify-content-center Mui-error"> {t("Exception.InvalidFileSize")} </span>
                }
                {isValidFileExtension &&
                    <span className="d-flex justify-content-center Mui-error"> {t("Exception.InvalidFile")} </span>
                }
            </>

            {/* Confirmation dialog shown when finalized button is cliked */}
            <ConfirmationDialog
                open={openFinalizeConfirmation}
                title={t("Common.ConfirmationTitle")}
                content={serviceLevelData?.isFinalized ? t("Assessment.DataCollection.DataQualityAssessment.SLDQAFinalizeDialogContent") : t("Assessment.DataCollection.DataQualityAssessment.SLDQAFinalizeDialogContent")}
                onClick={onFinalizeConfirmationBtnClick}
            />

            <DownloadInProgressModal isFileDownloading={isFileDownloading} />
        </>
    );
};

export default ServiceLevelDeliveryComponent;