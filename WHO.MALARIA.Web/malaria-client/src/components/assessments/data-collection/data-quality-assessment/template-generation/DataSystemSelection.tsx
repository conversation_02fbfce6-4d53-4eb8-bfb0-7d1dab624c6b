﻿import { useTranslation } from "react-i18next";
import MultiSelectModel from "../../../../../models/MultiSelectModel";
import React, { useState } from "react";
import TextBox from "../../../../controls/TextBox";
import { DataSourceModel } from "../../../../../models/DQA/DataSourceModel";
import { Constants } from "../../../../../models/Constants";
import { DQADataSystem } from "../../../../../models/Enums";
import { DeskLevelRequestModel } from "../../../../../models/DQA/DeskLevelRequestModel";
import { useSelector } from "react-redux";
import RadioButtonGroup from "../../../../controls/RadioButtonGroup";
import { IconButton } from "@mui/material";
import Tooltip from "../../../../controls/Tooltip";
import InfoIcon from "@mui/icons-material/Info";

interface IDataSystemSelectionProps {
    dataSources?: Array<DataSourceModel>;
    deskLevelData: DeskLevelRequestModel;
    updateData: (deskLevelData: DeskLevelRequestModel) => void;
}

/** Renders Data System Selection for desk level delivery */
const DataSystemSelection = (props: IDataSystemSelectionProps) => {
    const { t } = useTranslation();
    const { dataSources, deskLevelData, updateData } = props;
    const errors = useSelector((state: any) => state.error);

    // Triggers on the data source radio button change event and checks if other radio button is checked from Data System 1, if checked textbox to specify other is shown
    const onDS1ValueChange = (fieldName: string, values: any) => {
        let isOtherDataSystem1Selected: boolean = false;

        if (values.length && values.includes(Constants.Common.DQADataSystem1OtherId)) {
            isOtherDataSystem1Selected = true;
        }
        else if (!values.includes(Constants.Common.DQADataSystem1OtherId)) {
            isOtherDataSystem1Selected = false;
        }

        const _deskLevelData = {
            ...deskLevelData,
            [fieldName]: values,
            isOtherDataSystem1Selected: isOtherDataSystem1Selected,
        };
        updateData(_deskLevelData);

    }

    // Triggers on the data source radio button change event and checks if other radio button is checked from Data System 2, if checked textbox to specify other is shown
    const onDS2ValueChange = (fieldName: string, values: any) => {
        let isOtherDataSystem2Selected: boolean = false;
        if (values.length && values.includes(Constants.Common.DQADataSystem2OtherId)) {
            isOtherDataSystem2Selected = true;
        }
        else if (!values.includes(Constants.Common.DQADataSystem2OtherId)) {
            isOtherDataSystem2Selected = false;
        }

        const _deskLevelData = {
            ...deskLevelData,
            [fieldName]: values,
            isOtherDataSystem2Selected: isOtherDataSystem2Selected,
        };

        updateData(_deskLevelData);
    };

    // triggers the other details textbox
    const onOtherDetailsChange = (evt: React.ChangeEvent<HTMLInputElement>) => {
        const name: string = evt.currentTarget.name;
        const value: string = evt.currentTarget.value;
        const _deskLevelData = {
            ...deskLevelData,
            [name]: value,
        };

        updateData(_deskLevelData);
    }

    // converts data sources options for data system 1 in the MultiSelectModel datatype
    // checks and renders if data system is common in both the data system (1 and 2)
    const dataSourceDS1Options: Array<MultiSelectModel> = dataSources ?
        dataSources.filter(dataSource => dataSource.dataSystem === DQADataSystem.System1).map(dataSource => (
            new MultiSelectModel(
                dataSource.id,
                dataSource.name,
            )
        )) : [];

    // converts data sources options for data system 2 in the MultiSelectModel datatype
    const dataSourceDS2Options: Array<MultiSelectModel> = dataSources ?
        dataSources.filter(dataSource => dataSource.dataSystem === DQADataSystem.System2).map(dataSource => (
            new MultiSelectModel(
                dataSource.id,
                dataSource.name,
            )
        )) : [];

    return (
        <div className="checkbox-list-sidebar">
            {
                errors[`dataSystemsOneId`] &&
                <small className="Mui-error d-flex mx-3">
                    {t("Assessment.DataCollection.DataQualityAssessment.DataSystemsError")}
                </small>
            }
            <div className="checkbox-list radiobox-list">
                <h6>
                    {t("Assessment.DataCollection.DataQualityAssessment.DataSystem1")}
                    <IconButton className="grid-icon-button">
                        <Tooltip
                            content={t("Assessment.DataCollection.DataQualityAssessment.DataSystems1Tooltip"
                            )}
                            isHtml
                        >
                            <InfoIcon fontSize="small" />
                        </Tooltip>
                    </IconButton>
                </h6>
                <RadioButtonGroup
                    id="dataSystemsOneId"
                    name="dataSystemsOneId"
                    color="primary"
                    options={dataSourceDS1Options}
                    value={deskLevelData?.dataSystemsOneId}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                        onDS1ValueChange("dataSystemsOneId", e.currentTarget.value)
                    }
                />
                {deskLevelData?.isOtherDataSystem1Selected &&
                    <div className="mx-3 mb-3">
                        <TextBox
                            id="otherDataSystem1SourceName"
                            name="otherDataSystem1SourceName"
                            label={t("Common.PleaseSpecify")}
                            placeholder={t("Assessment.DataCollection.DataQualityAssessment.SourceName")}
                            size="small"
                            InputLabelProps={{ shrink: true }}
                            value={deskLevelData?.otherDataSystem1SourceName}
                            onChange={onOtherDetailsChange}
                            error={errors[`otherDataSystem1SourceName`] && errors[`otherDataSystem1SourceName`]}
                            helperText={errors[`otherDataSystem1SourceName`] && errors[`otherDataSystem1SourceName`]}
                        />
                    </div>
                }
            </div>

            {
                errors[`dataSystemsTwoId`] &&
                <small className="Mui-error d-flex mx-3">
                    {t("Assessment.DataCollection.DataQualityAssessment.DataSystemsError")}
                </small>
            }
            <div className="checkbox-list radiobox-list">
                <h6>
                    {t("Assessment.DataCollection.DataQualityAssessment.DataSystem2")}
                    <IconButton className="grid-icon-button">
                        <Tooltip
                            content={t("Assessment.DataCollection.DataQualityAssessment.DataSystems2Tooltip"
                            )}
                            isHtml
                        >
                            <InfoIcon fontSize="small" />
                        </Tooltip>
                    </IconButton>
                </h6>

                <RadioButtonGroup
                    id="dataSystemsTwoId"
                    name="dataSystemsTwoId"
                    color="primary"
                    options={dataSourceDS2Options}
                    value={deskLevelData?.dataSystemsTwoId}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                        onDS2ValueChange("dataSystemsTwoId", e.currentTarget.value)
                    }
                />
                {deskLevelData?.isOtherDataSystem2Selected &&
                    <div className="mx-3 mb-3">
                        <TextBox
                            id="otherDataSystem2SourceName"
                            name="otherDataSystem2SourceName"
                            label={t("Common.PleaseSpecify")}
                            placeholder={t("Assessment.DataCollection.DataQualityAssessment.SourceName")}
                            size="small"
                            InputLabelProps={{ shrink: true }}
                            value={deskLevelData?.otherDataSystem2SourceName}
                            onChange={onOtherDetailsChange}
                            error={errors[`otherDataSystem2SourceName`] && errors[`otherDataSystem2SourceName`]}
                            helperText={errors[`otherDataSystem2SourceName`] && errors[`otherDataSystem2SourceName`]}
                        />
                    </div>
                }
            </div>
        </div>
    );
};

export default DataSystemSelection;