﻿import React, { useEffect, useState } from "react";
import classes from "../dqa.module.scss";
import classNames from "classnames";
import { useTranslation } from "react-i18next";
import DataGrid from "../../../../controls/DataGrid";
import {
    GridCellProps,
    GridColumnProps,
    GridHeaderCellProps
} from "@progress/kendo-react-grid";
import { getter } from "@progress/kendo-data-query";
import Checkbox from "../../../../controls/Checkbox";
import { VariableModel } from "../../../../../models/VariableModel";
import { IconButton } from "@mui/material";
import Tooltip from "../../../../controls/Tooltip";
import InfoIcon from "@mui/icons-material/Info";
import { useSelector } from "react-redux";

const DATA_ITEM_KEY = "id";
const SELECTED_FIELD = "selected";
const idGetter = getter(DATA_ITEM_KEY);

interface VariablesSelectionProps {
    showConcordance?: boolean;
    variables?: Array<VariableModel>;
    updateVariableIds: (variableIds: Array<string>, concordanceVariables: Array<string>) => void;
    variableIds: Array<string>;
    concordanceVariables: Array<string>;
    updateConcordanceVariables: (concordanceVariableIds: Array<string>) => void;
}

/** Renders the variables selection for desk level delivery - DQA*/
const VariablesSelection = (props: VariablesSelectionProps) => {
    const { t } = useTranslation();
    const { showConcordance, variables, updateVariableIds, variableIds, concordanceVariables, updateConcordanceVariables } = props;
    const errors = useSelector((state: any) => state.error);

    // render concordance checkbox
    const renderConcordanceCheckBox = (props: GridCellProps) => {
        return (
            <span>
                <Checkbox
                    color="primary"
                    className="p-0"
                    label=""
                    disableRipple
                    disableFocusRipple
                    disableTouchRipple
                    inputProps={{ "aria-label": "secondary checkbox" }}
                    value={props.dataItem["id"]}
                    disabled={!variableIds?.includes(props.dataItem["id"])}
                    checked={concordanceVariables?.includes(props.dataItem["id"])}
                    onChange={(evt: React.ChangeEvent<HTMLInputElement>) => {
                        onConcordanceCheckboxChange(evt, props.dataItem["id"]);
                    }}
                />
            </span>
        );
    };

    // create column definition
    const getColDefs = (): Array<GridColumnProps> => {
        const columns: Array<GridColumnProps> = [
            {
                field: "variables",
                title: t("Assessment.DataCollection.DataQualityAssessment.Variables"),
                sortable: false,
                filterable: false,
                cell: (props: GridCellProps) => (
                    <td>
                        <>
                            <span>
                                <Checkbox
                                    color="primary"
                                    className="p-0"
                                    label=""
                                    disableRipple
                                    disableFocusRipple
                                    disableTouchRipple
                                    value={props.dataItem["id"]}
                                    checked={variableIds?.includes(props.dataItem["id"])}
                                    inputProps={{ "aria-label": "secondary checkbox" }}
                                    onChange={(evt: React.ChangeEvent<HTMLInputElement>) => {
                                        onVariableCheckboxChange(evt, props.dataItem["id"]);
                                    }}
                                />
                            </span>
                            <span>{props.dataItem["name"]} </span>
                        </>
                    </td>
                ),
            }
        ];
        // Load column for if need to show concordance for the DQA variables
        if (showConcordance) {
            columns.push(
                {
                    field: "concordance",
                    title: t("Assessment.DataCollection.DataQualityAssessment.Concordance"),
                    width: "200",
                    sortable: false,
                    cell: (props: GridCellProps) => (
                        <td>{renderConcordanceCheckBox(props)}</td>
                    ),
                    headerCell: (props: GridHeaderCellProps) => (
                        <>
                            {t("Assessment.DataCollection.DataQualityAssessment.Concordance")}
                            <IconButton className="grid-icon-button">
                                <Tooltip
                                    content={t("Assessment.DataCollection.DataQualityAssessment.SelectConcordanceTooltip"
                                    )}
                                    isHtml
                                >
                                    <InfoIcon fontSize="small" />
                                </Tooltip>
                            </IconButton>
                        </>
                    ),
                },
            );
        }

        return columns;
    };

    // Create data to be bound
    const getVariablesData = () => {
        return variables?.map(({ id, name }, index: number) => {
            return {
                [SELECTED_FIELD]: false,
                id,
                name,
            };
        });
    };

    // triggers on the desk level variable checkbox change event 
    const onVariableCheckboxChange = (evt: React.ChangeEvent<HTMLInputElement>, variableId: string) => {
        if (evt.target.checked) {
            const _variableIds = [
                ...variableIds,
                variableId
            ];
            updateVariableIds(_variableIds, concordanceVariables);
        } else {
            const _variableIds = variableIds?.filter((Id: string) => Id !== variableId);

            if (concordanceVariables.includes(variableId)) {
                const _concordanceVariables = concordanceVariables?.filter((Id: string) => Id !== variableId);
                updateVariableIds(_variableIds, _concordanceVariables);
            } else {
                updateVariableIds(_variableIds, concordanceVariables);
            }

        }
    }

    // triggers on the desk level concordance checkbox change event 
    const onConcordanceCheckboxChange = (evt: React.ChangeEvent<HTMLInputElement>, concordanceId: any) => {
        if (evt.target.checked) {
            const _concordanceVariables: Array<string> = [
                ...concordanceVariables,
                concordanceId
            ];
            updateConcordanceVariables(_concordanceVariables);
        } else {
            const _concordanceVariables = concordanceVariables?.filter((Id: string) => Id !== concordanceId);
            updateConcordanceVariables(_concordanceVariables);
        }
    }

    return (
        <div className={classNames(classes.indicatorWrapper)}>
            <DataGrid
                className="k-widget k-grid k-grid-wrapper hide-grid-icon"
                columns={getColDefs()}
                data={getVariablesData()}
                dataItemKey={DATA_ITEM_KEY}
                selectedField={SELECTED_FIELD}
                selectable={{
                    enabled: true,
                    drag: false,
                    cell: true,
                    mode: "single",
                }}
                hasActionBtn={true}
            />
        </div>
    );
}

export default VariablesSelection;