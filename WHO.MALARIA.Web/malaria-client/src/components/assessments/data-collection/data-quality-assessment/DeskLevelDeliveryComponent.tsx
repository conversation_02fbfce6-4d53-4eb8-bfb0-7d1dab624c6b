﻿import React, { useEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import classNames from "classnames";
import WHOTabs from "../../../controls/WHOTabs";
import { TabModel } from "../../../../models/TabModel";
import classes from "../data-quality-assessment/dqa.module.scss";
import VariablesSelection from "./template-generation/VariablesSelection";
import DataSystemSelection from "./template-generation/DataSystemSelection";
import { Button, IconButton } from "@mui/material";
import { AssessmentStatus, DialogAction, DQAType } from "../../../../models/Enums";
import ConfirmationDialog from "../../../common/ConfirmationDialog";
import { VariableModel } from "../../../../models/VariableModel";
import { DataSourceModel } from "../../../../models/DQA/DataSourceModel";
import { DeskLevelFinalizeModel, DeskLevelRequestModel, DLDQARequestModel } from "../../../../models/DQA/DeskLevelRequestModel";
import { dqaService } from "../../../../services/dqaService";
import DLValidationRules from "./DLValidationRules";
import useFormValidation from "../../../common/useFormValidation";
import { IValidationRuleProvider } from "../../../../models/ValidationRuleModel";
import { UserAssessmentPermission } from "../../../../models/PermissionModel";
import { useSelector } from "react-redux";
import FileUploader from "../../../controls/FileUploader";
import { Constants } from "../../../../models/Constants";
import Tooltip from "../../../controls/Tooltip";
import InfoIcon from "@mui/icons-material/Info";
import { KeyValuePair } from "../../../../models/DeskReview/KeyValueType";
import { UtilityHelper } from "../../../../utils/UtilityHelper";
import DownloadInProgressModal from "../../../controls/DownloadInProgressModal";
import { useLocation } from "react-router-dom";

type DeskLevelDeliveryProps = {
    strategyId: string;
    variables?: Array<VariableModel>;
    optionalVariables?: Array<VariableModel>;
    dataSources?: Array<DataSourceModel>;
    assessmentId: string;
};

/** Renders the Desk Level Delivery Component for Data Quality Assessment */
const DeskLevelDeliveryComponent = (props: DeskLevelDeliveryProps) => {
    const { t } = useTranslation();
    document.title = t("app.DQATitle");
    const location: any = useLocation();
    const state = location?.state;
    const assessmentStatus = state?.status;

    const { variables, optionalVariables, dataSources, assessmentId } = props;
    const [currentTab, setCurrentTab] = useState<number>(0);
    const [openConfirmation, setOpenConfirmation] = useState<boolean>(false);
    const [deskLevelData, setDeskLevelData] = useState<DeskLevelRequestModel>(DeskLevelRequestModel.init(assessmentId));
    const [isTemplateUploaded, setIsTemplateUploaded] = useState<boolean>(false);
    const [isFileSizeMaximum, setIsFileSizeMaximum] = useState<boolean>(false);
    const [isValidFileExtension, setIsValidFileExtension] = useState<boolean>(false);
    const [openFinalizeConfirmation, setOpenFinalizeConfirmation] = useState<boolean>(false);
    const [isDataSaved, setIsDataSaved] = useState<boolean>(false);
    const [isFileDownloading, setIsFileDownloading] = useState<boolean>(false);

    const validate = useFormValidation(DLValidationRules);
    const userPermission: UserAssessmentPermission = useSelector((state: any) => state.userPermission.assessment);
    const errors = useSelector((state: any) => state.error);
    let selectedCountryName = sessionStorage.getItem(Constants.SessionStorageKey.SELECTED_COUNTRY_NAME);

    const { canSaveOrFinalizeDeskLevelDQAVariables, canGenerateOrUploadDeskLevelDQATemplate } = userPermission;

    // triggered whenever finalized button is clicked
    const onButtonClick = (action: DialogAction) => {
        switch (action) {
            case DialogAction.Add:
                onFinalize();
                break;
            case DialogAction.Close:
                setOpenConfirmation(false);
                break;
        }
    };

    // triggers whenever user clicks on 'Yes' button of confirmation dialog buton
    const onFinalize = () => {
        if (deskLevelData.isFinalized) {
            let variables: Array<KeyValuePair<string, boolean | null>> = [];

            //Prepare priority variables for save response and check if concordance is selected
            deskLevelData?.priorityVariableIds?.forEach((variableId: string) => {
                if (deskLevelData.concordanceVariables.includes(variableId)) {
                    variables.push({ key: variableId, value: true });
                } else {
                    variables.push({ key: variableId, value: false });
                }
            })

            //Prepare optional variables for save response and check if concordance is selected
            deskLevelData?.optionalVariableIds?.forEach((variableId: string) => {
                if (deskLevelData.concordanceVariables.includes(variableId)) {
                    variables.push({ key: variableId, value: true });
                } else {
                    variables.push({ key: variableId, value: false });
                }
            })

            const DLSaveRequest: DLDQARequestModel = new DLDQARequestModel(
                variables,
                deskLevelData?.dataSystemsOneId,
                deskLevelData?.dataSystemsTwoId,
                deskLevelData.otherDataSystem1SourceName,
                deskLevelData.otherDataSystem2SourceName,
                assessmentId
            );

            // Re-finalize the desk level dqa with update all data
            dqaService.updateDeskLevelDQA(DLSaveRequest)
                .then((response) => {
                    setDeskLevelData({
                        ...deskLevelData,
                        isFinalized: true,
                    });
                });
        } else {
            // finalize the desk level dqa with only update status  
            const request: DeskLevelFinalizeModel = new DeskLevelFinalizeModel(assessmentId);

            dqaService.finalizeDeskLevel(request)
                .then((response) => {
                    if (response) {
                        setDeskLevelData({
                            ...deskLevelData,
                            isFinalized: response as boolean,
                            isReFinalized: false
                        });
                    }
                });
        }
        setOpenFinalizeConfirmation(false);
    };

    // Triggers whenever tab is changed
    const onTabChange = (event: React.ChangeEvent<{}>, tabIndex: number) => {
        setCurrentTab(tabIndex);
    }

    // Trigger whenever any control's values are changed or updated
    const updateDeskLevelData = (deskLevelData: DeskLevelRequestModel) => {
        setDeskLevelData(deskLevelData);
    }

    // Trigger whenever any priority variable selection changed or updated
    const updateDeskLevelPriorityVariableData = (priorityVariables: Array<string>, concordanceVariables: Array<string>) => {
        setDeskLevelData({
            ...deskLevelData,
            priorityVariableIds: priorityVariables,
            concordanceVariables: concordanceVariables
        });
    }

    // Trigger whenever any optional variable selection changed or updated
    const updateDeskLevelOptionalVariableData = (optionalVariables: Array<string>, concordanceVariables: Array<string>) => {
        setDeskLevelData({
            ...deskLevelData,
            optionalVariableIds: optionalVariables,
            concordanceVariables: concordanceVariables
        });
    }

    // Trigger whenever concordance variable selection changed or updated
    const updateDeskLevelConcordanceVariables = (concordanceVariables: Array<string>) => {
        setDeskLevelData({
            ...deskLevelData,
            concordanceVariables: concordanceVariables,
        });
    }

    // Renders tabs for Data Quality Assessment - desk level delivery
    const deskLeveltabs: Array<TabModel> = [
        new TabModel(
            0,
            `${t("Assessment.DataCollection.DataQualityAssessment.Priority")}
            <span class="fw-light fst-italic">${t("Assessment.DataCollection.DataQualityAssessment.PriorityTabDesc")}</span>`,
            <VariablesSelection
                showConcordance={deskLevelData.dataSystemsTwoId != null ? true : false}
                variables={variables?.filter((variable: VariableModel) => variable.isSystemDefined === false)}
                variableIds={deskLevelData?.priorityVariableIds}
                concordanceVariables={deskLevelData.concordanceVariables}
                updateVariableIds={updateDeskLevelPriorityVariableData}
                updateConcordanceVariables={updateDeskLevelConcordanceVariables}
            />

        ),
        new TabModel(
            1,
            `${t("Assessment.DataCollection.DataQualityAssessment.Optional")}
            <span class="fw-light fst-italic">${t("Assessment.DataCollection.DataQualityAssessment.OptionalTabDesc")}</span>`,
            <VariablesSelection
                showConcordance={deskLevelData.dataSystemsTwoId != null ? true : false}
                variables={optionalVariables}
                variableIds={deskLevelData?.optionalVariableIds}
                concordanceVariables={deskLevelData.concordanceVariables}
                updateVariableIds={updateDeskLevelOptionalVariableData}
                updateConcordanceVariables={updateDeskLevelConcordanceVariables}
            />
        ),
    ];

    // Triggered whenever finalized button is clicked
    const onFinalizeConfirmationBtnClick = (action: DialogAction) => {
        switch (action) {
            case DialogAction.Add:
                onFinalize();
                break;
            case DialogAction.Close:
                setOpenFinalizeConfirmation(false);
                break;
        }
    };

    useEffect(() => {
        getDeskLevelDQA(assessmentId);
    }, []);

    // Triggers whenever user clicks on Save button
    const onSave = () => {
        const isFormValid = validate(deskLevelData);

        if (isFormValid) {
            let variables: Array<KeyValuePair<string, boolean | null>> = [];

            //Prepare priority variables for save response and check if concordance is selected
            deskLevelData?.priorityVariableIds?.map((variableId: string) => {
                if (deskLevelData.concordanceVariables.includes(variableId)) {
                    variables.push({ key: variableId, value: true });
                } else {
                    variables.push({ key: variableId, value: false });
                }
            })

            //Prepare optional variables for save response and check if concordance is selected
            deskLevelData?.optionalVariableIds?.map((variableId: string) => {
                if (deskLevelData.concordanceVariables.includes(variableId)) {
                    variables.push({ key: variableId, value: true });
                } else {
                    variables.push({ key: variableId, value: false });
                }
            })

            const DLSaveRequest: DLDQARequestModel = new DLDQARequestModel(
                variables,
                deskLevelData?.dataSystemsOneId,
                deskLevelData?.dataSystemsTwoId,
                deskLevelData.otherDataSystem1SourceName,
                deskLevelData.otherDataSystem2SourceName,
                assessmentId
            );

            dqaService.saveDeskLevelDQA(DLSaveRequest).then((response: boolean) => {
                if (response) {
                    setIsDataSaved(true);
                }
            });
        }
    }

    // Get desk level dqa response by assessmentId
    const getDeskLevelDQA = (assessmentId: string) => {
        dqaService.getDeskLevelDQA(assessmentId).then((response) => {

            if (response) {
                setDeskLevelData({
                    ...response,
                    assessmentId: assessmentId,
                    dataSystemsOneId: response.dataSystemsOneId,
                    dataSystemsTwoId: response.dataSystemsTwoId,
                    isOtherDataSystem1Selected: response.isOtherDataSystem1Selected,
                    otherDataSystem1SourceName: response.otherDataSystem1SourceName,

                    isOtherDataSystem2Selected: response.isOtherDataSystem2Selected,
                    otherDataSystem2SourceName: response.otherDataSystem2SourceName,
                    priorityVariableIds: response.priorityVariableIds,
                    optionalVariableIds: response.optionalVariableIds,
                    concordanceVariables: response.concordanceVariables
                });

                setIsDataSaved(true);
            }
        });
    }

    // Triggers whenever user clicks on Generate Template button
    const onGenerateTemplate = () => {

        const downloadedTemplateName =
            t("app.DeskDqaFileName") +
            "_" +
            selectedCountryName;

        setIsFileDownloading(true);
        dqaService
            .generateTemplateDeskLevelDQA(assessmentId).then((response: any) => {
                UtilityHelper.download(response, downloadedTemplateName);
                setIsFileDownloading(false);
            }).catch((error: any) => {
                setIsFileDownloading(false);
            });
    }

    // Triggers whenever user clicks on upload button
    const uploadFile = (evt: React.ChangeEvent<HTMLInputElement>) => {
        // check file size
        const filesize = evt.target.files ? evt.target.files[0].size : 0;
        if (filesize > Constants.Common.MaxFileSize) //25MB
        {
            setIsFileSizeMaximum(true);
            return;
        } else {
            setIsFileSizeMaximum(false);
        }

        // check file extension        
        const fileExtension: string | undefined = evt.target.files ? evt.target.files[0].name.split('.').pop() : "";
        if (Constants.Common.ValidExcelExtensions.indexOf(fileExtension as string) === -1) {
            setIsValidFileExtension(true);
            return;
        } else {
            setIsValidFileExtension(false);
        }

        deskLevelData.fileName = evt.target.files ? evt.target.files[0].name : '';

        // since we are passing file along with other information for which we are creating 
        // FormData object to pass other properties along with file so it can be mapped at server side
        setIsTemplateUploaded(true);
        const formData = new FormData();
        formData.append("AssessmentId", assessmentId);
        formData.append("File", evt.target.files ? evt.target.files[0] : "");

        dqaService.uploadDeskLevelTemplateResponse(formData).then((response) => {
            if (response) {
                setIsTemplateUploaded(false);
                setDeskLevelData({
                    ...deskLevelData
                });
            }
        }).catch((error: any) => {
            deskLevelData.fileName = '';
            setIsTemplateUploaded(false);
        });
    }

    // Triggers whenever user clicks on reset button of data source 2
    const onResetDataSource2 = () => {
        setDeskLevelData({
            ...deskLevelData,
            dataSystemsTwoId: null,
            otherDataSystem2SourceName: "",
            concordanceVariables: [],
            isOtherDataSystem2Selected: false
        });
    }

    return (
        <>
            <fieldset  className={assessmentStatus === AssessmentStatus.Published ? "row disableContent" : "row"}>
                <div className={classNames("wrapper", classes.dqaCategoryWrapper)}>
                    <div className={classNames("mt-2", "rounded")}>

                        <div
                            className={classNames(
                                "d-flex",
                                "align-items-stretch",
                                "mt-2",
                                classes.categoryWrapper
                            )}
                        >
                            <div className={classNames(classes.subCategoriesWrapper)}>
                                <small className="fw-lighter px-2">
                                    * {t("Assessment.DataCollection.DataQualityAssessment.SelectDataSystems")}
                                    <IconButton className="grid-icon-button">
                                        <Tooltip
                                            content={t("Assessment.DataCollection.DataQualityAssessment.SelectDataSystemsTooltip"
                                            )}
                                            isHtml
                                        >
                                            <InfoIcon fontSize="small" />
                                        </Tooltip>
                                    </IconButton>
                                </small>

                                <DataSystemSelection dataSources={dataSources} deskLevelData={deskLevelData} updateData={updateDeskLevelData} />
                                <Button className={classNames("btn", "app-btn-secondary")} onClick={onResetDataSource2}>
                                    {t("Common.Reset")}
                                </Button>
                            </div>

                            <div className={classNames("indicators-wrapper", classes.indicatorsWrapper)}>
                                <small className="fw-lighter">
                                    * {t("Assessment.DataCollection.DataQualityAssessment.SelectVariablesConcordance")}
                                </small>

                                <div className="app-tab-wrapper">
                                    <WHOTabs
                                        tabs={deskLeveltabs}
                                        value={currentTab}
                                        onChange={onTabChange}
                                        scrollable={false}
                                    >
                                        <> {deskLeveltabs[currentTab].children}
                                            {errors["priorityVariableIds"] &&
                                                <small className="Mui-error d-flex mt-2 mx-3">
                                                    {t("Assessment.DataCollection.DataQualityAssessment.SLVariableSelectError")}
                                                </small>
                                            }
                                            {(deskLevelData.dataSystemsTwoId != null && errors["concordanceVariables"]) &&
                                                <small className="Mui-error d-flex mt-2 mx-3">
                                                    {t("Assessment.DataCollection.DataQualityAssessment.VariableConcordanceError")}
                                                </small>
                                            }
                                        </>
                                    </WHOTabs>
                                </div>

                            </div>
                        </div>
                        <div className=" pt-md-2 d-flex align-items-center justify-content-center">      {
                            assessmentStatus === AssessmentStatus.Published &&
                            <p className="m-0"><span className=" text-dark d-inline-flex mx-2 align-items-center"><b className="me-2">{t("Common.LastUploadedFile")}: </b>{deskLevelData?.fileName}</span></p>
                        }
                        </div>

                        <div className={classNames("button-action-section", "button-bottom", "d-flex", "justify-content-center", "p-3")}>
                            {deskLevelData?.isFinalized === false && canSaveOrFinalizeDeskLevelDQAVariables &&
                                <Button className={classNames("btn", "app-btn-secondary")} onClick={onSave}>
                                    {t("Common.Save")}
                                </Button>
                            }

                            {isDataSaved && canSaveOrFinalizeDeskLevelDQAVariables && userPermission.canFinalizeServiceLevel &&
                                <Button
                                    className={classNames("btn", "app-btn-secondary")}
                                    onClick={(evt: React.MouseEvent<HTMLButtonElement>) => {
                                        evt.preventDefault();
                                        setOpenFinalizeConfirmation(true);
                                    }}
                                >
                                    {t("Common.Finalize")}
                                </Button>
                            }
                            {deskLevelData?.isFinalized && canGenerateOrUploadDeskLevelDQATemplate &&
                                <>
                                    <Button className={classNames("btn", "app-btn-secondary")} onClick={onGenerateTemplate}>
                                        {t("Assessment.DataCollection.DataQualityAssessment.GenerateTemplate")}
                                    </Button>

                                    <FileUploader
                                        key={`fileuploader_${Math.random()}`}
                                        id="template"
                                        linkCss={classNames("btn", "app-btn-secondary", "ms-2")}
                                        onChange={uploadFile}
                                        accept=".xlsx, .xls"
                                    >
                                        <div style={{ display: "grid" }}>
                                            {deskLevelData?.fileName &&
                                                <>
                                                    <p className="m-0"> <i className="d-inline-flex mx-2 align-items-center"> {t("Assessment.DataCollection.DataQualityAssessment.FileUploadSize")}</i></p>
                                                    <p className="m-0"><span className="d-inline-flex mx-2 align-items-center"><b className="me-2">{t("Common.LastUploadedFile")}: </b>{deskLevelData?.fileName}</span></p>
                                                </>
                                            }
                                            {!deskLevelData?.fileName &&
                                                <>
                                                    <i className="d-inline-flex mx-2 align-items-center">{t("Assessment.DataCollection.DataQualityAssessment.FileUploadSize")}</i>
                                                </>
                                            }
                                        </div>
                                    </FileUploader>
                                </>
                            }
                        </div>

                        <>
                            {isTemplateUploaded &&
                                <span className="d-flex justify-content-center Mui-error"> {t("Assessment.DataCollection.DataQualityAssessment.TemplateUploadMessage")} </span>
                            }
                            {isFileSizeMaximum &&
                                <span className="d-flex justify-content-center Mui-error"> {t("Exception.InvalidFileSize")} </span>
                            }
                            {isValidFileExtension &&
                                <span className="d-flex justify-content-center Mui-error"> {t("Exception.InvalidFile")} </span>
                            }
                        </>

                        {/* Confirmation dialog shown when finalized button is clicked */}
                        <ConfirmationDialog
                            open={openFinalizeConfirmation}
                            title={t("Common.ConfirmationTitle")}
                            content={deskLevelData?.isFinalized ? t("Assessment.DataCollection.DataQualityAssessment.DLDQAFinalizeDialogContent") : t("Assessment.DataCollection.DataQualityAssessment.DLDQAFinalizeDialogContent")}
                            onClick={onFinalizeConfirmationBtnClick}
                        />

                        <DownloadInProgressModal isFileDownloading={isFileDownloading} />
                    </div>
                </div>
            </fieldset>
        </>
    );
};

export default DeskLevelDeliveryComponent;