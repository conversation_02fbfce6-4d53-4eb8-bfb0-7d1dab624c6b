﻿.stepWrapper {
    min-width: 55% !important;
    margin: 0 auto;
    width: fit-content;
}

.stepWrapper svg {
    fill: #008dc3;
}

.categoryWrapper {
    min-height: 400px;    
}
.dqaCategoryWrapper {
    position: relative;

    .buttonActionSection {
        position: absolute;
        bottom: -75px;
        left: 25%;
    }
}

.subCategoriesWrapper {
    flex: 0.2;
    border-right: 1px solid #ddd;
}

.checkboxListSidebar {
    .checkboxList {
        h6{
            font-size: 14px;
            padding: 0px 15px;
        }
        .col-checkbox-control {
            .MuiListItemIcon-root {
                min-width: 20px;
            }
        }
    }
}
