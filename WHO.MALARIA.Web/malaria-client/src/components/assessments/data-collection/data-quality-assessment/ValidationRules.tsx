﻿import { DataType } from '../../../../models/Enums';
import ValidationRuleModel, { IValidationRuleProvider } from '../../../../models/ValidationRuleModel';

/** Service level validation rules */
const ValidationRules: IValidationRuleProvider = {
    from: new ValidationRuleModel(DataType.String, true),
    to: new ValidationRuleModel(DataType.String, true),
    validationPeriodStartDate: new ValidationRuleModel(DataType.String, true),
    year: new ValidationRuleModel(DataType.Number, false),
    registerTypes: new ValidationRuleModel(DataType.ArrayOfObject, true),
    variableIds: new ValidationRuleModel(DataType.ArrayOfObject, true)
}

export default ValidationRules;