import React, { useEffect } from "react";
import { useState } from "react";
import {
    GridColumnProps,
    GridRowProps,
    GridSortChangeEvent,
    GridFilterChangeEvent,
    GridCellProps,
} from "@progress/kendo-react-grid";
import {
    filterBy,
    CompositeFilterDescriptor,
} from "@progress/kendo-data-query";
import classNames from "classnames";
import { useTranslation } from "react-i18next";
import classes from "./user.module.scss";
import {
    UserRoleEnum
} from "../../models/Enums";
import DataGrid from "../controls/DataGrid";
import { UserListModel } from "../../models/UserModel";
import { SortDescriptor, orderBy } from "@progress/kendo-data-query";

interface IUserListProps {
    users: Array<UserListModel>;
    additionalColumns: Array<GridColumnProps>
}

/** Renders Users list screen */
export const Users = (props: IUserListProps) => {
    const { t } = useTranslation();
    document.title = t("app.UsersTitle");
    const { users, additionalColumns } = props;
    const initialSort: Array<SortDescriptor> = [{ field: "name", dir: "asc" }];
    const [hover, setHover] = useState<string>("");
    const [filter, setFilter] = useState<CompositeFilterDescriptor>({
        logic: "and",
        filters: [],
    });
    const [sort, setSort] = useState<Array<SortDescriptor>>(initialSort);
    const [filteredUsers, setFilteredUsers] = React.useState<Array<UserListModel>>([]);

    useEffect(() => {
        const usersData: Array<UserListModel> = users.map((user: UserListModel) => {
            user.role = getRole(user.userType);
            return user;
        });
        setFilteredUsers(usersData);
    }, [users]);

    const filterChange = (event: GridFilterChangeEvent) => {
        setFilteredUsers(filterBy(users, event.filter));
        setFilter(event.filter);
    };

    // triggers whenever moustover event is triggered thru <tr>
    const handleMouseOver = (id: string) => {
        setHover(id);
    };

    // triggers whenever mouse out event is triggered thru <tr>
    const handleMouseOut = (id: string) => {
        setHover("");
    };

    // customized row and it's action on it
    const rowRender = (
        trElement: React.ReactElement<HTMLTableRowElement>,
        rowProps: GridRowProps
    ) => {
        const trProps = {
            ...trElement.props,
            ["onMouseOver"]: () => handleMouseOver(rowProps.dataItem.id),
            ["onMouseOut"]: () => handleMouseOut(rowProps.dataItem.id),
            ["className"]: classes.rowHeight,
        };

        return React.cloneElement(
            trElement,
            { ...trProps },
            trElement.props.children
        );
    };

    // get role by enum
    const getRole = (userType: number) => {

        switch (userType) {
            case UserRoleEnum.Viewer:
                return t("UserManagement.Viewer");
            case UserRoleEnum.Manager:
                return t("UserManagement.Manager");
            case UserRoleEnum.SuperManager:
                return t("UserManagement.Super Manager");
            case UserRoleEnum.WHOAdmin:
                return t("UserManagement.WhoAdmin");
            default:
                return "N/A";
        }
    };

    const columns: Array<GridColumnProps> =
        [
            {
                field: "name",
                title: t("UserManagement.GridColumn.Name"),
                sortable: true,
                filterable: true,
                width: 150,
                cell: (props: GridCellProps) => (
                    <td title={props.dataItem["name"]}>
                        <span> {props.dataItem["name"]}</span>
                    </td>
                )
            },
            {
                field: "email",
                title: t("UserManagement.GridColumn.Email"),
                sortable: true,
                filterable: true,
                width: 150,
                cell: (props: GridCellProps) => (
                    <td title={props.dataItem["email"]}>
                        <span> {props.dataItem["email"]}</span>
                    </td>
                )
            },
            {
                field: "organizationName",
                title: t("UserManagement.GridColumn.OrganizationName"),
                sortable: true,
                filterable: true,
                width: 150,
                cell: (props: GridCellProps) => (
                    <td title={props.dataItem["organizationName"]}>
                        <span> {props.dataItem["organizationName"]}</span>
                    </td>
                )
            },
            {
                field: "country",
                title: t("UserManagement.GridColumn.Country"),
                sortable: true,
                filterable: true,
                width: 150,
                cell: (props: GridCellProps) => (
                    <td title={props.dataItem["country"]}>
                        <span> {props.dataItem["country"]}</span>
                    </td>
                )
            },
            {
                field: "role",
                title: t("UserManagement.GridColumn.Role"),
                sortable: true,
                filterable: true,
                width: 150,
                cell: (props: GridCellProps) => (
                    <td title={props.dataItem["role"]}>
                        <span> {props.dataItem["role"]}</span>
                    </td>
                )
            },
        ];

    const allColumns: Array<GridColumnProps> = [...columns, ...additionalColumns];

    return (
        <div className={classNames(classes.dataWrapper)}>
            <DataGrid
                className="k-grid-wrapper grid-wrapper"
                columns={allColumns}
                rowRender={rowRender}
                sortable
                sort={sort}
                initialSort={initialSort}
                data={orderBy(filteredUsers, sort)}
                onSortChange={(e: GridSortChangeEvent) => {
                    setSort(e.sort);
                }}
                filter={filter}
                onFilterChange={filterChange}
                hasActionBtn={true}
            />
        </div>
    );
};

