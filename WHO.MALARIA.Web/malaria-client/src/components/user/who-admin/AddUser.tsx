import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { TabModel } from "../../../models/TabModel";
import WHOTabs from "../../controls/WHOTabs";
import InviteUser from "../../controls/InviteUser";
import AssginUser from "../../controls/AssignUser";
import { DialogAction } from "../../../models/Enums";
import MultiSelectModel from "../../../models/MultiSelectModel";
import { commonServices } from "../../../services/commonService";

type AddUserProps = {
    onDialogClose: (action: DialogAction) => void;
};

/** Renders the Add User screen */
const AddUser = (props: AddUserProps) => {
    const { onDialogClose } = props;
    const { t } = useTranslation();
    const [countries, setCountries] = useState<Array<MultiSelectModel>>([]);
    const [currentTabIndex, setCurrentTabIndex] = useState<number>(0);

    useEffect(() => {
        commonServices
            .getCountriesWithoutSuperManager()
            .then((countries: any) => {
                setCountries(countries);
            });
    }, []);

    // Tabs to be rendered
    const tabs: Array<TabModel> = [
        {
            id: 1,
            label: t("Common.AddInviteNewUser"),
            children: null,
            canDelete: false,
            icon: undefined
        },
        {
            id: 2,
            label: t("Common.AddAssignUser"),
            children: null,
            canDelete: false,
            icon: undefined
        },
    ];

    // Triggers whenever user changes the tab
    const onTabChange = (event: React.ChangeEvent<{}>, newValue: any) => {
        setCurrentTabIndex(newValue as number);
    };

    // Render the component based on the tab selected
    const renderComponent = () => {
        switch (currentTabIndex) {
            case 0: // invite user screen
                return (
                    <InviteUser onDialogClose={onDialogClose} countries={countries} />
                );
            case 1: // assign user screen
                return (
                    <AssginUser onDialogClose={onDialogClose} countries={countries} />
                );
        }
    };

    return <div className="app-tab-wrapper">
        <WHOTabs
            tabs={tabs}
            value={currentTabIndex}
            scrollable={false}
            onChange={onTabChange}
        >
            <div className="p-3">{renderComponent()}</div>
        </WHOTabs>
    </div>
}

export default AddUser;