import { Link } from "react-router-dom";
import classNames from "classnames";
import Carousel from "../ui/Carousel";
import { useTranslation } from "react-i18next";

/** Renders User Dashbaord  */
function Dashboard() {
    const { t } = useTranslation();
    document.title = t("app.DashboardTitle");

    return (
        <>
            <section className="page-column-section dashboard-section">
                <div className="container-fluid">                    
                    <div className="d-flex align-items-stretch row">
                        <div className="col-xs-12 col-md-8">
                            {/* Carousel Component */}
                            <Carousel canRenderAssessmentStatistics={false} />

                            <div className="app-card-wrapper">
                                <div className="row">
                                    <div className="col-sm-12 col-md-4">
                                        <div className="card mb-3"></div>
                                    </div>

                                    <div className="col-sm-12 col-md-4">
                                        <div className="card mb-3"></div>
                                    </div>

                                    <div className="col-sm-12 col-md-4">
                                        <div className="card mb-3"></div>
                                    </div>
                                </div>
                            </div>

                            <div className="app-card-wrapper">
                                <div className="row">
                                    <div className="col-sm-12 col-md-12">
                                        <div className="card mb-3"></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div className="col-xs-12 col-md-4">
                            <div className="app-card-wrapper">
                                <div className="row height100">
                                    <div className="col-sm-12 col-md-12">
                                        <div className="card mb-3 height100"></div>
                                    </div>
                                </div>
                            </div>

                            <div className="button-action-section mt-3">
                                <Link
                                    className={classNames(
                                        "btn app-btn-primary w-100 text-center"
                                    )}
                                    to="/assessments"
                                >
                                    {t("Assessment.ViewAssessments")}
                                </Link>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </>
    );
}

export default Dashboard;
