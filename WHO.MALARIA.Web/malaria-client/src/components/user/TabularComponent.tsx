﻿import { Table } from "@mui/material";
import React from "react";
import { useTranslation } from "react-i18next";
import { MetNotMetStatus } from "../../models/Enums";

type DashboardTabularProps = {
    data: any;
};

/** Tabular component which renders indicator (table components) to be rendered*/
const TabularComponent = (props: DashboardTabularProps) => {
    const { t } = useTranslation();
    const { data } = props;

    // get table column
    const rowFields = data?.indicators?.rows[0];

    // Get CSS class for indicator status to highlight percentage value
    const getCssClassForIndicatorStatus = (value: number) => {
        switch (value) {
            case MetNotMetStatus.Met:
                return "green";
            case MetNotMetStatus.PartiallyMet:
                return "yellow";
            case MetNotMetStatus.NotMet:
                return "red";
            case MetNotMetStatus.NotAssessed:
                return "grey";
            default: return "";
        }
    }

    return (
        <>
            <div className="table-responsive">
                <Table className="app-table table mb-0 indicators-dashboard">
                    <>
                        <thead>
                            <tr>
                                <th></th>
                                <th></th>
                                <th></th>
                                <>
                                    {
                                        data && data?.objectives.map((objective: any) => {
                                            return (
                                                <th key={`objective_${Math.random()}`} title={objective.key} colSpan={objective.value}>
                                                    {objective.key}
                                                </th>
                                            )
                                        })
                                    }
                                </>
                            </tr>
                            <tr>
                                <th></th>
                                <th></th>
                                <th></th>
                                <>
                                    {
                                        data && data?.subObjectives.map((subobjective: any) => {
                                            return (
                                                <th key={`subobjective_${Math.random()}`} title={subobjective.key} colSpan={subobjective.value}>
                                                    {subobjective.key}
                                                </th>
                                            )
                                        })
                                    }
                                </>
                            </tr>
                            <tr>
                                <>
                                    {
                                        data && data?.indicators.columns.map((indicator: any) => {
                                            return (
                                                <th key={`indicator_${Math.random()}`} title={indicator.label}>
                                                    {indicator.label}
                                                </th>
                                            )
                                        })
                                    }
                                </>
                            </tr>
                        </thead>

                        <tbody>
                            {data && data?.indicators?.rows.map((indicator: any, index: number) => {
                                return (
                                    <tr key={`indicator_${Math.random()}`}>
                                        {Object.keys(rowFields)?.map((modelKeyName: string, index: number) => {
                                            return (
                                                <React.Fragment key={`modelKeyName_${Math.random()}`}>
                                                    {index > 2 ?
                                                        <td key={modelKeyName} width={100} className="indicators">
                                                            <div className={`${"d-flex status-box"} ${getCssClassForIndicatorStatus(indicator[modelKeyName]["DeskReview"])}`} title={t("Dashboard.DeskReviewScore")}>

                                                                {
                                                                    indicator[modelKeyName]["IsSurvey"] === true &&
                                                                    <div className={`${"survey-value"} ${getCssClassForIndicatorStatus(indicator[modelKeyName]["SurveyValue"])}`} title={t("Dashboard.SurveyScore")}>
                                                                        {indicator[modelKeyName]["SurveyValue"]}
                                                                    </div>
                                                                }
                                                            </div>
                                                        </td> :
                                                        <td key={modelKeyName}>{indicator[modelKeyName]}</td>
                                                    }
                                                </React.Fragment>
                                            )
                                        }
                                        )}

                                    </tr>
                                )
                            }
                            )}
                        </tbody>
                    </>
                </Table>
            </div>

            <div className="d-flex justify-content-center py-2">
                <b>NOTE:&nbsp;</b> {t("Dashboard.NoteForScoreOnIndicatorDashboard")}
            </div>

            <div className="d-flex justify-content-center py-2">
                <ul className="dashboard-legend">
                    <li><span className="met"></span>{t("Common.Met")}​​​​​</li>
                    <li><span className="partially-met"></span>{t("Common.PartiallyMet")}​​​​​</li>
                    <li><span className="not-met"></span>{t("Common.NotMet")}​​​​​</li>
                    <li><span className="not-assessed"></span>{t("Common.NotAssessed")}​​​​​</li>
                </ul>
            </div>
        </>
    )
}

export default TabularComponent;