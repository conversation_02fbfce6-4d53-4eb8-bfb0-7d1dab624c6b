import React, { useEffect, useState } from "react";
import classNames from "classnames";
import LeftIcon from "@mui/icons-material/ChevronLeft";
import RightIcon from "@mui/icons-material/ChevronRight";

import TextBox from "../controls/TextBox";
import { userService } from "../../services/userService";
import classes from "./user.module.scss";
import EmojiEmotionsIcon from "@mui/icons-material/EmojiEmotions";

import RequestDetail from "./RequestDetail";
import { Button } from "@mui/material";
import { useTranslation } from "react-i18next";
import ModalFooter from "../controls/ModalFooter";
import {
  CountryAccessRequestModel,
  InvitationRequestModel,
  PendingUserDetailModel,
} from "../../models/RequestModels/UserRequestModel";
import UserMessage from "../common/UserMessage";
import MultiSelectModel from "../../models/MultiSelectModel";
import Dropdown from "../controls/Dropdown";
import { DialogAction } from "../../models/Enums";
import { UtilityHelper } from "../../utils/UtilityHelper";

type RequestProps = {
  onCancel: () => void;
  isCountryAccess: boolean;
  pendingRequests: Array<PendingUserDetailModel>;
  getPendingRequests: () => void;
  onDialogClose: (action: DialogAction) => void;
};

/** Renders the Pending requests */
const PendingRequests = (props: RequestProps) => {
  const { isCountryAccess, onCancel, pendingRequests, getPendingRequests, onDialogClose } = props;

  const { t } = useTranslation();
  const [error, setError] = useState<any>({});

  const [currentIndex, setCurrentIndex] = useState<number>(1);
  const [pendingRequest, setPendingRequest] =
    useState<Array<PendingUserDetailModel>>(pendingRequests);

  const [countryAccessRequest, setCountryAccessRequest] =
    useState<CountryAccessRequestModel>(
      CountryAccessRequestModel.init()
    );

  const [rejectionReasons, setRejectionReasons] =
    useState<Array<MultiSelectModel>>
      ([new MultiSelectModel(t("UserManagement.RejectionReasons.UserIdentifier"), t("UserManagement.RejectionReasons.UserIdentifier")),
      new MultiSelectModel(t("UserManagement.RejectionReasons.OrganizationalAccessDenied"), t("UserManagement.RejectionReasons.OrganizationalAccessDenied")),
      new MultiSelectModel(t("UserManagement.RejectionReasons.OrganizationalCredentialNotVerified"), t("UserManagement.RejectionReasons.OrganizationalCredentialNotVerified")),
      new MultiSelectModel(t("UserManagement.RejectionReasons.Other"), t("UserManagement.RejectionReasons.Other"))
      ]);

  const [rejectionValue, setRejectionValue] = useState<string>("");

  const [isOtherReason, setIsOtherReason] = useState<boolean>(false);


  // triggers when user changes the input text value
  const onChange = (evt: React.ChangeEvent<HTMLInputElement>) => {
    setCountryAccessRequest({
      ...countryAccessRequest,
      [evt.target.name]: evt.target.value,
    });

    if (evt.target.name === "rejectReason") {
      setRejectionValue(evt.target.value.toString());
      if (evt.target.value.toString() === "Other") {
        let err = error;
        delete err['comment'];
        setError(err);
        setIsOtherReason(true);
        setCountryAccessRequest({
          ...countryAccessRequest,
          ["comment"]: '',
        });
      }
      else {
        setIsOtherReason(false);
        setCountryAccessRequest({
          ...countryAccessRequest,
          ["comment"]: evt.target.value,
        });
      }
    }

    if (evt.target.value.length > 0) {
      let err = error;
      delete err[evt.target.name];
      setError(err);
    }
  };

  // move to next card
  const moveNext = () => {
    setCurrentIndex((prevState: number) =>
      prevState < pendingRequest.length
        ? prevState + 1
        : pendingRequest.length
    );
  }

  // triggers whenever user clicks on reject
  const onUserReject = ((pendingUserDetailModel: PendingUserDetailModel) => {
    if (countryAccessRequest.comment === "") {
      if (rejectionValue === "") {
        setError({ ["comment"]: t("UserManagement.ProvideReason"), ["rejectReason"]: t("UserManagement.SelectReason") });
        return;
      }
      setError({ ["comment"]: t("UserManagement.ProvideReason") });
      return;
    }
    if (!isCountryAccess) {
      userService.onReject(pendingUserDetailModel.id, pendingUserDetailModel.countryId, countryAccessRequest.comment || rejectionValue).then((response: boolean) => {
        if (response) {
          getPendingRequests();
          moveNext();
        }
      });
    }
    else {
      const userCountryAccessModel = new CountryAccessRequestModel(pendingUserDetailModel.userCountryAccessId, pendingUserDetailModel.countryId, countryAccessRequest.comment)
      userService.rejectUserCountryAccessRequest(userCountryAccessModel).then((response: boolean) => {
        if (response) {
          getPendingRequests();
          moveNext();
        }
      });
    }

    //Tracking reject user activation request event in analytics
    UtilityHelper.onEventAnalytics("User activation request", "Reject user activation request", "Reject user activation request");

  });

  // triggers whenever user clicks on forward to WHO
  const forwardToWHOAdmin = (pendingUserDetailModel: PendingUserDetailModel) => {
    if (countryAccessRequest.comment === "") {
      if (rejectionValue === "") {
        setError({ ["comment"]: t("UserManagement.ProvideReason"), ["rejectReason"]: t("UserManagement.SelectReason") });
        return;
      }
      setError({ ["comment"]: t("UserManagement.ProvideReason") });
      return;
    }
    if (!isCountryAccess) {
      const userCountryAccessModel = new CountryAccessRequestModel(pendingUserDetailModel.userCountryAccessId, pendingUserDetailModel.countryId, countryAccessRequest.comment)
      userService.forwardToWHOAdminRequest(userCountryAccessModel).then((response: boolean) => {
        if (response) {
          getPendingRequests();
          moveNext();
        }
      });
    }
  }

  // triggers whenever user clicks on invite/Grant button
  // if isCountryAccess then we approve the user request
  // else send invite will call
  const onUserInvite = (pendingUserDetailModel: PendingUserDetailModel) => {
    if (!isCountryAccess) {
      userService.sendInvitation(new InvitationRequestModel(pendingUserDetailModel.id, pendingUserDetailModel.userCountryAccessId, pendingUserDetailModel.email)).then((response: boolean) => {
        if (response) {
          getPendingRequests();
          onDialogClose(DialogAction.Add);
          moveNext();
        }
      });
    } else {
      const userCountryAccessModel = new CountryAccessRequestModel(pendingUserDetailModel.userCountryAccessId, pendingUserDetailModel.countryId, countryAccessRequest.comment)
      userService.approveUserCountryAccessRequest(userCountryAccessModel).then((response: boolean) => {
        if (response) {
          getPendingRequests();
          moveNext();
        }
      });
    }

    //Tracking grant country access to user event in analytics
    UtilityHelper.onEventAnalytics("User activation request", "Grant user activation request", "Grant user activation request");

  };

  if (pendingRequests.length === 0) {
    return (
      <UserMessage
        className="d-flex justify-content-center"
        renderContent={
          <h5>
            <EmojiEmotionsIcon />
            {t("Common.NoPendingRequest")}
          </h5>
        }
      />
    );
  }

  return (
    <>
      <div className={classNames("d-flex", "align-items-center", "mb-3")}>
        <div className={classNames("d-inline-flex", "ml-auto")}>
          <div className={classNames("d-flex", "align-middle")}>
            <Button
              disabled={currentIndex === 1}
              onClick={() =>
                setCurrentIndex((prevState: number) => prevState - 1)
              }
            >
              <LeftIcon />
            </Button>
          </div>

          <h6
            className={classNames("align-items-center", "m-2")}
          >{`${currentIndex}/${pendingRequest.length}`}</h6>

          <div className={classNames("d-inline-flex", "ml-auto")}>
            <Button
              disabled={currentIndex === pendingRequest.length}
              onClick={() =>
                setCurrentIndex((prevState: number) => prevState + 1)
              }
            >
              <RightIcon />
            </Button>
          </div>
        </div>
      </div>
      <div
        className={classNames(
          "d-flex",
          "justify-content-center",
          classes.pendingRequestWrapper
        )}
      >
        <div className={classNames("blue-background", classes.contentWrapper)}>
          <RequestDetail {...pendingRequest[currentIndex - 1]} />
        </div>
      </div>
      <div className="row mt-3">
        <div className="col-md-12">
          <Dropdown
            id="rejectReason"
            name="rejectReason"
            error={error["rejectReason"] ? true : false}
            helperText={error["rejectReason"]}
            options={rejectionReasons}
            value={rejectionValue}
            label={t("UserManagement.Reason")}
            onChange={onChange}
            InputLabelProps={{ shrink: true }}
          />
        </div>
      </div>
      {isOtherReason ?
        <div className="mt-3">
          <TextBox
            id="comment"
            name="comment"
            label={t("UserManagement.ProvideCommentsAndReasons")}
            rows={5}
            multiline
            maxLength={2000}
            fullWidth
            error={error["comment"] ? true : false}
            helperText={error["comment"] ? error["comment"] : ""}
            onChange={onChange}
          />
        </div>
        : <div></div>
      }
      <ModalFooter>
        <>
          <Button
            className={classNames("btn", "app-btn-secondary")}
            onClick={() =>
              onUserReject(
                pendingRequest &&
                pendingRequest[currentIndex - 1] &&
                pendingRequest[currentIndex - 1]
              )
            }
          >
            {t("Common.Reject")}
          </Button>
          {isCountryAccess ?
            <Button
              className={classNames("btn", "app-btn-primary")}
              onClick={() => {
                onUserInvite(
                  pendingRequest &&
                  pendingRequest[currentIndex - 1] &&
                  pendingRequest[currentIndex - 1]
                )
              }
              }
            >
              {t("Common.Grant")}
            </Button> :
            <Button
              className={classNames("btn", "app-btn-primary")}
              onClick={() => {
                forwardToWHOAdmin(
                  pendingRequest &&
                  pendingRequest[currentIndex - 1] &&
                  pendingRequest[currentIndex - 1]
                )
              }
              }
            >
              {t("Common.SendToWHO")}
            </Button>}
        </>
      </ModalFooter>
    </>
  );
};

export default PendingRequests;
