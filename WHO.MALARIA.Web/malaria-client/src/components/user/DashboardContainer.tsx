﻿import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate, useLocation } from "react-router-dom";
import { DashboardStepper } from "../../models/Enums";
import { TabModel } from "../../models/TabModel";
import WHOTabs from "../controls/WHOTabs";
import { Carousel as ReactCarousel } from "react-responsive-carousel";
import { dataQualityAssessmentService } from "../../services/dataQualityAssessmentService";
import TabsWithCharts from "../common/TabsWithCharts";
import TabularComponent from "../user/TabularComponent";
import CountryDashboard from "../landing/CountryDashboard";
import ObjectiveWorldMap from "../landing/ObjectiveWorldMap";
import { dashboardService } from "../../services/dashboardService";
import { DashboardReportModel } from "../../models/GlobalDashboardModel";
import { TabWithChartsModel } from "../../models/ChartModel";
import { UserRoleEnum } from "../../models/Enums";
import { CurrentUserModel } from "../../models/ProfileModel";
import { authService } from "../../services/authService";
import { Constants } from "../../models/Constants";
import { UtilityHelper } from "../../utils/UtilityHelper";

/** Dashboard Container which renders Global/Country dashboard components */
const DashboardContainer = () => {
  const { t } = useTranslation();
  document.title = t("app.DashboardTitle");
  const navigate = useNavigate();
  const location: any = useLocation();
  const state = location?.state;
  const subTabIndex = location?.state?.subTabIndex;
  const currentUser = authService.getCurrentUser();

  const [dashboardRegionalSummary, setDashboardRegionalSummary] =
    useState<any>();
  const [dashboardIndicatorSummary, setDashboardIndicatorSummary] =
    useState<any>();
  const [dashboardReport, setDashboardReport] =
    useState<DashboardReportModel>();

  const [dashboardObjectiveSummary, setDashboardObjectiveSummary] =
    useState<any>();

  const currentlanguage = UtilityHelper.getCookieValue("i18next");

  useEffect(() => {
    getIndicatorDashboardSummary();
    getRegionalDashboardSummary();
    getObjectiveDashboardSummary();
  }, [currentlanguage]);

  // Get regional dashboard summary response
  const getRegionalDashboardSummary = () => {
    dashboardService
      .getRegionalSummary()
      .then((dashboardReport: DashboardReportModel) => {
        const dashboardData = dashboardReport.response;
        setDashboardRegionalSummary(dashboardData);
        setDashboardReport(dashboardReport);
      });
  };

  // Get indicator dashboard summary response
  const getIndicatorDashboardSummary = () => {
    dashboardService
      .getIndicatorSummary()
      .then((indicatorDashboardReport: DashboardReportModel) => {
        const dashboardData = indicatorDashboardReport.response;
        setDashboardIndicatorSummary(dashboardData);
        setDashboardReport(indicatorDashboardReport);
      });
  };

  // Get objective dashboard summary response
  const getObjectiveDashboardSummary = () => {
    dashboardService
      .getObjectiveSummary()
      .then((dashboardReport: DashboardReportModel) => {
        const dashboardData = dashboardReport.response;
        setDashboardObjectiveSummary(dashboardData);
        setDashboardReport(dashboardReport);
      });
  };

  const [currentTab, setCurrentTab] = useState<number>(0);

  // Triggers whenever tab is changed
  const onTabChange = (event: React.ChangeEvent<{}>, tabIndex: number) => {
    setCurrentTab(tabIndex);
  };

  // Helps in redirection to other route of report generation based on the tab index
  const onNavigate = (currentTabIndex: number) => {
    let url: string = "";
    switch (currentTabIndex) {
      case DashboardStepper.Global:
        url = "/global-dashboard";
        break;

      case DashboardStepper.Country:
        url = "/country-dashboard";
        break;
    }

    navigate(url, {
      state: {
        ...state,
        subTabIndex: currentTabIndex,
      },
    });
  };

  //click next button of coursel programaticaly
  const showNextdiv = () => {
    const nextbtn = document.getElementsByClassName(
      "control-next"
    )[0] as HTMLElement;
    if (nextbtn) nextbtn.click();
  };

  //click previous button of coursel programaticaly
  const showPrediv = () => {
    const prebtn = document.getElementsByClassName(
      "control-prev"
    )[0] as HTMLElement;
    if (prebtn) prebtn.click();
  };

  // Renders tabs for Dashboard
  const dashboardTabs: Array<TabModel> =
    currentUser.userType !== UserRoleEnum.WHOAdmin
      ? [
          new TabModel(
            DashboardStepper.Global,
            t("Common.Global"),
            (
              <>
                <div className="carouselWrapper">
                  <div
                    id="app-dashboard-carousel"
                    className="carousel slide mb-4"
                    data-ride="carousel"
                  >
                    <div className="carousel-inner">
                      <ReactCarousel
                        showArrows={true}
                        showThumbs={false}
                        showIndicators={true}
                        showStatus={false}
                      >
                        <div className="row justify-content-center">
                          <div className="p-4">
                            <div>
                              {t("Dashboard.ObjectiveDashboardSlider")}
                              <span
                                className="nav-arrow-btn nav-arrow-btn-next"
                                onClick={showNextdiv}
                              >
                                {t("Dashboard.RegionalDashboardSlider")}
                              </span>
                            </div>
                          </div>
                          <div>
                            {dashboardObjectiveSummary !== undefined ? (
                              <ObjectiveWorldMap
                                data={dashboardObjectiveSummary}
                              />
                            ) : (
                              <></>
                            )}
                          </div>
                        </div>

                        <div className="row justify-content-center">
                          <div className="p-4">
                            <div>
                              <span
                                className="nav-arrow-btn nav-arrow-btn-prev"
                                onClick={showPrediv}
                              >
                                {t("Dashboard.ObjectiveDashboardSlider")}
                              </span>
                              {t("Dashboard.RegionalDashboardSlider")}
                              <span
                                className="nav-arrow-btn nav-arrow-btn-next"
                                onClick={showNextdiv}
                              >
                                {t("Dashboard.IndicatorDashboardSlider")}
                              </span>
                            </div>
                          </div>

                          <div>
                            {dashboardRegionalSummary != null ? (
                              <TabsWithCharts data={dashboardRegionalSummary} />
                            ) : (
                              <div className="app-dashboard d-flex align-items-center justify-content-center">
                                {t("Dashboard.NoPublishedData")}
                              </div>
                            )}
                          </div>
                        </div>

                        <div className="row justify-content-center">
                          <div className="p-4">
                            <div>
                              <span
                                className="nav-arrow-btn nav-arrow-btn-prev"
                                onClick={showPrediv}
                              >
                                {t("Dashboard.RegionalDashboardSlider")}
                              </span>
                              {t("Dashboard.IndicatorDashboardSlider")}
                            </div>
                          </div>
                          <div>
                            {dashboardIndicatorSummary != null ? (
                              <TabularComponent
                                data={dashboardIndicatorSummary}
                              />
                            ) : (
                              <div className="app-dashboard d-flex align-items-center justify-content-center">
                                {t("Dashboard.NoPublishedData")}
                              </div>
                            )}
                          </div>
                        </div>
                      </ReactCarousel>
                    </div>
                  </div>
                </div>
              </>
            )
          ),
          new TabModel(
            DashboardStepper.Country,
            t("Common.Country"),
            <CountryDashboard />
          ),
        ]
      : [
          new TabModel(
            DashboardStepper.Global,
            t("Common.Global"),
            (
              <>
                <div className="carouselWrapper">
                  <div
                    id="app-dashboard-carousel"
                    className="carousel slide mb-4"
                    data-ride="carousel"
                  >
                    <div className="carousel-inner">
                      <ReactCarousel
                        showArrows={true}
                        showThumbs={false}
                        showIndicators={false}
                        showStatus={false}
                      >
                        <div className="row justify-content-center">
                          <div className="p-4">
                            {t("Dashboard.ObjectiveDashboardSlider")}
                          </div>
                          <div>
                            {dashboardObjectiveSummary !== undefined ? (
                              <ObjectiveWorldMap
                                data={dashboardObjectiveSummary}
                              />
                            ) : (
                              <></>
                            )}
                          </div>
                        </div>

                        <div className="row justify-content-center">
                          <div className="p-4">
                            {t("Dashboard.RegionalDashboardSlider")}
                          </div>

                          <div>
                            {dashboardRegionalSummary != null ? (
                              <TabsWithCharts data={dashboardRegionalSummary} />
                            ) : (
                              <div className="app-dashboard d-flex align-items-center justify-content-center">
                                {t("Dashboard.NoPublishedData")}
                              </div>
                            )}
                          </div>
                        </div>

                        <div className="row justify-content-center">
                          <div className="p-4">
                            {t("Dashboard.IndicatorDashboardSlider")}
                          </div>

                          <div>
                            {dashboardIndicatorSummary != null ? (
                              <TabularComponent
                                data={dashboardIndicatorSummary}
                              />
                            ) : (
                              <div className="app-dashboard d-flex align-items-center justify-content-center">
                                {t("Dashboard.NoPublishedData")}
                              </div>
                            )}
                          </div>
                        </div>
                      </ReactCarousel>
                    </div>
                  </div>
                </div>
              </>
            )
          ),
        ];

  useEffect(() => {
    if (subTabIndex > 0) {
      onNavigate(subTabIndex);
      setCurrentTab(subTabIndex);
    }
  }, []);

  return (
    <section className="page-full-section">
      <>
        <div className="app-tab-wrapper app-centered-tab-wrapper py-3">
          <WHOTabs
            tabs={dashboardTabs}
            value={currentTab}
            onChange={onTabChange}
            scrollable={true}
          >
            <> {dashboardTabs[currentTab].children} </>
          </WHOTabs>
        </div>
      </>
    </section>
  );
};

export default DashboardContainer;
