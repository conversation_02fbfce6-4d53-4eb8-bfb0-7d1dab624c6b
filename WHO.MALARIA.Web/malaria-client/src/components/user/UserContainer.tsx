import { useTranslation } from "react-i18next";
import { UserRoleEnum } from "../../models/Enums";
import { Users as SuperManagerUsers } from "./super-manager/Users";
import { Users as WHOAdminUsers } from "./who-admin/Users";

/** Renders the user Container */
function UserContainer({ userType = -1 }) {
  const { t } = useTranslation();

  if (userType === UserRoleEnum.WHOAdmin) return <WHOAdminUsers />;

  /*if (userType === UserRoleEnum.SuperManager) return <SuperManagerUsers />;*/
  if (userType === UserRoleEnum.SuperManager) return <SuperManagerUsers />;

  if (userType === UserRoleEnum.Viewer) return <h3>Viewers</h3>;

  return <div></div>;
}

export default UserContainer;
