﻿import React, { useState } from "react";
import classNames from "classnames";
import { useTranslation } from "react-i18next";
import Dropdown from "../controls/Dropdown";
import MultiSelectModel from "../../models/MultiSelectModel";
import FileUploader from "../controls/FileUploader";
import { Constants } from "../../models/Constants";
import { Button } from "@mui/material";
import ConfirmationDialog from "../common/ConfirmationDialog";
import { DialogAction } from "../../models/Enums";
import { commonServices } from "../../services/commonService";

/**Renders the upload document component */
const UploadDocument = () => {
  const { t } = useTranslation();
  document.title = t("app.UploadDocumentTitle");
  const [languageSelection, setLanguageSelection] = useState<string>("");
  const [isFileSizeMaximum, setIsFileSizeMaximum] = useState<boolean>(false);
  const [openUploadDocumentsConfirmation, setOpenUploadDocumentsConfirmation] =
    useState<boolean>(false);
  const [toolkitDocument, setToolkitDocument] = useState<string | Blob>("");
  const [uploadedFileName, setUploadedFileName] = useState<string>("");
  const [error, setError] = useState<any>({});

  // Triggers whenever user tries to change dropdown value
  const onChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    setLanguageSelection(e.currentTarget.value);

    removeControlValidationOnChange(e.target.value, e.target.name);
  };

  // Triggers whenever user clicks on upload button
  const uploadFile = (evt: React.ChangeEvent<HTMLInputElement>) => {
    // check file size
    const filesize = evt.target.files ? evt.target.files[0].size : 0;
    if (filesize > Constants.Common.MaxToolkitZipFileSize) {
      //50MB
      setIsFileSizeMaximum(true);
    } else {
      setIsFileSizeMaximum(false);
    }

    if (evt.target.files) {
      setToolkitDocument(evt.target.files[0]);
      setUploadedFileName(evt.target.files[0].name);
      removeControlValidationOnChange(evt.target.value, "toolkitDocument");
    } else {
      setToolkitDocument("");
      setUploadedFileName("");
    }
  };

  // Triggered whenever confirmation button is clicked to upload toolkit documents
  const onUploadDocumentsConfirmationBtnClick = (action: DialogAction) => {
    switch (action) {
      case DialogAction.Add:
        onSave();
        break;
      case DialogAction.Close:
        setOpenUploadDocumentsConfirmation(false);
        break;
    }
  };

  // Triggers whenever user clicks on Save button
  const onSaveBtnClick = () => {
    if (!isFormValid()) {
      setError(validate());
      return;
    }
    setOpenUploadDocumentsConfirmation(true);
  };

  const isFormValid = () => Object.keys(validate()).length === 0;

  // Triggers whenever user clicks on Yes button
  const onSave = () => {
    if (!isFormValid()) {
      setError(validate());
      setOpenUploadDocumentsConfirmation(false);
      return;
    }

    // FormData object to pass along with file so it can be mapped at server side
    const formData = new FormData();
    formData.append("Language", languageSelection);
    formData.append("File", toolkitDocument);

    commonServices.uploadToolkitDocuments(formData);

    setOpenUploadDocumentsConfirmation(false);
  };

  // validate form
  const validate = () => {
    let error: any = {};

    if (languageSelection == "") {
      error = { ...error, ["languageSelection"]: t("Errors.MandatoryField") };
    } else {
      delete error["languageSelection"];
    }

    if (toolkitDocument == "") {
      error = { ...error, ["toolkitDocument"]: t("Errors.MandatoryField") };
    } else {
      delete error["toolkitDocument"];
    }

    return error;
  };

  // checks and remove validation for the control if satisfies the validate condition
  const removeControlValidationOnChange = (
    value: string | Array<MultiSelectModel>,
    field: string
  ) => {
    if (value?.length > 0) {
      const formError = { ...error };
      delete formError[field];
      setError({ ...formError });
    } else {
      setError({ ...error, [field]: "Field is mandatory" });
    }
  };

  return (
    <section className="page-full-section page-response-section">
      <div className="container-fluid">
        <div className="d-flex document-title-section">
          <h2 className="heading-title">{t("Common.UploadDocument")}</h2>
        </div>
        <div className="h-300 d-flex align-items-center justify-content-center flex-column">
          <div className="width-250">
            <div className="row">
              <div className="col-sm-12">
                <p>{t("UploadDocument.UploadDocumentMessage")}</p>

                <Dropdown
                  id="languageSelection"
                  name="languageSelection"
                  label={t("Common.SelectLanguage")}
                  value={languageSelection}
                  size="small"
                  options={[
                    new MultiSelectModel("en", t("Common.English")),
                    new MultiSelectModel("fr", t("Common.French")),
                  ]}
                  onChange={onChange}
                  error={error["languageSelection"] ? true : false}
                  helperText={error["languageSelection"]}
                />
              </div>

              <div className="col-sm-12 mt-3">
                <FileUploader
                  key={`fileuploader_${Math.random()}`}
                  id="template"
                  linkCss={classNames("btn", "app-btn-secondary", "ms-2")}
                  onChange={uploadFile}
                  accept=".zip"
                >
                  <i className="d-inline-flex mx-2 align-items-center">
                    {t("UploadDocument.FileUploadSize")}
                  </i>
                </FileUploader>
                {error["toolkitDocument"] && (
                  <span className="d-flex Mui-error small px-2">
                    {t("Errors.MandatoryField")}
                  </span>
                )}
                <p className="p-2">{uploadedFileName}</p>
              </div>
            </div>
          </div>
        </div>

        <div className="response-action-wrapper d-flex align-items-center justify-content-center">
          <div className="button-action-section d-flex justify-content-center p-3">
            <Button
              className={classNames("btn", "app-btn-secondary")}
              onClick={(evt: React.MouseEvent<HTMLButtonElement>) => {
                evt.preventDefault();
                onSaveBtnClick();
              }}
            >
              {t("Common.Save")}
            </Button>
          </div>
        </div>
        <>
          {isFileSizeMaximum && (
            <span className="d-flex justify-content-center Mui-error">
              {" "}
              {t("Exception.InvalidZipFileSize")}{" "}
            </span>
          )}
        </>
      </div>

      {/* Confirmation dialog shown when save button is clicked */}
      <ConfirmationDialog
        open={openUploadDocumentsConfirmation}
        title={t("Common.ConfirmationTitle")}
        content={t("UploadDocument.UploadDocumentConfirmationMessage")}
        onClick={onUploadDocumentsConfirmationBtnClick}
      />
    </section>
  );
};

export default UploadDocument;
