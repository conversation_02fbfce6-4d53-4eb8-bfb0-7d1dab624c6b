import { useState } from "react";
import MultiSelectModel from "../../models/MultiSelectModel";
import { useEffect } from "react";
import { commonServices } from "../../services/commonService";
import { DialogAction } from "../../models/Enums";
import WHOTabs from "../controls/WHOTabs";
import { TabModel } from "../../models/TabModel";
import InviteUser from "../controls/InviteUser";
import AssginUser from "../controls/AssignUser";
import { useTranslation } from "react-i18next";
import { IconButton } from "@mui/material";
import InfoIcon from "@mui/icons-material/Info";
import Tooltip from "../controls/Tooltip";

type AddSuperManagerProps = {
  onDialogClose: (action: DialogAction) => void;
};

/** Renders the Add Super Manager screen */
function AddSuperManager(props: AddSuperManagerProps) {
  const { onDialogClose } = props;
  const { t } = useTranslation();
  const [countries, setCountries] = useState<Array<MultiSelectModel>>([]);
  const [currentTabIndex, setCurrentTabIndex] = useState<number>(0);

  useEffect(() => {
    commonServices
      .getCountriesWithoutSuperManager()
      .then((countries: any) => {
        setCountries(countries);
      });
  }, []);

  // Tabs to be rendered
  const tabs: Array<TabModel> = [
    {
      id: 1,
      label: t("Common.InviteAssign"),
      children: null,
      canDelete: false,
      icon: (<IconButton className="grid-icon-button">
        <Tooltip
          content={t("Common.InviteAssignTooltip")}
          isHtml
        >
          <InfoIcon fontSize="default" />
        </Tooltip>
      </IconButton>)
    },
    {
      id: 2,
      label: t("Common.AssingExist"),
      children: null,
      canDelete: false,
      icon: (<IconButton className="grid-icon-button">
        <Tooltip
          content={t("Common.AssingExistTooltip")}
          isHtml
        >
          <InfoIcon fontSize="default" />
        </Tooltip>
      </IconButton>)
    },
  ];

  // Triggers whenever user changes the tab
  const onTabChange = (event: React.ChangeEvent<{}>, newValue: any) => {
    setCurrentTabIndex(newValue as number);
  };

  // Render the component based on the tab selected
  const renderComponent = () => {
    switch (currentTabIndex) {
      case 0: // invite user screen
        return (
          <InviteUser onDialogClose={onDialogClose} countries={countries} />
        );
      case 1: // assign user screen
        return (
          <AssginUser onDialogClose={onDialogClose} countries={countries} />
        );
    }
  };
  return (
    <div className="app-tab-wrapper tab-wrapper-center">
      <WHOTabs
        tabs={tabs}
        value={currentTabIndex}
        scrollable={false}
        onChange={onTabChange}
      >
        <div className="p-3">{renderComponent()}</div>
      </WHOTabs>
    </div>
  );
}

export default AddSuperManager;
