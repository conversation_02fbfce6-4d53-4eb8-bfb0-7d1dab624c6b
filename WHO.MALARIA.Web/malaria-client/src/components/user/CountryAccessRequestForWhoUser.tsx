import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import Profile from "./Profile";
import { UserModel } from "../../models/ProfileModel";
import { useNavigate,  useLocation } from "react-router-dom";

/** Renders for country access request for who user*/
function CountryAccessRequestForWhoUser() {
    const { t } = useTranslation();
    const navigate = useNavigate();
    const [isWhoUser, setIsWhoUser] = useState<boolean>(false);
    const [user, setUser] = useState<UserModel>(UserModel.init());
    document.title = t("app.CountryAccessRequestTitle");

    //Set flag after successful country access request sent from who-user
    const setIsAccessRequestForCountryFromWhoUser = (param: boolean) => {
        setIsWhoUser(param);
    }

    //Set user and country access details
    const updateUserDetails = (userModel: UserModel) => {
        setUser(userModel);
        //If user have any country access
        if (userModel.accessGrantedCountryIds.length)
            navigate("/dashboard");
    }


    return (
        <section className="page-center-section">
            <div className="w-50">
                {(!isWhoUser && user.accessGrantedCountryIds.length == 0)
                    || user.pendingRequestCountryIds.length == 0
                    ?
                    <div className="d-flex row">
                        <Profile registerUser={false}
                            isAccessRequestForCountryFromWhoUser={true}  //isAccessRequestForCountryFromWhoUser is true when access request for country from who user
                            setIsAccessRequestForCountryFromWhoUser={setIsAccessRequestForCountryFromWhoUser}
                            updateUserDetails={updateUserDetails} />
                    </div>
                    : user.accessGrantedCountryIds.length == 0
                        && user.pendingRequestCountryIds.length >= 1
                        ? <div className="d-flex row">
                            {t("UserManagement.Message.WhoUserAddedAsViewer")}
                        </div>
                        : <></>
                }
            </div>
        </section >
    );
}

export default CountryAccessRequestForWhoUser;
