import React from "react";
import { useTranslation } from "react-i18next";

/** Renders the Pending Request Detail */
const RequestDetail = ({ name = "", email = "", organizationName = "" }) => {
  const { t } = useTranslation();
  return (
    <>
      <div className="mb-3">
        <label className="fw-bold">{t("UserManagement.GridColumn.Name")}</label>
        <div>{name}</div>
      </div>
      <div className="mb-3">
        <label className="fw-bold">
          {t("UserManagement.GridColumn.Email")}
        </label>
        <div>{email}</div>
      </div>
      <div className="mb-3">
        <label className="fw-bold">
          {t("UserManagement.GridColumn.OrganizationName")}
        </label>
        <div>{organizationName}</div>
      </div>
    </>
  );
};

export default RequestDetail;
