import { Button, CardActions, CardMedia, Typography } from "@mui/material";
import { Card, CardActionArea, CardContent } from "@mui/material";
import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { authService } from "../../services/authService";
import UserMessage from "../common/UserMessage";
import Applause from "../../images/applause.png";
import classes from "./invitation.module.scss";
import { useNavigate, useParams } from "react-router-dom";
import { userService } from "../../services/userService";
import Loading from "../common/Loading";

interface QueryParam {
  ucaId: string;
}

/** Renders the Invitatino acceptance screen */
function InvitationAccepted() {
  const { t } = useTranslation();
  document.title = t("app.AcceptInvitationTitle");

  const navigate = useNavigate();
  const params: QueryParam = useParams<QueryParam>();
  const [isInvitaionAccepted, setIsInvitaionAccepted] =
    useState<boolean>(false);

  useEffect(() => {
    userService.acceptInvite(params.ucaId).then((response) => {
      if (response) {
        setIsInvitaionAccepted(true);
      }
    });
  }, []);

  // Triggers when user clicks on Login link to navigate user to AAD
  const onNavigate = () => {
    authService.challenge("aad");
  };

  if (!isInvitaionAccepted) return <Loading />;

  return (
    <Card className={classes.root}>
      <CardActionArea>
        <img className={classes.media} src={Applause} title="Applause" />
        <CardContent>
          <Typography gutterBottom variant="h5" component="h2">
            Welcome
          </Typography>
          <Typography variant="body2" color="textSecondary" component="p">
            Thank you for accepting invitation from Malaria Surveillance
            Toolkit. Please feel free to choose your option below
          </Typography>
        </CardContent>
      </CardActionArea>
      <CardActions>
        <Button
          size="small"
          className="btn app-btn-primary"
          color="primary"
          onClick={onNavigate}
        >
          Login
        </Button>
        <Button
          size="small"
          color="primary"
          className="btn app-btn-secondary"
          onClick={() => navigate("/", { replace: true })}
        >
          Dashboard
        </Button>
      </CardActions>
    </Card>
  );
}

export default InvitationAccepted;
