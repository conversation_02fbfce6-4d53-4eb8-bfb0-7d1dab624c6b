import React from "react";
import classNames from "classnames";
import { useTranslation } from "react-i18next";

import RadioButtonGroup from "../controls/RadioButtonGroup";
import { DialogAction, UserRoleEnum } from "../../models/Enums";
import MultiSelectModel from "../../models/MultiSelectModel";
import { useState } from "react";
import { EditUserModel } from "../../models/UserModel";
import { useEffect } from "react";
import ModalFooter from "../controls/ModalFooter";
import { Button } from "@mui/material";
import { userService } from "../../services/userService";
import PersonIcon from "@mui/icons-material/AccountCircle";
import { UpdateUserRoleAndStatusRequestModel } from "../../models/RequestModels/UserRequestModel";
import { UtilityHelper } from "../../utils/UtilityHelper";

type EditUserProps = {
  userId: string;
  name: string;
  userType: number;
  status: boolean;
  countryId: string;

  onDialogClose: (action: DialogAction) => void;
};
/** Renders the Edit user screen */
const EditUser = (props: EditUserProps) => {
  const { t } = useTranslation();
  const { name, userId, userType, status, countryId, onDialogClose } = props;

  const [user, setUser] = useState<EditUserModel>(EditUserModel.init());

  useEffect(() => {
    setUser({
      ...user,
      userId,
      userType,
      status,
    });
  }, [userId, userType, status]);

  // triggers whenever user changes the input type radio button
  const onChange = (evt: React.ChangeEvent<HTMLInputElement>) => {
    setUser({
      ...user,
      [evt.target.name]:
        evt.target.name !== "status"
          ? parseInt(evt.target.value)
          : evt.target.value === "true",
    });
  };

  // Triggers when user click on 'Save' Button
  const onUpdate = () => {
    // todo: Validations
    userService
      .updateUser(
        new UpdateUserRoleAndStatusRequestModel(
          user.userId,
          user.userType,
          user.status ? 1 : 0,
          countryId
        )
      )
      .then((response) => {
        if (response) {
          onDialogClose(DialogAction.Add);
        }

        //Tracking update user event in analytics
        UtilityHelper.onEventAnalytics("User", "Update user", "Update user");

      });
  };
  return (
    <>
      <div className="grey-background">
        <h5 className="modal-content-head">
          <PersonIcon className="person-icon" /> <b>{name}</b>
        </h5>

        <div className="ms-4 mt-4">
          <div className="mb-3">
            <span className="fs-small">
              {t("UserManagement.GridColumn.Role")}
            </span>
            <RadioButtonGroup
              id="userType"
              name="userType"
              row
              color="primary"
              options={[
                new MultiSelectModel(
                  UserRoleEnum.Manager,
                  t("Common.Manager"),
                  userType === UserRoleEnum.Manager
                ),
                new MultiSelectModel(
                  UserRoleEnum.Viewer,
                  t("Common.Viewer"),
                  userType === UserRoleEnum.Manager
                ),
              ]}
              value={user.userType}
              onChange={onChange}
              className="inputFocus"
            />
          </div>
          <div className="mb-3">
            <span className="fs-small">
              {t("UserManagement.GridColumn.Status")}
            </span>
            <RadioButtonGroup
              id="status"
              name="status"
              row
              color="primary"
              options={[
                new MultiSelectModel(true, t("Common.Active")),
                new MultiSelectModel(false, t("Common.Inactive")),
              ]}
              value={user.status}
              onChange={onChange}
              className="inputFocus"
            />
          </div>
        </div>
      </div>

      <ModalFooter>
        <>
          <Button
            className={classNames("btn", "app-btn-secondary")}
            onClick={() => onDialogClose(DialogAction.Close)}
          >
            {t("Common.Cancel")}
          </Button>
          <Button
            className={classNames("btn", "app-btn-primary")}
            onClick={onUpdate}
          >
            {t("Common.Update")}
          </Button>
        </>
      </ModalFooter>
    </>
  );
};

export default EditUser;
