import React, { useState } from "react";
import Paper from "@mui/material/Paper";

import PermIdentityIcon from "@mui/icons-material/PermIdentity";
import { But<PERSON> } from "@mui/material";
import classNames from "classnames";

import ModalFooter from "../controls/ModalFooter";

import classes from "./Login.module.scss";
import GoogleIcon from "../ui/icons/GoogleIcon";
import FacebookIcon from "../ui/icons/FacebookIcon";
import { authService } from "../../services/authService";
import { Scheme, UserKind } from "../../models/Enums";
import { Constants } from "../../models/Constants";

type LoginProps = { onLoginOptionClicked: () => void };

/** Renders Login modal */
function Login(props: LoginProps) {
  const { onLoginOptionClicked } = props;
  const [active, setActive] = useState("");
  const [showLoginOption, setShowLoginOption] = useState(false);

  // triggers whenever user clicks on Let's start button
  const onClick = (evt: React.MouseEvent<HTMLButtonElement>) => {
    if (!active) return;

    if (active === Constants.Common.WHOUser) {
      redirect(Scheme.Azure);
    } else {
      setShowLoginOption(true);
    }
    onLoginOptionClicked();
  };

  // redirect user to authenticatin page based on scheme selected
  const redirect = (scheme: string) => {
    authService.challenge(scheme);
  };

  // Renders the login option element
  const LoginOption = (): React.ReactElement => {
    return (
      <div>
        <h4 className="d-flex justify-content-center">
          How Would you like to sign in ?
        </h4>
        <div className="d-flex flex-column bd-highlight mb-3 align-items-center pt-2 m-4">
          <Button
            color="primary"
            variant="outlined"
            className={classNames("p-2", "bd-highlight", classes.social)}
            onClick={() => redirect(Scheme.Google)}
          >
            <GoogleIcon width="30px" height="30px" />
            <span className="ps-2">SIGN IN WITH GOOGLE </span>
          </Button>
          <div className="line-highlight bd-highlight">
            <span className={classes.line}>OR</span>
          </div>
          <Button
            color="primary"
            variant="outlined"
            className={classNames("p-2", "bd-highlight", classes.social)}
            onClick={() => redirect('aad')}
          >
            <FacebookIcon />
            <span className="ps-2">SIGN IN WITH FACEBOOK</span>
          </Button>
        </div>
      </div>
    );
  };

  return !showLoginOption ? (
    <>
      <h4>Hello,</h4>
      <div>
        Welcome to the surveillance assessment Tooolkit.The Toolkit will guide
        you through conducting a surveillance system assessment in your country.
      </div>
      <div className="mt-3">Login to get started</div>
      <div className="text-center">Who are you ?</div>
      <section className={`${classes.loginOption}`}>
        <Paper
          id="WHO"
          className={`${classes.box} ${
            active === UserKind.WHO && classes.active
          }`}
          elevation={3}
          onClick={() => setActive(UserKind.WHO)}
          variant="outlined"
        >
          <PermIdentityIcon className={classes.userIcon} />
          <h5>I am a</h5>
          <h6>WHO User</h6>
        </Paper>
        <Paper
          id="non-WHO"
          className={`${classes.box} ${
            active === UserKind.NonWHO && classes.active
          }`}
          elevation={3}
          variant="outlined"
          onClick={() => setActive(UserKind.NonWHO)}
        >
          <PermIdentityIcon className={classes.userIcon} />
          <h5>I am a</h5>
          <h6>Non-WHO User</h6>
        </Paper>
      </section>
      <ModalFooter>
        <Button
          className={classNames("btn-modal-primary", {
            [classes.active]: active.length > 0,
            [classes.btnSecondary]: active.length > 0,
          })}
          onClick={onClick}
        >
          LETS GET STARTED
        </Button>
      </ModalFooter>
    </>
  ) : (
    <LoginOption />
  );
}

export default Login;
