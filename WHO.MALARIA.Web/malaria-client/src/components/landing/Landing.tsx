import AssessmentGraphics from "../../images/AssessmentGraphics.svg";
import DeskReviewGraphics from "../../images/DeskReviewGraphics.svg";
import QuestionBankGraphics from "../../images/QuestionBankGraphics.svg";
import DataQualityGraphic from "../../images/DataQualityGraphic.svg";
import AnalysisTools from "../../images/DataQualityGraphic.svg";
import ReportAndPresentationTemplates from "../../images/DataQualityGraphic.svg";
import ConceptNoteAndProtocol from "../../images/AssessmentGraphics.svg";
import Carousel from "../ui/Carousel";
import ToolsCard from "../ui/ToolsCard";
import ToolsCardModal from "../ui/ToolsCardModal";
import { commonServices } from "../../services/commonService";
import { useTranslation } from "react-i18next";
import React, { useEffect, useState } from "react";
import Modal from "../controls/Modal";
import { CurrentUserModel } from "../../models/ProfileModel";
import { authService } from "../../services/authService";
import Profile from "../user/Profile";
import { UtilityHelper } from "../../utils/UtilityHelper";
import { Constants } from "../../models/Constants";
import { Table } from "@mui/material";
import KeyObjective from "./KeyObjective";
import DownloadInProgressModal from "../controls/DownloadInProgressModal";
import parse from "html-react-parser";

/** Renders Landing page */
function Landing() {
  const [openNewUserDialog, setOpenNewUserDialog] = useState<boolean>(false);
  const [downloadDialog, setDownloadDialog] = useState<boolean>(false);
  const [openObjectivesDialog, setOpenObjectivesDialog] =
    useState<boolean>(false);
  const [showReadMore, setShowReadMore] = useState<boolean>(false);

  // on read details click upadte the flag and set/update text
  const onReadDetails = (showReadMore: boolean) => {
    setShowReadMore(!showReadMore);
  };

  useEffect(() => {
    setOpenNewUserDialog(currentUser && currentUser.isNewUser);
  }, []);

  const currentUser: CurrentUserModel = authService.getCurrentUser();
  const { t } = useTranslation();
  document.title = t("translation:app.LandingPageTitle");
  // redirect user to authentication page based on scheme selected
  const redirect = (scheme: string) => {
    authService.challenge(scheme);
  };

  // triggered whenever user clicks on download all button
  const onDownload = () => {
    setDownloadDialog(true);
    commonServices.downloadAllTools().then((content: any) => {
      const language: string = UtilityHelper.getCookieValue("i18next") || "en";
      const fileName: string = Constants.Common.DownloadFileName.replace(
        "{language}",
        language.toUpperCase()
      );
      UtilityHelper.download(content, fileName);
      setDownloadDialog(false);
    });
  };

  // triggered whenever user clicks on overview link for the toolkit
  const onOverviewDownload = () => {
    setDownloadDialog(true);
    commonServices.downloadToolkitOverview().then((content: any) => {
      const language: string = UtilityHelper.getCookieValue("i18next") || "en";
      const fileName: string = Constants.Common.OverviewFileName.replace(
        "{language}",
        language.toUpperCase()
      );
      UtilityHelper.download(content, fileName);
      setDownloadDialog(false);
    });
  };

  const landing_Table1_1_Headers = [
    {
      field: "",
      label: "1",
    },
    {
      field: "performance",
      label: t("translation:Landing:LandingTable1_1:Performance"),
    },
    {
      field: "measureThePerformance",
      label: t("translation:Landing:LandingTable1_1:MeasureThePerformance"),
    },
  ];

  const landing_Table1_2_Headers = [
    {
      field: "",
      label: "2",
    },
    {
      field: "contextInfrastructure",
      label: t("translation:Landing:LandingTable1_2:ContextInfrastructure"),
    },
    {
      field: "evaluateInfrastructure",
      label: t("translation:Landing:LandingTable1_2:EvaluateInfrastructure"),
    },
  ];

  const landing_Table1_3_Headers = [
    {
      field: "",
      label: "3",
    },
    {
      field: "technicalProcesses",
      label: t("translation:Landing:LandingTable1_3:TechnicalProcesses"),
    },
    {
      field: "describeTechnicalProcesses",
      label: t(
        "translation:Landing:LandingTable1_3:DescribeTechnicalProcesses"
      ),
    },
  ];

  const landing_Table1_4_Headers = [
    {
      field: "",
      label: "4",
    },
    {
      field: "behavior",
      label: t("translation:Landing:LandingTable1_4:Behavior"),
    },
    {
      field: "describeBehavior",
      label: t("translation:Landing:LandingTable1_4:DescribeBehavior"),
    },
  ];

  const landing_Table1_1_Columns = [
    {
      field: "surveillanceSystemCoverage",
      labelOne: "1.1",
      labelTwo: t(
        "translation:Landing:LandingTable1_1:SurveillanceSystemCoverage"
      ),
      labelThree: t(
        "translation:Landing:LandingTable1_1:SurveillanceSystemCoverageLabel"
      ),
    },
    {
      field: "dataQuality",
      labelOne: "1.2",
      labelTwo: t("translation:Landing:LandingTable1_1:DataQuality"),
      labelThree: t("translation:Landing:LandingTable1_1:DataQualityLabel"),
    },
    {
      field: "dataUse",
      labelOne: "1.3",
      labelTwo: t("translation:Landing:LandingTable1_1:DataUse"),
      labelThree: t("translation:Landing:LandingTable1_1:DataUseLabel"),
    },
  ];

  const landing_Table1_2_Columns = [
    {
      field: "surveillanceSystemCoverage",
      labelOne: "2.1",
      labelTwo: t("translation:Landing:LandingTable1_2:SurveillanceSectors"),
      labelThree: t(
        "translation:Landing:LandingTable1_2:DescribeSurveillanceSectors"
      ),
    },
    {
      field: "dataQuality",
      labelOne: "2.2",
      labelTwo: t("translation:Landing:LandingTable1_2:InformationSystems"),
      labelThree: t(
        "translation:Landing:LandingTable1_2:DescribeInformationSystems"
      ),
    },
    {
      field: "dataUse",
      labelOne: "2.3",
      labelTwo: t("translation:Landing:LandingTable1_2:Guidelines"),
      labelThree: t("translation:Landing:LandingTable1_2:DescribeGuidelines"),
    },
    {
      field: "dataUse",
      labelOne: "2.4",
      labelTwo: t("translation:Landing:LandingTable1_2:Resources"),
      labelThree: t("translation:Landing:LandingTable1_2:DescribeResources"),
    },
    {
      field: "dataUse",
      labelOne: "2.5",
      labelTwo: t("translation:Landing:LandingTable1_2:FinancialSupport"),
      labelThree: t(
        "translation:Landing:LandingTable1_2:DescribeFinancialSupport"
      ),
    },
  ];

  const landing_Table1_3_Columns = [
    {
      field: "caseManagement",
      labelOne: "3.1",
      labelTwo: t("translation:Landing:LandingTable1_3:CaseManagement"),
      labelThree: t(
        "translation:Landing:LandingTable1_3:DescribeCaseManagement"
      ),
    },
    {
      field: "recording",
      labelOne: "3.2",
      labelTwo: t("translation:Landing:LandingTable1_3:Recording"),
      labelThree: t("translation:Landing:LandingTable1_3:DescribeRecording"),
    },
    {
      field: "reporting",
      labelOne: "3.3",
      labelTwo: t("translation:Landing:LandingTable1_3:Reporting"),
      labelThree: t("translation:Landing:LandingTable1_3:DescribeReporting"),
    },
    {
      field: "analysis",
      labelOne: "3.4",
      labelTwo: t("translation:Landing:LandingTable1_3:Analysis"),
      labelThree: t("translation:Landing:LandingTable1_3:DescribeAnalysis"),
    },
    {
      field: "qualityAssurance",
      labelOne: "3.5",
      labelTwo: t("translation:Landing:LandingTable1_3:QualityAssurance"),
      labelThree: t(
        "translation:Landing:LandingTable1_3:DescribeQualityAssurance"
      ),
    },
    {
      field: "dataAccess",
      labelOne: "3.6",
      labelTwo: t("translation:Landing:LandingTable1_3:DataAccess"),
      labelThree: t("translation:Landing:LandingTable1_3:DescribeDataAccess"),
    },
  ];

  const landing_Table1_4_Columns = [
    {
      field: "governance",
      labelOne: "4.1",
      labelTwo: t("translation:Landing:LandingTable1_4:Governance"),
      labelThree: t("translation:Landing:LandingTable1_4:DetermineGovernance"),
    },
    {
      field: "promotion",
      labelOne: "4.2",
      labelTwo: t("translation:Landing:LandingTable1_4:Promotion"),
      labelThree: t("translation:Landing:LandingTable1_4:DeterminePromotion"),
    },
    {
      field: "supervision",
      labelOne: "4.3",
      labelTwo: t("translation:Landing:LandingTable1_4:Supervision"),
      labelThree: t("translation:Landing:LandingTable1_4:DetermineSupervision"),
    },
    {
      field: "surveillanceStaff",
      labelOne: "4.4",
      labelTwo: t("translation:Landing:LandingTable1_4:SurveillanceStaff"),
      labelThree: t(
        "translation:Landing:LandingTable1_4:DetermineSurveillanceStaff"
      ),
    },
  ];

  return (
    <>
      <section className="page-full-section pb-0">
        <div className="card-blue-box">
          <div className="row summary">
            <div className="col-xs-12 col-sm-6">
              <div className="d-flex document-title-section">
                <h2 className="heading-title">
                  {t("translation:Landing.SurveillanceAssessments")}
                </h2>
              </div>

              <p
                className="collapse"
                id="collapseSummary"
                aria-expanded="false"
              >
                {t("translation:Landing:SurveillanceAssessmentDescription")}
                &nbsp;
                <a
                  href="https://www.who.int/publications/i/item/9789240031357"
                  target="_blank"
                >
                  {t("translation:Landing.SurveillanceAssessmentsLink")}
                </a>{" "}
                &nbsp;
                {t("translation:Landing:SurveillanceAssessmentDesc")}
                &nbsp;
                <a
                  href="https://www.who.int/publications/i/item/9789241565578"
                  target="_blank"
                >
                  {t("translation:Landing.SurveillanceAssessmentsLinkOne")}
                </a>{" "}
                &nbsp;
                {t("translation:Landing:SurveillanceAssessmentDescOne")}
              </p>
            </div>

            <div className="col-xs-12 col-sm-6 border-start">
              <div className="d-flex document-title-section">
                <h2 className="heading-title">
                  {t("translation:Landing.SurveillanceAssessmentToolkit")}
                </h2>
              </div>
              <p
                className="collapse"
                id="collapseSummary"
                aria-expanded="false"
              >
                {t("translation:Landing.SurveillanceAssessmentToolkitDesc")}
                &nbsp;
                <a
                  className="link-text"
                  onClick={() => setOpenObjectivesDialog(true)}
                >
                  {t("translation:Landing.FourKeyObjectives")}
                </a>{" "}
                &nbsp;
                {t("translation:Landing:SurveillanceAssessmentToolkitDesc1")}
                &nbsp;
                {t("translation:Landing:SurveillanceAssessmentToolkitDesc2")}
                &nbsp;
                <a
                  href="https://apps.who.int/iris/bitstream/handle/10665/361178/9789240055278-eng.pdf"
                  target="_blank"
                >
                  {t("translation:Landing.ImplementationReferenceGuide")}
                </a>
                &nbsp;
                {t("translation:Landing.An")}&nbsp;
                <span
                  className="link-text cursor-pointer"
                  onClick={onOverviewDownload}
                >
                  {t("translation:Landing.Overview")}
                </span>
                &nbsp;
                {t(
                  "translation:Landing.SurveillanceAssessmentToolkitOverviewDesc"
                )}
                .
              </p>
            </div>
            <div className="d-flex justify-content-center mt-3">
              <a
                className="summary-btn collapsed"
                data-bs-toggle="collapse"
                href="#collapseSummary"
                role="button"
                aria-expanded="false"
                aria-controls="collapseSummary"
                onClick={() => onReadDetails(showReadMore)}
              >
                {!showReadMore
                  ? t("translation:Landing.ReadMore")
                  : t("translation:Landing.ReadLess")}
              </a>
            </div>
          </div>
        </div>

        <div className="d-flex document-title-section">
          <h2 className="heading-title">
            {t("translation:Landing.AssessmentsSummary")}
          </h2>
        </div>

        {/* Carousel Component */}
        <Carousel canRenderAssessmentStatistics={true} />

        <div className="d-flex document-title-section">
          <h2 className="heading-title">{t("Landing.Tools")}</h2>

          <div className="button-action-section ml-auto">
            <div className="button-group">
              <button
                className="btn app-btn-primary ripple"
                onClick={onDownload}
              >
                {t("translation:Common.DownloadAll")}
              </button>
            </div>
          </div>
        </div>

        {/* Landing Card Section */}
        <div className="app-card-wrapper">
          <div className="row">
            <ToolsCardModal
              src={AssessmentGraphics}
              title={t("translation:Landing.Assessment")}
              shortDescription={t("translation:Landing.AssessmentFramework")}
              linkDetails={t("translation:Landing.AssessmentApproach")}
            />
            <ToolsCard
              src={ConceptNoteAndProtocol}
              title={t("translation:Landing.ConceptNoteProtocol")}
              shortDescription={t("translation:Landing.AssessmentProtocol")}
            />
            <ToolsCard
              src={DeskReviewGraphics}
              title={t("translation:Landing.DeskReview")}
              shortDescription={t("translation:Landing.AssessmentDeskReview")}
            />
            <ToolsCard
              src={DataQualityGraphic}
              alt=""
              title={t("translation:Landing.DataQualityAnalysis")}
              shortDescription={t(
                "translation:Landing.AssessmentDataQualityGraphic"
              )}
            />
            <ToolsCard
              src={QuestionBankGraphics}
              title={t("translation:Landing.QuestionBank")}
              shortDescription={t("translation:Landing.AssessmentQuestionBank")}
            />
            <ToolsCard
              src={AnalysisTools}
              title={t("translation:Landing.AnalysisTool")}
              shortDescription={t("translation:Landing.AssessmentAnalysisTool")}
            />
            <ToolsCard
              src={ReportAndPresentationTemplates}
              title={t("translation:Landing.ReportTemplates")}
              shortDescription={t(
                "translation:Landing.AssessmentReportTemplates"
              )}
            />
          </div>
        </div>
        <div className="login-action-section mt-auto py-3 d-flex justify-content-center">
          <div className="button-action-section w-100 text-center">
            <div className="button-group">
              {/*TODO: commented condition will needs to be added later */}
              {/*{(currentUser && currentUser.isNewUser) &&*/}
              <button
                className="btn app-btn-primary ripple"
                onClick={() => redirect("aad")}
              >
                {t("translation:Menus.Login")}
              </button>

              <span className="register-link">
                {t("translation:Landing.NonWHOUsers")},&nbsp;
                <a
                  className="link-text"
                  onClick={() => setOpenNewUserDialog(true)}
                >
                  <b className="text-uppercase">
                    {t("translation:UserManagement.Register")}
                  </b>
                </a>
              </span>
            </div>
          </div>
        </div>
      </section>

      {/* Modal to register user */}
      {
        <Modal
          open={openNewUserDialog}
          title={
            !currentUser.isNewUser
              ? t("translation:UserManagement.Register")
              : t("translation:UserManagement.Profile")
          }
          onEscPress={false}
          onDialogClose={() => setOpenNewUserDialog(false)}
        >
          <Profile registerUser onCancel={() => setOpenNewUserDialog(false)} />
        </Modal>
      }

      {/* Modal to show four key objectives */}
      {
        <Modal
          open={openObjectivesDialog}
          title={t("translation:Landing:LandingTable1Heading")}
          onEscPress={false}
          modalClassName="app-modal-lg"
          onDialogClose={() => setOpenObjectivesDialog(false)}
        >
          <div className="landing-table-center">
            <div className="custom-table">
              <Table className="grey-table mb-0">
                <thead className="grey-row">
                  <tr>
                    <th>
                      {t("translation:Landing:LandingTable1_1:Objective")}
                    </th>
                    <th>{t("translation:Landing:LandingTable1_1:Name")}</th>
                    <th>
                      {t("translation:Landing:LandingTable1_1:Description")}
                    </th>
                  </tr>
                </thead>
                <thead className="grey-row">
                  <tr>
                    <th colSpan={3}>
                      {t("translation:Landing:LandingTable1_1:SubObjective")}
                    </th>
                  </tr>
                </thead>
              </Table>
            </div>
            <KeyObjective
              tableHeader={landing_Table1_1_Headers}
              tableBody={landing_Table1_1_Columns}
              tableClassName="table-blue"
            />

            <KeyObjective
              tableHeader={landing_Table1_2_Headers}
              tableBody={landing_Table1_2_Columns}
              tableClassName="table-green"
            />

            <KeyObjective
              tableHeader={landing_Table1_3_Headers}
              tableBody={landing_Table1_3_Columns}
              tableClassName="table-purple"
            />

            <KeyObjective
              tableHeader={landing_Table1_4_Headers}
              tableBody={landing_Table1_4_Columns}
              tableClassName="table-red"
            />
          </div>
        </Modal>
      }

      <DownloadInProgressModal isFileDownloading={downloadDialog} />
    </>
  );
}

export default Landing;
