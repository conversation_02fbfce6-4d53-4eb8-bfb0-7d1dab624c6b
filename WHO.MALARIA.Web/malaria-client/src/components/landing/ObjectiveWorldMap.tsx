import { useEffect, useState } from "react";
import { TabModel } from "../../models/TabModel";
import { ObjectiveDashboardModel } from "../../models/GlobalDashboardModel";
import WHOTabs from "../controls/WHOTabs";
import { TabWithChartsModel } from "../../models/ChartModel";
import WorldMapDashboard from "./WorldMapDashboard";

type DashboardObjectiveProps = {
    data: any;
};

////Summary
/* Dashboard Component to generate world map with strategy wise objective with country data. */
const ObjectiveWorldMap = (props: DashboardObjectiveProps) => {

    const { data } = props;

    const [currentTab, setCurrentTab] = useState<number>(0);
    const [strategies, setStrategies] = useState<Array<ObjectiveDashboardModel>>([]);

    useEffect(() => {
        bindStrategies();
    }, [])


    // Triggers whenever Strategy tab is changed
    const onTabChange = (event: React.ChangeEvent<{}>, tabIndex: number) => {
        setCurrentTab(tabIndex);
    };

    // bind strategies
    const bindStrategies = () => {
        data.map((strategy: Array<ObjectiveDashboardModel>) => {
            setStrategies(strategy);
        })
    }

    // Generate dynamic Strategy tab's component
    //TODO: needs to remove filter from below function later. This is done for time being to launch just Rapid assessment only with Burdenreduction or elimination 
    const renderTabComponent: Array<TabModel> = data?.filter((tab: TabWithChartsModel) => tab.tabId != 3).map((tab: TabWithChartsModel, index: number) => {
        return (new TabModel(
            tab.tabId,
            tab.tabName,
            <WorldMapDashboard data={data[index]?.response} />
        ))
    });

    return (

        <div className="country-card-wrapper">
            <div className="app-tab-wrapper">
                <WHOTabs
                    tabs={renderTabComponent}
                    scrollable={true}
                    value={currentTab}
                    onChange={onTabChange}
                >
                    <div className="p-2"> {renderTabComponent[currentTab].children} </div>
                </WHOTabs>
            </div>
        </div>
    )
}

export default ObjectiveWorldMap;