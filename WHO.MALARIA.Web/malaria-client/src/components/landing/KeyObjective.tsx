﻿import React from "react";
import { Table, TableBody, TableCell, TableRow } from "@mui/material";
import TableHeader from "../assessments/data-collection/desk-review/responses/TableHeader";
import { useTranslation } from "react-i18next";

type KeyObjectiveProps = {
    tableHeader: Array<object>,
    tableBody: Array<object>,
    tableClassName: string,
}

/** Renders landing screen component for modal tables */
function KeyObjective(props: KeyObjectiveProps) {
    const { t } = useTranslation();
    const { tableHeader, tableBody, tableClassName } = props;
    
    return (
        <div className="custom-table">
            <Table className={tableClassName}>              
                <TableHeader headers={tableHeader.map((header: any) => header.label)} />
                <TableBody>
                    <>
                        {tableBody.map((column: any, index: number) => (
                            <TableRow key={`column_${column.field}_${index}`}>
                                <>
                                    <TableCell className="table-highlight">
                                        <label>{column.labelOne}</label>
                                    </TableCell>
                                    <TableCell className="table-highlight">
                                        <label>{column.labelTwo}</label>
                                    </TableCell>
                                    <TableCell>
                                        <label>{column.labelThree}</label>
                                    </TableCell>
                                </>
                            </TableRow>
                        ))}
                    </>
                </TableBody>
            </Table>
        </div>
    );
}

export default KeyObjective;