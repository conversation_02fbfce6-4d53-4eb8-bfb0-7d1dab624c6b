import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate,  useLocation } from "react-router-dom";
import { DashboardModel, SubObjectivePercentageModel } from "../../models/DashboardModel";
import { CaseStrategySelectionType, MetNotMetStatus } from "../../models/Enums";
import { TabModel } from "../../models/TabModel";
import GlobalWorldMap from "./GlobalWorldMap";
import WorldMap from "../controls/WorldMap";
import { ObjectiveDashboardModel, CountryDataModel, ObjectiveModel, } from "../../models/GlobalDashboardModel";
import { WHOTab } from "../controls/Tabs";
import Tabs from "../controls/Tabs";
import classNames from "classnames";
import WHOTabs from "../controls/WHOTabs";
import { TabWithChartsModel } from "../../models/ChartModel";

type DashboardTabularProps = {
    data: any | undefined;
};

////Summary
/* Dashboard Component to generate world map with strategy wise completion status of each objective. */
const WorldMapDashboard = (props: DashboardTabularProps) => {

    const { t } = useTranslation();
    const { data } = props;

    const location: any = useLocation();
    const [objectives, setObjectives] = useState<Array<ObjectiveModel>>([]);
    const [countries, setCountries] = useState<Array<CountryDataModel>>();
    const [currentTabIndex, setcurrentTabIndex] = useState<number>(0);

    useEffect(() => {
        bindObjectives();
        bindCountries();
    }, [])

    // triggers whenever user changes the category tabs
    const onObjectiveTabChange = (event: React.ChangeEvent<{}>, tabIndex: number) => {
        setcurrentTabIndex(tabIndex);
        bindCountries();
    };

    // bind objective based on strategy id
    const bindObjectives = () => {
        data?.map((objective: Array<ObjectiveModel>) => {
            setObjectives(objective);
        })
    }

    // bind countries in specific objective
    const bindCountries = () => {
        data?.map((objective: ObjectiveDashboardModel) => {
            setCountries(objective.countries);
        })
    }

    // Generate dynamic objective tab's component
    const renderTabComponent: Array<TabModel> = data?.map((tab: TabWithChartsModel, index: number) => {
        return (new TabModel(
            tab.tabId,
            tab.tabName,
            <GlobalWorldMap countries={data[index]?.countries} />
        ))
    });

    return (

        <WHOTabs
            tabs={renderTabComponent}
            value={currentTabIndex}
            onChange={onObjectiveTabChange}
            scrollable={false}
        >
            <div className="p-2"> {renderTabComponent[currentTabIndex].children} </div>
        </WHOTabs>
    )
}

export default WorldMapDashboard;