﻿import { MetNotMetEnum } from '../../models/Enums';

// action
export const SET_MET_NOT_MET_STATUS: string =
    "SET_MET_NOT_MET_STATUS";


/**Update met not met status
 * @param status Met not met status
 */
export const setMetNotMetStatus = (status: MetNotMetEnum) => ({
    type: SET_MET_NOT_MET_STATUS,
    status,
});

export default (state: MetNotMetEnum = MetNotMetEnum.NotMet, action: any) => {
    switch (action.type) {
        case SET_MET_NOT_MET_STATUS:
            const { status } = action;

            return { status };
        default:
            return state;
    }
};
