import { UserAssessmentPermission } from "../../models/PermissionModel";
// action
export const SET_USER_ASSESSMENT_PERMISSION: string =
    "SET_USER_ASSESSMENT_PERMISSION";

// initial state initialization
const initialState = {
    assessment: UserAssessmentPermission.init(),
};

/**Set user access permission
 * @param assessment User access permission
 */
export const setUserAssessmentPermission = (assessment: UserAssessmentPermission) => ({
    type: SET_USER_ASSESSMENT_PERMISSION,
    assessment,
});

export default (state = initialState, action: any) => {
    switch (action.type) {
        case SET_USER_ASSESSMENT_PERMISSION:
            const { assessment } = action;
            return { ...state, assessment };
        default:
            return state;
    }
};
