﻿//Actions performed under user guide drawer section are defined here
export const SHOW_USER_GUIDE: string = "SHOW_USER_GUIDE";
export const SET_TAB_CLICK: string = "SET_TAB_CLICK";
export const RESET: string = "RESET";

//Initial/default values for the store's state
const initialState = {
    isUserGuideShown: true,
    isClickedOnTab: false
};

/**Set how to assess guide section(drawer section with info)
 * @param isUserGuideShown a boolean value
 */
export const showUserGuide = (isUserGuideShown: boolean) => ({
    type: SHOW_USER_GUIDE,
    isUserGuideShown,
});

/**Set tab click value when user clicks on stepper or tabs
 * @param isClickedOnTab a boolean value
 */
export const setTabClick = (isClickedOnTab: boolean) => ({
    type: SET_TAB_CLICK,
    isClickedOnTab,
});

/**Resets the state from the store
 * @returns Action object with type property
 */
export const reset = () => ({ type: RESET });

export default (state = initialState, action: any) => {
    switch (action.type) {
        case SHOW_USER_GUIDE:
            const { isUserGuideShown } = action;
            return { ...state, isUserGuideShown };

        case SET_TAB_CLICK:
            const { isClickedOnTab } = action;
            return { ...state, isClickedOnTab };

        case RESET:
            return initialState;

        default:
            return state;
    }
};