﻿//Actions performed under user selected country are defined here
export const SET_SELECTED_COUNTRY: string = "SET_SELECTED_COUNTRY";

//Initial/default values for the store's state
const initialState = {
    isSelectedCountry: false,
};

/**Set selected country once user logged in the application
 * @param isSelectedCountry a boolean value
 */
export const showSelectedCountry = (isSelectedCountry: boolean) => ({
    type: SET_SELECTED_COUNTRY,
    isSelectedCountry,
});

export default (state = initialState, action: any) => {
    switch (action.type) {
        case SET_SELECTED_COUNTRY:
            const { isSelectedCountry } = action;
            return { ...state, isSelectedCountry };

        default:
            return state;
    }
};