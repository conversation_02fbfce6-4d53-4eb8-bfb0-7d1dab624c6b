//Sets action name to check isDirty
export const SET_IS_DIRTY: string =
    "SET_IS_DIRTY";

//Sets action name to reset isDirty 
export const RESET: string = "RESET";

/**
 * Resets the state.
 * @returns Action object with type property
 */
export const reset = () => ({ type: RESET });

/**Called whenever there is any change event occurs for html input element
 * @param isDirty 
 */
export const setIsDirty = (isDirty: boolean) => ({
    type: SET_IS_DIRTY,
    isDirty,
});

export default (state: Boolean = false, action: any) => {
    switch (action.type) {
        case SET_IS_DIRTY:
            const { isDirty } = action;
            return { ...state, isDirty };
        case RESET:
            return false;
        default:
            return state;
    }
};