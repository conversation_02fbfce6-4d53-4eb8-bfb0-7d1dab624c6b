// action
export const SET_ERROR: string =
    "SET_ERROR";

export const REMOVE_ERROR_PROPERTY: string =
    "REMOVE_ERROR_PROPERTY";

export const CLEANUP: string = "CLEANUP";

/**triggers whenever steps or tabs change event occured
 * @param error number of current tab/step index
 */
export const setError = (error: any) => ({
    type: SET_ERROR,
    error,
});

/**
 * Remove existing error property from the error state
 * @param propertyName Name of the property to be removed.
 * @returns Action object with type and payload (Property name)
 */
export const removeErrorProperty = (propertyName: string) => ({ type: REMOVE_ERROR_PROPERTY, propertyName });

/**
 * Cleanup the all errors from the store.
 * @returns Action object with type property
 */
export const cleanup = () => ({ type: CLEANUP });

export default (state: any = {}, action: any) => {
    switch (action.type) {
        case SET_ERROR:
            const { error } = action;

            return { ...state, ...error };
        case REMOVE_ERROR_PROPERTY:
            const updatedState: any = { ...state };
            delete updatedState[action.propertyName];

            return updatedState;
        case CLEANUP:
            return {};
        default:
            return state;
    }
};
