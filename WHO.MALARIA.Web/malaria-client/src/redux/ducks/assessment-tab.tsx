﻿import { AssessmentTabs } from "../../models/Enums";

// action
export const SET_ASSESSMENT_TAB_INDEX: string = "SET_ASSESSMENT_TAB_INDEX";

/**Set assessment tab index
 * @param tabIndex assessment tab index
 */
export const setAssessmentTabIndex = (tabIndex: number) => ({
    type: SET_ASSESSMENT_TAB_INDEX,
    tabIndex,
});

export default (state: number = AssessmentTabs.ScopeDefinition, action: any) => {
    switch (action.type) {
        case SET_ASSESSMENT_TAB_INDEX:
            const { tabIndex } = action;
            return tabIndex;

        default:
            return state;
    }
};