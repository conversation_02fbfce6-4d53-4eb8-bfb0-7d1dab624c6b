// action
export const UPDATE_INDICATOR_GUIDE_STEP_INDEX: string =
  "UPDATE_INDICATOR_GUIDE_STEP_INDEX";

// initial state initialization
const initialState = {
  currentStepIndex: null,
};

/**triggers whenever steps or tabs change event occured
 * @param currentStepIndex number of current tab/step index
 */
export const updateStepIndex = (currentStepIndex: number | null) => ({
  type: UPDATE_INDICATOR_GUIDE_STEP_INDEX,
  currentStepIndex,
});

export default (state = initialState, action: any) => {
  switch (action.type) {
    case UPDATE_INDICATOR_GUIDE_STEP_INDEX:
      const { currentStepIndex } = action;

      return { ...state, currentStepIndex };

    default:
      return state;
  }
};
