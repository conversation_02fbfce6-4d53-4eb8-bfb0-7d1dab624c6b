import { DiagramsStatusModel } from "../../models/DiagramsStatusModel";
import { SaveIndicatorSelectionRequestModel } from "../../models/RequestModels/ScopeDefinitionRequestModel";
import {
  ScopeDefinitionModel,
  StrategySelectionModel,
} from "../../models/ScopeDefinitionModel";

// action
export const UPDATE_STRATEGY_SELECTION: string = "UPDATE_STRATEGY_SELECTION";
export const UPDATE_INDICATOR_SELECTION: string = "UPDATE_INDICATOR_SELECTION";
export const UPDATE_DIAGRAMS_STATUS: string = "UPDATE_DIAGRAMS_STATUS";

// actions

// triggers from assessment scope-definition strategy selection screen with updated object
export const updateStrategySelection = (
  strategySelection: StrategySelectionModel
) => ({
  type: UPDATE_STRATEGY_SELECTION,
  strategySelection,
});

// triggers from assessment scope-definition indicator selection screen with updated object
export const updateIndicatorSelection = (
  indicatorSelection: SaveIndicatorSelectionRequestModel
) => ({
  type: UPDATE_INDICATOR_SELECTION,
  indicatorSelection,
});

// triggers from assessment scope-definition strategy selection screen with diagram status object
export const updateDiagramsStatus = (
  diagramsStatus: DiagramsStatusModel
) => ({
  type: UPDATE_DIAGRAMS_STATUS,
  diagramsStatus,
});

// initial state initialization
const initialState: ScopeDefinitionModel = {
  assessmentId: "",
  strategySelection: new StrategySelectionModel([], false, []),
  indicatorSelection: new SaveIndicatorSelectionRequestModel("", []),
  diagramsStatus: DiagramsStatusModel.init()
};

export default (state = initialState, action: any) => {
  switch (action.type) {
    case UPDATE_INDICATOR_SELECTION:
      const { indicatorSelection } = action;

      return { ...state, indicatorSelection };

    case UPDATE_STRATEGY_SELECTION:
      const { strategySelection } = action;

      return { ...state, strategySelection };

    case UPDATE_DIAGRAMS_STATUS:
      const { diagramsStatus } = action;

      return { ...state, diagramsStatus };

    default:
      return state;
  }
};
