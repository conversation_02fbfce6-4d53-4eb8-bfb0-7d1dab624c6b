import { combineReducers, createStore } from "redux";

import scopeDefinitionReducer from "./ducks/scopedefinition";
import indicatorGuideReducer from "./ducks/indicator-guide";
import errorReducer from './ducks/error';
import metNotMetReducer from './ducks/met-not-met';
import userPermissionReducer from './ducks/user-permission';
import assessmentTabReducer from './ducks/assessment-tab';
import formChangeTrackerReducer from './ducks/form-change-tracker';
import userGuideDrawerReducer from './ducks/user-guide-drawer';
import useSelectedCountryReducer from './ducks/selected-country';

const reducer = combineReducers({
    scopeDefinition: scopeDefinitionReducer,
    indicatorGuide: indicatorGuideReducer,
    error: errorReducer,
    metNotMet: metNotMetReducer,
    userPermission: userPermissionReducer,
    assessmentTab: assessmentTabReducer,
    formChangeTracker: formChangeTrackerReducer,
    userGuideDrawer: userGuideDrawerReducer,
    countrySelection: useSelectedCountryReducer,
});

const store = createStore(reducer);

export default store;