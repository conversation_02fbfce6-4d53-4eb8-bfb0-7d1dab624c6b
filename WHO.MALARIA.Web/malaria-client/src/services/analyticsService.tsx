import { Constants } from "../models/Constants";
import { AnalyticsDataModel } from "../models/AnalyticsDataModel";
import { AnalyticsResponseDataModel } from "../models/AnalyticsResponseDataModel";
import { postCall } from "./api";

/** Analytics service which holds all methods related to analytics */
export const analyticsService = {

    /** Returns the list of analytics data*/
    getAnalyticsData: (data: AnalyticsDataModel) =>
        new Promise<Array<AnalyticsResponseDataModel>>((resolve, reject) => {
            postCall(Constants.Api.Url.GET_ANALYTICS_DATA, data).then(
                (analyticsData: any) => {
                    resolve(analyticsData);
                }
            );
        }),

};