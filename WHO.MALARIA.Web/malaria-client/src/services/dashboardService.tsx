import { Constants } from "../models/Constants";
import { ObjectivePercentageModel, SubObjectivePercentageModel } from "../models/DashboardModel";
import MultiSelectModel from "../models/MultiSelectModel";
import { getCall } from "./api";
import { DashboardReportModel } from "../models/GlobalDashboardModel";
import { DashboardStatisticsModel, DashboardYearWiseStatisticsModel } from "../models/DashboardStatisticsModel";

/** Handles all responsibilities related to application dashboard */
export const dashboardService = {

    /** Gets the assessment years for a country */
    getDashboardYears: (countryId: string) =>
        new Promise<Array<MultiSelectModel>>((resolve, reject) => {
            getCall(Constants.Api.Url.GET_DASHBOARD_YEARS
                .replace("{0}", countryId.toString()))
                .then((response) => {
                    if (response) {
                        const records: Array<MultiSelectModel> = response.map(
                            (record: number): MultiSelectModel =>
                                new MultiSelectModel(record, record.toString(), false, true)
                        );
                        resolve(records);
                    }
                });
        }),

    /** Gets Dashboard records for specific country and year  */
    getDashboardRecords: (countryId: string, year: string) =>
        new Promise<Array<SubObjectivePercentageModel>>
            ((resolve, reject) => {
                getCall(Constants.Api.Url.GET_DASHBOARD_RECORDS
                    .replace("{0}", countryId.toString())
                    .replace("{1}", year.toString())
                )
                    .then((response) => {
                        if (response) {
                            const dashboardRecords = response;
                            resolve(dashboardRecords.objectives);
                        }
                    });
            }),

    /** get regional summary */
    getRegionalSummary: () =>
        new Promise<DashboardReportModel>((resolve, reject) => {
            getCall(Constants.Api.Url.GET_REGIONAL_SUMMARY).then(
                (response: any) => {
                    resolve(response);
                }
            )
                .catch((error) => reject(error));
        }),

    /** get indicator wise summary */
    getIndicatorSummary: () =>
        new Promise<DashboardReportModel>((resolve, reject) => {
            getCall(Constants.Api.Url.GET_INDICATOR_SUMMARY).then(
                (response: any) => {
                    resolve(response);
                }
            )
                .catch((error) => reject(error));
        }),


    /** get objective summary */
    getObjectiveSummary: () =>
        new Promise<DashboardReportModel>((resolve, reject) => {
            getCall(Constants.Api.Url.GET_OBJECTIVE_SUMMARY).then(
                (response: any) => {
                    resolve(response);
                }
            )
                .catch((error) => reject(error));
        }),

    /** Get dashboard assessments details for landing screen carousel */
    getAssessementsDetailsForDashboard: () =>
        new Promise<DashboardStatisticsModel>((resolve, reject) => {
            // api get call
            getCall(Constants.Api.Url.GET_DASHBOARD_ASSESSMENTS_STATISTICS).then(
                (response: any) => {
                    resolve(response);
                }
            );
        }),

    /** Get year wise dashboard assessments details for landing screen carousel */
    getYearWiseAssessementsDetailsForDashboard: (year: string) =>
        new Promise<DashboardYearWiseStatisticsModel>((resolve, reject) => {
            // api get call
            getCall(Constants.Api.Url.GET_DASHBOARD_ASSESSMENTS_YEAR_WISE_STATISTICS
                .replace("{0}", year)
            ).then(
                (response: any) => {
                    resolve(response);
                }
            );
        }),
}