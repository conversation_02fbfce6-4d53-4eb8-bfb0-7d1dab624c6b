import { AssessmentTabs, StatusCode, ValidationStatus } from "../models/Enums";
import { WHOTab } from "../components/controls/Tabs";
import { GetIndicatorModel } from "../models/IndicatorModel";
import { QueryListResultModel } from "../models/QueryListResultModel";
import {
    AssessmentGridModel,
    CreateAssessmentModel,
    FinalizeAssessment,
    UpdateAssessmentRequestModel,
} from "../models/AssessmentModel";
import { StrategyModel } from "../models/StrategyModel";
import DataAnalysisIcon from "../components/ui/icons/DataAnalysisIcon";
import ScopeDefinitionIcon from "../components/ui/icons/ScopeDefinitionIcon";
import ReportGenerationIcon from "../components/ui/icons/ReportGenerationIcon";
import DataCollectionIcon from "../components/ui/icons/DataCollectionIcon";
import { notificationService } from "./notificationService";
import { BaseMessageModel, ErrorModel } from "../models/ErrorModel";
import { Constants } from "../models/Constants";
import { getCall, postCall, putCall } from "./api";
import { GridBaseRequestModel } from "../models/GridBaseRequestModel";
import {
    GetIndicatorsRequestModel,
    SaveIndicatorSelectionRequestModel,
    SaveStrategyRequestModel,
} from "../models/RequestModels/ScopeDefinitionRequestModel";
import {
    GetObjectiveModel,
    GetSubObjectiveModel,
} from "../models/ScopeDefinitionModel";
import { UserAssessmentPermission } from "../models/PermissionModel";
import { GetIndicatorsByStrategyIdRequestModel } from "../models/RequestModels/DataCollectionRequestModel";
import i18next from "../i18n";
import { DeskReviewRequestModel } from "../models/DeskReview/DeskReviewRequestModel";
import { DiagramsStatusModel } from "../models/DiagramsStatusModel"

export const assessmentService = {
    /** Get Permissions of a user for an assessment
     * @param assessmentId as a string
     */
    getPermission: (assessmentId: string) =>
        new Promise<UserAssessmentPermission>((resolve, reject) => {
            const url = Constants.Api.Url.GET_ASSESSMENT_PERMISSIONS.replace(
                "{0}",
                assessmentId
            );
            getCall(url).then((response: any) => {
                resolve(response);
            });
        }),

    /** Creates an Assessment  */
    createAssessment: (data: CreateAssessmentModel) =>
        new Promise((resolve, reject) => {
            postCall<CreateAssessmentModel>(Constants.Api.Url.CREATE_ASSESSMENT, data)
                .then((response: any) => {
                    if (!response) {
                        resolve("");

                        return;
                    }

                    // on success reload the page so that updated cookies being used
                    notificationService.sendMessage(
                        new BaseMessageModel(
                            i18next.t("Assessment.Message.CreateSuccessMessage"),
                            StatusCode.Created
                        )
                    );
                    resolve(response);
                })
                .catch((error: ErrorModel) => {
                    if (error && error.statusCode !== StatusCode.PreConditionFailed) {
                        notificationService.sendMessage(
                            new BaseMessageModel(
                                i18next.t("Errors.SomethingWentWrong"),
                                StatusCode.InternalServerError
                            )
                        );
                    }
                    reject(true);
                });
        }),

    /** Creates an Assessment  */
    updateAssessment: (data: UpdateAssessmentRequestModel) =>
        new Promise((resolve, reject) => {
            putCall<UpdateAssessmentRequestModel>(
                Constants.Api.Url.UPDATE_ASSESSMENT,
                data
            )
                .then((response: any) => {
                    if (!response) {
                        resolve("");

                        return;
                    }

                    // on success reload the page so that updated cookies being used
                    notificationService.sendMessage(
                        new BaseMessageModel(
                            i18next.t("Assessment.Message.UpdatedSuccessMessage"),
                            StatusCode.Created
                        )
                    );
                    resolve(response);
                })
                .catch((error: ErrorModel) => {
                    if (error && error.statusCode !== StatusCode.PreConditionFailed) {
                        notificationService.sendMessage(
                            new BaseMessageModel(
                                i18next.t("Errors.SomethingWentWrong"),
                                StatusCode.InternalServerError
                            )
                        );
                    }
                    reject(true);
                });
        }),

    /** Finalize an assessment
     * @param data as FinalizeAssessment
     */
    finalizeAssessment: (data: FinalizeAssessment) =>
        new Promise<boolean>((resolve, reject) => {
            postCall<FinalizeAssessment>(
                Constants.Api.Url.FINALIZE_ASSESSMENT,
                data
            ).then((response) => {
                if (response) {
                    notificationService.sendMessage(
                        new BaseMessageModel(
                            i18next.t("Assessment.Message.FinalizedSuccessMessage"),
                            ValidationStatus.Success
                        )
                    );
                } else {
                    notificationService.sendMessage(
                        new ErrorModel(
                            i18next.t("Assessment.Message.FinalizeErrorMessage"),
                            ValidationStatus.Failed
                        )
                    );
                }

                resolve(response);
            });
        }),

    /** Get list of all assessments for all countries */
    getAssessments: (request: GridBaseRequestModel) =>
        new Promise<QueryListResultModel<AssessmentGridModel>>(
            (resolve, reject) => {
                postCall<GridBaseRequestModel>(
                    Constants.Api.Url.GET_ASSESSMENTS,
                    request
                )
                    .then((response: any) => {
                        const items =
                            response?.items.map(
                                (item: any): AssessmentGridModel =>
                                    new AssessmentGridModel(
                                        item.id,
                                        item.status,
                                        item.name,
                                        item.startDate,
                                        item.endDate,
                                        "N/A",
                                        item.approach,
                                        item.canConfigure,
                                        item.canViewDetails,
                                        item.displayStatus,
                                        item.caseStrategyType,
                                        item.role
                                    )
                            ) || [];

                        resolve(
                            new QueryListResultModel<AssessmentGridModel>(
                                items,
                                response.totalRows,
                                response.canUserCreateAssessment
                            )
                        );
                    })
                    .catch((error) => reject(error));
            }
        ),

    /** Get Assessment details by assessmentId */
    getAssessmentById: (assessmentId: string) =>
        new Promise<CreateAssessmentModel>((resolve, reject) => {
            // call an api
            const url = Constants.Api.Url.GET_ASSESSMENT.replace(
                "{assessmentId}",
                assessmentId
            );

            getCall(url).then((response: any) => {
                resolve(response);
            });
        }),

    /** Returns the tabs to be rendered for assessment */
    getAssessmentTabs: (activeTabIndex: number): Array<WHOTab> => {
        return [
            {
                id: 0,
                label: i18next.t("Assessment.ScopeDefinitionTitle"),
                icon: <ScopeDefinitionIcon width="20" height="20" />,
                disabled: activeTabIndex !== AssessmentTabs.ScopeDefinition,
            },
            {
                id: 1,
                label: i18next.t("Assessment.DataCollectionTitle"),
                icon: <DataCollectionIcon width="20" height="20" />,
                disabled: activeTabIndex !== AssessmentTabs.DataCollection,
            },
            {
                id: 2,
                label: i18next.t("Assessment.DataAnalysisTitle"),
                icon: <DataAnalysisIcon width="20" height="20" />,
                disabled: activeTabIndex !== AssessmentTabs.DataAnalysis,
            },
            {
                id: 3,
                label: i18next.t("Assessment.ReportGenerationTitle"),
                icon: <ReportGenerationIcon width="20" height="20" />,
                disabled: activeTabIndex !== AssessmentTabs.ReportGeneration,
            },
        ];
    },

    /** returns the list of strategies */
    getStrategies: () =>
        new Promise<Array<StrategyModel>>((resolve, reject) => {
            getCall(Constants.Api.Url.GET_ASSESSMENT_STRATEGIES).then(
                (strategies: Array<StrategyModel>) => {
                    const records = strategies.sort(
                        (prev: StrategyModel, next: StrategyModel) => {
                            if (prev.order > next.order) return 1;
                            else return -1;
                        }
                    );
                    resolve(records);
                }
            );
        }),

    /** Get Indicators for an assessment and strategy */
    getIndicatorsByStragtegyId: (data: GetIndicatorsByStrategyIdRequestModel) =>
        new Promise<Array<GetIndicatorModel>>((resolve, reject) => {
            postCall<GetIndicatorsByStrategyIdRequestModel>(
                Constants.Api.Url.GET_INDICATORS_FOR_DESK_REVIEW,
                data
            ).then((response: any) => {
                resolve(response);
            });
        }),

    /** Get strategies by assessmentId */
    getAssessmentStrategies: (assessmentId: string) =>
        new Promise<Array<StrategyModel>>((resolve, reject) => {
            getCall(
                Constants.Api.Url.GET_STRATEGIES_BY_ASSESSMENTID.replace(
                    "{0}",
                    assessmentId
                )
            ).then((strategies: Array<StrategyModel>) => {
                const records = strategies.sort(
                    (prev: StrategyModel, next: StrategyModel) => {
                        if (prev.order > next.order) return 1;
                        else return -1;
                    }
                );
                resolve(records);
            });
        }),

    /** Get indicators by assessmentId */
    getAssessmentIndicators: (assessmentId: string) =>
        new Promise<Array<GetIndicatorModel>>((resolve, reject) => {
            getCall(
                Constants.Api.Url.GET_INDICATORS_BY_ASSESSMENTID.replace(
                    "{0}",
                    assessmentId
                )
            ).then((indicators: any) => {
                resolve(indicators);
            });
        }),

    /** Saves the strategies by calling an API
     * request: an object of type SaveStrategyRequestModel
     */
    saveStrategies: (request: SaveStrategyRequestModel) =>
        new Promise<boolean>((resolve, reject) => {
            postCall<SaveStrategyRequestModel>(
                Constants.Api.Url.SAVE_ASSESSMENT_STRATEGIES,
                request
            ).then((response) => {
                resolve(response);
            });
        }),

    /*** get categories */
    getObjectives: () =>
        new Promise<Array<GetObjectiveModel>>((resolve, reject) => {
            getCall(Constants.Api.Url.SAVE_ASSESSMENT_OBJECTIVES).then(
                (response: any) => {
                    resolve(response);
                }
            );
        }),

    /** Get sub-categories */
    getSubObjectives: (categoryId: string) =>
        new Promise<Array<GetSubObjectiveModel>>((resolve, reject) => {
            getCall(
                Constants.Api.Url.GET_ASSESSMENT_SUBOBJECTIVES.replace(
                    "{0}",
                    categoryId
                )
            ).then((response: any) => {
                resolve(response);
            });
        }),

    /** Get All Indicators  */
    getIndicators: (request: GetIndicatorsRequestModel) =>
        new Promise<Array<GetIndicatorModel>>((resolve, reject) => {
            postCall<GetIndicatorsRequestModel>(
                Constants.Api.Url.GET_ASSESSMENT_STRATEGY_INDICATORS,
                request
            ).then((response: any) => {
                resolve(response);
            });
        }),

    /** Save Indicators from Assessment Indicator screen */
    saveIndicatorSelection: (request: SaveIndicatorSelectionRequestModel) =>
        new Promise<boolean>((resolve, reject) => {
            postCall<SaveIndicatorSelectionRequestModel>(
                Constants.Api.Url.SAVE_ASSESSMENT_INDICATORS,
                request
            ).then((response) => {
                resolve(response);
                if (response) {
                    notificationService.sendMessage(
                        new BaseMessageModel(
                            i18next.t("Assessment.Message.SaveIndicatorSuccessMessage"),
                            StatusCode.Created
                        )
                    );
                } else {
                    notificationService.sendMessage(
                        new BaseMessageModel(
                            i18next.t("Assessment.Message.SaveIndicatorsErrorMessage"),
                            StatusCode.PreConditionFailed
                        )
                    );
                }
            });
        }),
    /** Save response of indicator for an assessment for the selected strategy */
    saveIndicatorResponse: (request: DeskReviewRequestModel) =>
        new Promise<boolean>((resolve, reject) => {
            postCall<DeskReviewRequestModel>(
                Constants.Api.Url.DATA_COLLECTION_DESK_REVIEW_SAVE_RESPONSE,
                request
            )
                .then((response: any) => {
                    resolve(response);
                    if (response) {
                        notificationService.sendMessage(
                            new BaseMessageModel(
                                i18next.t(
                                    "Assessment.DataCollection.IndicatorDataSaveSuccessMessage",
                                    { sequence: request.sequence }
                                ),
                                StatusCode.Ok
                            )
                        );
                    } else {
                        notificationService.sendMessage(
                            new BaseMessageModel(
                                i18next.t("Assessment.Message.SaveIndicatorsErrorMessage"),
                                StatusCode.PreConditionFailed
                            )
                        );
                    }
                })
                .catch((error) => reject(error));
        }),

    /**
     * Save response captured for the indicator with the uploaded documents 
     * @param formData Object of FormData
     * @param sequence Indicator sequence
     */
    saveIndicatorResponseWithFiles: (formData: FormData, sequence: string) =>
        new Promise<boolean>((resolve, reject) => {
            postCall<FormData>(
                Constants.Api.Url.DATA_COLLECTION_DESK_REVIEW_SAVE_RESPONSE_UPLOAD_DOCUMENT,
                formData
            )
                .then((response: any) => {
                    resolve(response);
                    if (response) {
                        notificationService.sendMessage(
                            new BaseMessageModel(
                                i18next.t(
                                    "Assessment.DataCollection.IndicatorDataSaveSuccessMessage",
                                    { sequence }
                                ),
                                StatusCode.Ok
                            )
                        );
                    } else {
                        notificationService.sendMessage(
                            new BaseMessageModel(
                                i18next.t("Assessment.Message.SaveIndicatorsErrorMessage"),
                                StatusCode.PreConditionFailed
                            )
                        );
                    }
                })
                .catch((error) => reject(error));
        }),

    /** Get response documetns of an indicator
   * @param assessmentIndicatorId A string value
   * @param assessmentStrategyId A string value
   */
    getResponseDocuments: (
        assessmentIndicatorId: string,
        assessmentStrategyId: string
    ) =>
        new Promise<any>((resolve, reject) => {
            const url =
                Constants.Api.Url.GET_DATA_COLLECTION_DESK_REVIEW_RESPONSE_DOCUMENTS.replace(
                    "{assessmentIndicatorId}",
                    assessmentIndicatorId
                ).replace("{assessmentStrategyId}", assessmentStrategyId);

            getCall(url).then((response: any) => {
                if (response) {
                    resolve(response);
                } else {
                    resolve({});
                }
            });
        }),

    /** Get a response of an indicator
     * @param assessmentIndicatorId A string value
     * @param assessmentStrategyId A string value
     */
    getIndicatorResponse: (
        assessmentIndicatorId: string,
        assessmentStrategyId: string
    ) =>
        new Promise<any>((resolve, reject) => {
            const url =
                Constants.Api.Url.DATA_COLLECTION_DESK_REVIEW_GET_RESPONSE.replace(
                    "{0}",
                    assessmentIndicatorId
                ).replace("{1}", assessmentStrategyId);

            getCall(url).then((response: any) => {
                if (response) {
                    resolve(response);
                } else {
                    resolve({});
                }
            });
        }),

    /** Get a response of a parent indicator
     * @param indicatorId A string value
     * @param assessmentId A string value
     *  @param assessmentStrategyId A string value
     */
    getParentIndicatorResponse: (
        indicatorId: string,
        assessmentId: string,
        assessmentStrategyId: string
    ) =>
        new Promise<any>((resolve, reject) => {
            const url =
                Constants.Api.Url.DATA_COLLECTION_DESK_REVIEW_GET_PARENT_RESPONSE.replace(
                    "{0}",
                    indicatorId
                )
                    .replace("{1}", assessmentId)
                    .replace("{2}", assessmentStrategyId);

            getCall(url).then((response: any) => {
                if (response) {
                    resolve(response);
                } else {
                    resolve({});
                }
            });
        }),

    /** Get the recorded checklist variables list for an indicator
     * @param strategyId A string value
     */
    getRecordedChecklistVariables: (
        strategyId: string
    ) =>
        new Promise<any>((resolve, reject) => {
            const url =
                Constants.Api.Url.DATA_COLLECTION_DESK_REVIEW_RECORDED_CHECKLIST_VARIABLES.replace(
                    "{0}",
                    strategyId
                );

            getCall(url).then((response: any) => {
                if (response) {
                    resolve(response);
                } else {
                    resolve({});
                }
            });
        }),

    /** Get the reported checklist variables list for an indicator
     * @param strategyId A string value
     */
    getReportedChecklistVariables: (
        strategyId: string
    ) =>
        new Promise<any>((resolve, reject) => {
            const url =
                Constants.Api.Url.DATA_COLLECTION_DESK_REVIEW_REPORTED_CHECKLIST_VARIABLES.replace(
                    "{0}",
                    strategyId
                );

            getCall(url).then((response: any) => {
                if (response) {
                    resolve(response);
                } else {
                    resolve({});
                }
            });
        }),

    /** Get the checklist indicators list for an indicator
     * @param strategyId A string value
     */
    getChecklistIndicators: (
        strategyId: string
    ) =>
        new Promise<any>((resolve, reject) => {
            const url =
                Constants.Api.Url.DATA_COLLECTION_DESK_REVIEW_CHECKLIST_INDICATORS.replace(
                    "{0}",
                    strategyId
                );

            getCall(url).then((response: any) => {
                if (response) {
                    resolve(response);
                } else {
                    resolve({});
                }
            });
        }),

    /**
     * Uploads the diagram for an assessment and selected strategy for an objective 
     * @param request Contains formdata object which in turn contains the file and other details
     */
    uploadObjectiveDiagram: (request: FormData) =>
        new Promise<Array<string>>((resolve, reject) => {
            postCall(
                Constants.Api.Url.DATA_COLLECTION_DESK_REVIEW_OBJECTIVE_UPLOAD_DIAGRAM,
                request
            ).then((response) => {
                resolve(response);
                if (response) {
                    notificationService.sendMessage(
                        new BaseMessageModel(
                            i18next.t("Assessment.Message.UploadObjectiveDiagramSuccessMessage"),
                            StatusCode.Created
                        )
                    );
                } else {
                    notificationService.sendMessage(
                        new BaseMessageModel(
                            i18next.t("Errors.SomethingWentWrong"),
                            StatusCode.PreConditionFailed
                        )
                    );
                }
            });
        }),

    /**
   * Uploads the diagram for an assessment and selected strategy for an sub objective
   * @param request Contains formdata object which in turn contains the file and other details
   */
    uploadSubObjectiveDiagram: (request: FormData) =>
        new Promise<Array<string>>((resolve, reject) => {
            postCall(
                Constants.Api.Url.DATA_COLLECTION_DESK_REVIEW_SUBOBJECTIVE_UPLOAD_DIAGRAM,
                request
            ).then((response) => {
                resolve(response);
                if (response) {
                    notificationService.sendMessage(
                        new BaseMessageModel(
                            i18next.t("Assessment.Message.UploadDiagramSuccessMessage"),
                            StatusCode.Created
                        )
                    );
                } else {
                    notificationService.sendMessage(
                        new BaseMessageModel(
                            i18next.t("Errors.SomethingWentWrong"),
                            StatusCode.PreConditionFailed
                        )
                    );
                }
            });
        }),

    /**
     * Get the uploaded diagrams and other information
     * @param assessmentId Assessment Id for which the diagrams will be fetched
     * @param strategyId StrategyId Id for which the diagrams will be fetched
     * @returns List of uploaded diagrams
     */
    getObjectiveDiagrams: (
        assessmentId: string,
        strategyId: string
    ) =>
        new Promise<any>((resolve, reject) => {
            const url =
                Constants.Api.Url.GET_DATA_COLLECTION_DESK_REVIEW_OBJECTIVE_UPLOAD_DIAGRAM.replace(
                    "{assessmentId}",
                    assessmentId
                ).replace("{strategyId}", strategyId);

            getCall(url).then((response: any) => {
                if (response) {
                    resolve(response);
                } else {
                    resolve({});
                }
            });
        }),

    /**
   * Get the uploaded diagrams and other information
   * @param assessmentId Assessment Id for which the diagrams will be fetched
   * @param strategyId StrategyId Id for which the diagrams will be fetched
   * @returns List of uploaded diagrams
   */
    getSubObjectiveDiagrams: (
        assessmentId: string,
        strategyId: string
    ) =>
        new Promise<any>((resolve, reject) => {
            const url =
                Constants.Api.Url.GET_DATA_COLLECTION_DESK_REVIEW_SUBOBJECTIVE_UPLOAD_DIAGRAM.replace(
                    "{assessmentId}",
                    assessmentId
                ).replace("{strategyId}", strategyId);

            getCall(url).then((response: any) => {
                if (response) {
                    resolve(response);
                } else {
                    resolve({});
                }
            });
        }),


    /**
   * Get the diagrams status 
   * @param assessmentId Assessment Id for which the diagrams will be fetched
   * @param strategyId StrategyId Id for which the diagrams will be fetched
   * @returns object of diagrams status
   */
    getDiagramsStatus: (
        assessmentId: string,
        strategyId: string
    ) =>
        new Promise<DiagramsStatusModel>((resolve, reject) => {
            const url =
                Constants.Api.Url.GET_DATA_COLLECTION_DESK_REVIEW_OBJECTIVE_DIAGRAM_STATUS.replace(
                    "{assessmentId}",
                    assessmentId
                ).replace("{strategyId}", strategyId);

            getCall(url).then((response: any) => {
                if (response) {
                    resolve(response);
                } else {
                    resolve(DiagramsStatusModel.init());
                }
            });
        }),
};
