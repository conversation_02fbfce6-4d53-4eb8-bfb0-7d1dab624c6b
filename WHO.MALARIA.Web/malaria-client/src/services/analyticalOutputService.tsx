﻿import i18next from "i18next";
import { Constants } from "../models/Constants";
import { getCall, postCall, postCallFile } from "./api";
import { AnalyticalOutputDetailsModel } from "../models/DataAnalysis/DeskReview/AnalyticalOutputDetailsModel";
import { IndicatorReportRequestModel } from "../models/DataAnalysis/DeskReview/IndicatorReportRequestModel";
import { IndicatorReportModel, TabularReportModel } from "../models/DataAnalysis/DeskReview/IndicatorReportDetailsModel";
import { IndicatorsRequestModel } from "../models/RequestModels/AnalyticalOutputRequestModel";
import { notificationService } from "./notificationService";
import { BaseMessageModel } from "../models/ErrorModel";
import { StatusCode } from "../models/Enums";

export const analyticalOutputService = {
    /** Get collection of strategies, indicators, sub-objective, objective information that are associated with the analytical output and assessment
     ** @param assessmentId A string value
     */
    getStrategiesObjectivesSubobjectivesIndicators: (assessmentId: string) =>
        new Promise<AnalyticalOutputDetailsModel>((resolve, reject) => {
            // call an api
            const url = Constants.Api.Url.ANALYTICAL_OUTPUT_GET_STRATEGY_OBJECTIVE_SUBOBJECTIVE_INDICATORS.replace(
                "{0}",
                assessmentId
            );

            getCall(url).then((response: any) => {
                if (response) {
                    resolve(response);
                } else {
                    resolve(AnalyticalOutputDetailsModel.init());
                }
            });
        }),

    /** Get indicators response */
    getIndicatorsResponse: (request: IndicatorReportRequestModel) =>
        new Promise<IndicatorReportModel>((resolve, reject) => {
            postCall<IndicatorReportRequestModel>(
                Constants.Api.Url.ANALYTICAL_OUTPUT_INDICATOR_RESPONSE,
                request
            ).then((response: any) => {
                resolve(response);
            })
                .catch((error) => reject(error));
        }),

    /** Download report for indicator response */
    downloadReport: (request: IndicatorsRequestModel) =>
        new Promise<boolean>((resolve, reject) => {
            postCallFile<IndicatorsRequestModel>(
                Constants.Api.Url.ANALYTICAL_OUTPUT_REPORT_DOWNLOAD,
                request
            )
                .then((response: any) => {
                    resolve(response);
                    if (response) {
                        notificationService.sendMessage(
                            new BaseMessageModel(
                                i18next.t("Assessment.Message.IndicatorReportExportSuccessMessage"),
                                StatusCode.Ok
                            )
                        );
                    } else {
                        notificationService.sendMessage(
                            new BaseMessageModel(
                                i18next.t("Assessment.Message.IndicatorReportExportErrorMessage"),
                                StatusCode.PreConditionFailed
                            )
                        );
                    }
                })
                .catch((error) => reject(error));
        }),

    /** Download report for all indicators response */
    downloadReportForAll: (request: IndicatorsRequestModel) =>
        new Promise<boolean>((resolve, reject) => {
            postCallFile<IndicatorsRequestModel>(
                Constants.Api.Url.ANALYTICAL_OUTPUT_REPORT_DOWNLOAD_ALL,
                request
            )
                .then((response: any) => {
                    resolve(response);
                    if (response) {
                        notificationService.sendMessage(
                            new BaseMessageModel(
                                i18next.t("Assessment.Message.IndicatorReportExportSuccessMessage"),
                                StatusCode.Ok
                            )
                        );
                    } else {
                        notificationService.sendMessage(
                            new BaseMessageModel(
                                i18next.t("Assessment.Message.IndicatorReportExportErrorMessage"),
                                StatusCode.PreConditionFailed
                            )
                        );
                    }
                })
                .catch((error) => reject(error));
        }),

    /**
      * Get the uploaded diagrams and other information
      * @param assessmentId Assessment Id for which the diagrams will be fetched
      * @param strategyId StrategyId Id for which the diagrams will be fetched
      * @returns List of uploaded diagrams
    */
    getObjectiveUploadedDiagrams: (
        assessmentId: string,
        strategyId: string
    ) =>
        new Promise<any>((resolve, reject) => {
            const url =
                Constants.Api.Url.GET_ANALYTICAL_OUTPUT_OBJECTIVE_UPLOAD_DIAGRAM.replace(
                    "{0}",
                    assessmentId
                ).replace("{1}", strategyId);

            getCall(url).then((response: any) => {
                if (response) {
                    resolve(response);
                } else {
                    resolve({});
                }
            });
        }),

    /**
      * Download the uploaded diagrams for objective 3
      * @param assessmentId Assessment Id for which the diagrams will be fetched
      * @param strategyId StrategyId Id for which the diagrams will be fetched
      * @returns List of uploaded diagrams
    */
    downloadObjectiveDiagrams: (
        assessmentId: string,
        strategyId: string
    ) =>
        new Promise<boolean>((resolve, reject) => {
            window.open(`${Constants.Api.baseUrl}/${Constants.Api.Url.ANALYTICAL_OUTPUT_DOWNLOAD_OBJECTIVE_UPLOADED_DIAGRAM.replace(
                "{0}",
                assessmentId
            ).replace("{1}", strategyId)}`)
        }),

    /**
      * Download the uploaded diagrams for sub-objective 2.2
      * @param assessmentId Assessment Id for which the diagrams will be fetched
      * @param strategyId StrategyId Id for which the diagrams will be fetched
      * @returns List of uploaded diagrams
    */
    downloadSubObjectiveDiagrams: (
        assessmentId: string,
        strategyId: string
    ) =>
        new Promise<boolean>((resolve, reject) => {
            window.open(`${Constants.Api.baseUrl}/${Constants.Api.Url.ANALYTICAL_OUTPUT_DOWNLOAD_SUBOBJECTIVE_UPLOADED_DIAGRAM.replace(
                "{0}",
                assessmentId
            ).replace("{1}", strategyId)}`)
        }),
}