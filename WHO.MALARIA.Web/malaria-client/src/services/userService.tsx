﻿import { Constants } from "../models/Constants";
import { StatusCode } from "../models/Enums";
import { EditUserModel, UserListModel } from "../models/UserModel";
import { QueryListResultModel } from "../models/QueryListResultModel";
import { BaseMessageModel, ErrorModel } from "../models/ErrorModel";
import {
    CountryAccessRequestModel,
    ChangeUserStatusRequestModel,
    CreateSuperManagerRequestModel,
    CreateUserRequestModel,
    UpdateUserRequestModel,
    UpdateUserRoleAndStatusRequestModel,
    ResendInvitationRequestModel,
    InvitationRequestModel,
    UserCountryRequestModel,
    ChangeUserTypeRequestModel
} from "../models/RequestModels/UserRequestModel";
import { getCall, patchCall, postCall } from "../services/api";
import { putCall } from "../services/api";
import { notificationService } from "./notificationService";
import { UserRoleEnum } from "../models/Enums";
import { PendingRequestModel } from "../models/RequestModels/UserRequestModel";
import i18next from "../i18n";
import { UserCountry } from "../models/ProfileModel";
import { DeactivatedUserModel, UserCountryAssessRequestModel } from "../models/DeactivatedUserModel";

/** User service which holds all methods related to user */
export const userService = {
    /**
     * Save user by making an api call
     * @param data : instance of CreateUserRequestModel
     */
    register: (data: CreateUserRequestModel) => {
        postCall<CreateUserRequestModel>(Constants.Api.Url.REGISTER_USER, data)
            .then((response: any) => {
                // on success reload the page so that updated cookies being used
                notificationService.sendMessage(
                    new BaseMessageModel(
                        i18next.t("UserManagement.Message.UserRegistrationSubmitted"),
                        StatusCode.Created
                    )
                );
            })
            .catch((error: ErrorModel) => {
                if (error && error.statusCode !== StatusCode.PreConditionFailed) {
                    const message = error.text ?? i18next.t("Errors.SomethingWentWrong");
                    notificationService.sendMessage(
                        new BaseMessageModel(
                            message,
                            StatusCode.InternalServerError
                        )
                    );
                }
            });
    },

    /* Update profile by calling an api */
    updateProfile: (data: UpdateUserRequestModel) => {
        putCall<UpdateUserRequestModel>(Constants.Api.Url.UPDATE_USER, data)
            .then((response: any) => {
                // on success reload the page so that updated cookies being used
                if (
                    data.countryRequestedForIds &&
                    data.countryRequestedForIds.length == 0
                ) {
                    notificationService.sendMessage(
                        new BaseMessageModel(
                            i18next.t("UserManagement.Message.UserUpdatedSuccess"),
                            StatusCode.Created
                        )
                    );
                } else {
                    notificationService.sendMessage(
                        new BaseMessageModel(
                            i18next.t("UserManagement.Message.UserRegistrationSubmitted"),
                            StatusCode.Created
                        )
                    );
                }
            })
            .catch((error: ErrorModel) => {
                if (error && error.statusCode !== StatusCode.PreConditionFailed) {
                    const message = error.text ?? i18next.t("Errors.SomethingWentWrong");
                    notificationService.sendMessage(
                        new BaseMessageModel(
                            message,
                            StatusCode.InternalServerError
                        )
                    );
                }
            });
    },

    /* Country access request by calling an api */
    countryAccessRequestOfWhoUser: (data: UpdateUserRequestModel) => {
        putCall<UpdateUserRequestModel>(Constants.Api.Url.UPDATE_USER, data)
            .then((response: any) => {
                notificationService.sendMessage(
                    new BaseMessageModel(
                        i18next.t(
                            "UserManagement.Message.UserCountryAccessRequest"
                        ),
                        StatusCode.Ok
                    )
                );
            })
            .catch((error: ErrorModel) => {
                if (error && error.statusCode !== StatusCode.PreConditionFailed) {
                    const message = error.text ?? i18next.t("Errors.SomethingWentWrong");
                    notificationService.sendMessage(
                        new BaseMessageModel(
                            message,
                            StatusCode.InternalServerError
                        )
                    );
                }
            });
    },

    /**
     * Create identity and user as a super manager by calling an api
     * @param data: Instance of CreateUserRequestModel
     */
    createSuperManager: (data: CreateSuperManagerRequestModel) =>
        new Promise((resolve, reject) => {
            postCall<CreateSuperManagerRequestModel>(
                Constants.Api.Url.CREATE_SUPER_MANAGER_EXTERNAL,
                data
            )
                .then((response: any) => {
                    if (!response) {
                        resolve(false);
                        return;
                    }

                    // on success reload the page so that updated cookies being used
                    notificationService.sendMessage(
                        new BaseMessageModel(
                            i18next.t("UserManagement.Message.CreateSuperManagerSuccess"),
                            StatusCode.Created
                        )
                    );
                    resolve(true);
                })
                .catch((error: ErrorModel) => {
                    if (error && error.statusCode !== StatusCode.PreConditionFailed) {
                        const message = error.text ?? i18next.t("Errors.SomethingWentWrong");
                        notificationService.sendMessage(
                            new BaseMessageModel(
                                message,
                                StatusCode.InternalServerError
                            )
                        );
                    }
                    reject(true);
                });
        }),

    /**
     * Create identity and user as a super manager for already existing user in AAD
     * @param data: Instance of CreateUserRequestModel
     */
    createExistingSuperManager: (data: CreateSuperManagerRequestModel) =>
        new Promise((resolve, reject) => {
            postCall<CreateSuperManagerRequestModel>(
                Constants.Api.Url.CREATE_SUPER_MANAGER_INTERNAL,
                data
            )
                .then((response: any) => {
                    if (!response) {
                        resolve(false);
                        return;
                    }
                    // on success reload the page so that updated cookies being used
                    if (data.role === UserRoleEnum.WHOAdmin) {
                        notificationService.sendMessage(
                            new BaseMessageModel(
                                i18next.t("UserManagement.Message.CreateWHOAdminSuccessMessage"),
                                StatusCode.Created
                            )
                        );
                    } else {
                        notificationService.sendMessage(
                            new BaseMessageModel(
                                i18next.t("UserManagement.Message.CreateSuperManagerSuccess"),
                                StatusCode.Created
                            )
                        );
                    }
                    resolve(true);
                })
                .catch((error: ErrorModel) => {
                    if (error && error.statusCode !== StatusCode.PreConditionFailed) {
                        const message = error.text ?? i18next.t("Errors.SomethingWentWrong");
                        notificationService.sendMessage(
                            new BaseMessageModel(
                                message,
                                StatusCode.InternalServerError
                            )
                        );
                    }
                    reject(true);
                });
        }),

    /**
     * Returns the list of Super manager for all countries
     */
    getSuperManagersWHOUsersRequestedByWHOAdmin: () =>
        new Promise<Array<UserListModel>>((resolve, reject) => {
            getCall(Constants.Api.Url.GET_SUPER_MANAGERS_WHO_ADMIN_USER_REQUESTED_BY_WHO_ADMIN).then(
                (supermanagers: any) => {
                    resolve(supermanagers);
                }
            );
        }),

    //Return the list of newly registered users
    getNewlyRegisteredInActiveViewers: () =>
        new Promise<Array<UserListModel>>((resolve, reject) => {
            getCall(Constants.Api.Url.GET_REGISTERED_USERS_FOR_WHO_ADMIN).then(
                (response: any) => {
                    resolve(response);
                }
            );
        }),

    /** Returns the list of managers and viewers requested by Super Manager*/
    getManagersAndViewers: (countryId: string) =>
        new Promise<Array<UserListModel>>((resolve, reject) => {
            getCall(Constants.Api.Url.GET_VIEWERS_AND_MANAGERS_REQUESTED_BY_SM.replace("{0}", countryId)).then(
                (users: any) => {
                    resolve(users);
                }
            );
        }),

    /** Get list of users by it's country id */
    getUsersByCountry: (countryId: string) =>
        new Promise((resolve, reject) => {
            getCall(
                Constants.Api.Url.GET_USERS_BY_COUNTRY.replace("{0}", countryId)
            ).then((users: any) => {
                resolve(users);
            });
        }),

    /** Get list of managers by it's country id */
    getManagersByCountry: (countryId: string) =>
        new Promise((resolve, reject) => {
            getCall(
                Constants.Api.Url.GET_MANAGERS_BY_COUNTRY.replace("{0}", countryId)
            ).then((users: any) => {
                resolve(users);
            });
        }),

    /** Get list of viewers by it's country id */
    getViewersByCountry: (countryId: string) =>
        new Promise((resolve, reject) => {
            getCall(
                Constants.Api.Url.GET_MANAGERS_BY_COUNTRY.replace("{0}", countryId)
            ).then((users: any) => {
                resolve(users);
            });
        }),

    /** Update status and role of an user */
    updateUser: (data: UpdateUserRoleAndStatusRequestModel) =>
        new Promise((resolve, reject) => {
            putCall<UpdateUserRoleAndStatusRequestModel>(
                Constants.Api.Url.UPDATE_USER_TYPE_AND_STATUS_BY_SUPER_MANAGER,
                data
            )
                .then((response: any) => {
                    if (!response) {
                        resolve(false);

                        return;
                    }

                    // on success reload the page so that updated cookies being used
                    notificationService.sendMessage(
                        new BaseMessageModel(
                            i18next.t("UserManagement.Message.UpdateUserSuccess"),
                            StatusCode.Ok
                        )
                    );
                    resolve(true);
                })
                .catch((error: ErrorModel) => {
                    if (error && error.statusCode !== StatusCode.PreConditionFailed) {
                        const message = error.text ?? i18next.t("Errors.SomethingWentWrong");
                        notificationService.sendMessage(
                            new BaseMessageModel(
                                message,
                                StatusCode.InternalServerError
                            )
                        );
                    }
                    reject(true);
                });
        }),

    /** Change user's status */
    changeUserStatus: (data: ChangeUserStatusRequestModel) =>
        new Promise((resolve, reject) => {
            putCall<ChangeUserStatusRequestModel>(
                Constants.Api.Url.UPDATE_USER_STATUS_BY_WHO_USER,
                data
            )
                .then((response: any) => {
                    if (!response) {
                        resolve(false);

                        return;
                    }

                    notificationService.sendMessage(
                        new BaseMessageModel(
                            i18next.t("UserManagement.Message.UserStatusChangeSuccess"),
                            StatusCode.Ok
                        )
                    );

                    resolve(true);
                })
                .catch((error: ErrorModel) => {
                    if (error && error.statusCode !== StatusCode.PreConditionFailed) {
                        const message = error.text ?? i18next.t("Errors.SomethingWentWrong");
                        notificationService.sendMessage(
                            new BaseMessageModel(
                                message,
                                StatusCode.InternalServerError
                            )
                        );
                    }
                    reject(true);
                });
        }),

    /** Make viewer as supermanager*/
    makeViewerAsSupermanager: (data: ChangeUserTypeRequestModel) =>
        new Promise((resolve, reject) => {
            putCall<ChangeUserTypeRequestModel>(
                Constants.Api.Url.UPDATE_USER_TYPE_BY_WHO_USER,
                data
            )
                .then((response: any) => {
                    if (!response) {
                        resolve(false);

                        return;
                    }

                    notificationService.sendMessage(
                        new BaseMessageModel(
                            i18next.t("UserManagement.Message.UserTypeChangeSuccess"),
                            StatusCode.Ok
                        )
                    );

                    resolve(true);
                })
                .catch((error: ErrorModel) => {
                    if (error && error.statusCode !== StatusCode.PreConditionFailed) {
                        const message = error.text ?? i18next.t("Errors.SomethingWentWrong");
                        notificationService.sendMessage(
                            new BaseMessageModel(
                                message,
                                StatusCode.InternalServerError
                            )
                        );
                    }
                    reject(true);
                });
        }),

    /** Resend Invitation */
    resendInvitation: (data: ResendInvitationRequestModel) =>
        new Promise((resolve, reject) => {
            putCall<ResendInvitationRequestModel>(
                Constants.Api.Url.RESEND_INVITATION,
                data
            )
                .then((response: any) => {
                    if (!response) {
                        resolve(false);

                        return;
                    }

                    notificationService.sendMessage(
                        new BaseMessageModel(
                            i18next.t("UserManagement.Message.UserInvitationSuccess"),
                            StatusCode.Ok
                        )
                    );

                    resolve(true);
                })
                .catch((error: ErrorModel) => {
                    if (error && error.statusCode !== StatusCode.PreConditionFailed) {
                        const message = error.text ?? i18next.t("Errors.SomethingWentWrong");
                        notificationService.sendMessage(
                            new BaseMessageModel(
                                message,
                                StatusCode.InternalServerError
                            )
                        );
                    }
                    reject(true);
                });
        }),
    /* Retrieve pending requests */
    getPendingRequests: (countryId: string) =>
        new Promise<PendingRequestModel>((resolve, reject) => {
            getCall(Constants.Api.Url.GET_PENDING_REQUESTS.replace("{0}", countryId)).then((response: any) => {
                resolve(response);
            });
        }),

    /* Used to 'Reject' the user */
    onReject: (userId: string, countryId: string, reason: string) =>
        new Promise<boolean>((resolve, reject) => {
            patchCall(Constants.Api.Url.REJECT_USER_ACTIVATION_REQUEST, { userId, countryId, reason })
                .then((response: any) => {
                    if (!response) {
                        resolve(false);
                        return;
                    }
                    notificationService.sendMessage(
                        new BaseMessageModel(
                            i18next.t(
                                "UserManagement.Message.UserRejectionSuccess"
                            ),
                            StatusCode.Ok
                        )
                    );
                    resolve(true);
                })
                .catch((error: any) => {
                    if (error && error.statusCode !== StatusCode.PreConditionFailed) {
                        const message = error.text ?? i18next.t("Errors.SomethingWentWrong");
                        notificationService.sendMessage(
                            new BaseMessageModel(
                                message,
                                StatusCode.InternalServerError
                            )
                        );
                    }
                    reject(error);
                });
        }),

    /**
  * Sends user invitation 
  * @param data: Instance of InvitationRequestModel
  */
    sendInvitation: (data: InvitationRequestModel) =>
        new Promise<boolean>((resolve, reject) => {
            postCall(Constants.Api.Url.SEND_USER_ACTIVATION, data)
                .then((response: any) => {
                    if (!response) {
                        resolve(false);
                        return;
                    }
                    // on success reload the page so that updated cookies being used
                    notificationService.sendMessage(
                        new BaseMessageModel(
                            i18next.t(
                                "UserManagement.Message.UserInvitationSuccess"
                            ),
                            StatusCode.Ok
                        )
                    );
                    resolve(true);
                })
                .catch((error: any) => {
                    if (error && error.statusCode !== StatusCode.PreConditionFailed) {
                        const message = error.text ?? i18next.t("Errors.SomethingWentWrong");
                        notificationService.sendMessage(
                            new BaseMessageModel(
                                message,
                                StatusCode.InternalServerError
                            )
                        );
                    }
                    reject(error);
                });
        }),

    countryAccess: (email: string) =>
        new Promise<boolean>((resolve, reject) => {
            postCall(Constants.Api.Url.SEND_USER_ACTIVATION, { email })
                .then((response: any) => {
                    if (!response) {
                        resolve(false);
                        return;
                    }

                    // on success reload the page so that updated cookies being used
                    notificationService.sendMessage(
                        new BaseMessageModel(
                            i18next.t("UserManagement.Message.UserInvitationSuccess"),
                            StatusCode.Ok
                        )
                    );
                    resolve(true);
                })
                .catch((error: any) => {
                    if (error && error.statusCode !== StatusCode.PreConditionFailed) {
                        const message = error.text ?? i18next.t("Errors.SomethingWentWrong");
                        notificationService.sendMessage(
                            new BaseMessageModel(
                                message,
                                StatusCode.InternalServerError
                            )
                        );
                    }
                    reject(error);
                });
        }),

    /**
     * Approve user country access request by calling an api
     * @param data: Instance of CountryAccessRequestModel
     */
    approveUserCountryAccessRequest: (data: CountryAccessRequestModel) =>
        new Promise<boolean>((resolve, reject) => {
            postCall(Constants.Api.Url.APPROVE_USER_COUNTRY_ACCESS_REQUEST, data)
                .then((response: any) => {
                    if (!response) {
                        resolve(false);
                        return;
                    }
                    // on success reload the page so that updated cookies being used
                    notificationService.sendMessage(
                        new BaseMessageModel(
                            i18next.t(
                                "UserManagement.Message.ApproveUserCountryAccessRequest"
                            ),
                            StatusCode.Ok
                        )
                    );
                    resolve(true);
                })
                .catch((error: any) => {
                    if (error && error.statusCode !== StatusCode.PreConditionFailed) {
                        const message = error.text ?? i18next.t("Errors.SomethingWentWrong");
                        notificationService.sendMessage(
                            new BaseMessageModel(
                                message,
                                StatusCode.InternalServerError
                            )
                        );
                    }
                    reject(error);
                });
        }),

    /**
     * Reject user country access request by calling an api
     * @param data: Instance of CountryAccessRequestModel
     */
    rejectUserCountryAccessRequest: (data: CountryAccessRequestModel) =>
        new Promise<boolean>((resolve, reject) => {
            postCall(Constants.Api.Url.REJECT_USER_COUNTRY_ACCESS_REQUEST, data)
                .then((response: any) => {
                    if (!response) {
                        resolve(false);
                        return;
                    }
                    // on success reload the page so that updated cookies being used
                    notificationService.sendMessage(
                        new BaseMessageModel(
                            i18next.t(
                                "UserManagement.Message.RejectUserCountryAccessRequest"
                            ),
                            StatusCode.Ok
                        )
                    );
                    resolve(true);
                })
                .catch((error: any) => {
                    if (error && error.statusCode !== StatusCode.PreConditionFailed) {
                        const message = error.text ?? i18next.t("Errors.SomethingWentWrong");
                        notificationService.sendMessage(
                            new BaseMessageModel(
                                message,
                                StatusCode.InternalServerError
                            )
                        );
                    }
                    reject(error);
                });
        }),

    /**
     * Forward to WHO for access request by calling an api
     * @param data: Instance of CountryAccessRequestModel
     */
    forwardToWHOAdminRequest: (data: CountryAccessRequestModel) =>
        new Promise<boolean>((resolve, reject) => {
            postCall(Constants.Api.Url.FORWARD_USER_TO_WHO_ADMIN_REQUEST, data)
                .then((response: any) => {
                    if (!response) {
                        resolve(false);
                        return;
                    }
                    // on success reload the page so that updated cookies being used
                    notificationService.sendMessage(
                        new BaseMessageModel(
                            i18next.t(
                                "UserManagement.Message.ForwardToWhoAdminUserCountryAccessRequest"
                            ),
                            StatusCode.Ok
                        )
                    );
                    resolve(true);
                })
                .catch((error: any) => {
                    if (error && error.statusCode !== StatusCode.PreConditionFailed) {
                        const message = error.text ?? i18next.t("Errors.SomethingWentWrong");
                        notificationService.sendMessage(
                            new BaseMessageModel(
                                message,
                                StatusCode.InternalServerError
                            )
                        );
                    }
                    reject(error);
                });
        }),

    /**
     * Accept user's invitation
     * @param ucaId country id for which the user will be having the access
     */
    acceptInvite: (ucaId: string) => new Promise<boolean>((resolve, reject) => {
        postCall(Constants.Api.Url.ACCEPT_USER_INVITE, { ucaId })
            .then(response => {
                if (response) {
                    resolve(true);
                    return;
                }

                resolve(false);
            }).catch((error: any) => {
                if (error && error.statusCode !== StatusCode.PreConditionFailed) {
                    const message = error.text ?? i18next.t("Errors.SomethingWentWrong");
                    notificationService.sendMessage(
                        new BaseMessageModel(
                            message,
                            StatusCode.InternalServerError
                        )
                    );
                }
                reject(error);
            });
    }),

    /* Update user country selected */
    updateUserCountryDeafultOnLandingPage: (countryId: string, userType: number) =>
        new Promise<boolean>((resolve, reject) => {
            patchCall(Constants.Api.Url.USER_UPDATE_DEFAULT_COUNTRY_ON_LANDING_PAGE_REQUEST, { countryId, userType })
                .then((response: any) => {
                    if (response) resolve(response);
                })
                .catch((error: any) => {
                    if (error && error.statusCode !== StatusCode.PreConditionFailed) {
                        const message = error.text ?? i18next.t("Errors.SomethingWentWrong");
                        notificationService.sendMessage(
                            new BaseMessageModel(
                                message,
                                StatusCode.InternalServerError
                            )
                        );
                    }
                    reject(error);
                });
        }),

    /**Get the list of contries user for his assigned  */
    getUserActiveCountries: () =>
        new Promise<Array<UserCountry>>((resolve, reject) => {
            const url = Constants.Api.Url.USER_ACTIVE_COUNTRY;
            getCall(url).then((response: any) => {
                if (response) {
                    resolve(response);
                } else {
                    resolve([]);
                }
            });
        }),

    /** Get deactivated user profile */
    getDeactivatedUserProfile: () =>
        new Promise<DeactivatedUserModel>((resolve, reject) => {
            // api get call
            getCall(Constants.Api.Url.USER_DEACTIVATED_PROFILE).then(
                (response: any) => {
                    resolve(response);
                }
            );
        }),

    /**
     * Save user country access request by making an api call
     * @param data : instance of UserCountryAssessRequestModel
     */
    addUserCountryAccessRequest: (data: UserCountryAssessRequestModel) =>
        new Promise<boolean>((resolve, reject) => {
            postCall<UserCountryAssessRequestModel>(Constants.Api.Url.USER_ADD_COUNTRY_ACCESS_REQUEST, data)
                .then((response: any) => {
                    notificationService.sendMessage(
                        new BaseMessageModel(
                            i18next.t("UserManagement.Message.UserRegistrationSubmitted"),
                            StatusCode.Created
                        )
                    );
                    resolve(response);
                })
                .catch((error: ErrorModel) => {
                    if (error && error.statusCode !== StatusCode.PreConditionFailed) {
                        const message = error.text ?? i18next.t("Errors.SomethingWentWrong");
                        notificationService.sendMessage(
                            new BaseMessageModel(
                                message,
                                StatusCode.InternalServerError
                            )
                        );
                    }
                });
        }),
};
