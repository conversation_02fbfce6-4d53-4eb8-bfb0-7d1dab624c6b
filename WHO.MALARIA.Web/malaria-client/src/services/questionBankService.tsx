﻿import i18next from "i18next";
import { Constants } from "../models/Constants";
import { StatusCode } from "../models/Enums";
import { BaseMessageModel, ErrorModel } from "../models/ErrorModel";
import { RespondentTypeModel } from "../models/QuestionBank/RespondentTypeModel";
import { RespondentTypeRequestModel } from "../models/QuestionBank/RespondentTypeRequestModel";
import { SubObjectiveIndicatorModel, SurveyQuestionModel } from "../models/QuestionBank/SurveyQuestionModel";
import { getCall, postCall, putCall } from "./api";
import { notificationService } from "./notificationService";
import { SurveyQuestionRequestModel } from "../models/QuestionBank/SurveyQuestionRequestModel";
import { SurveyQuestionFinalizeModel } from "../models/QuestionBank/SurveyQuestionFinalizeModel";
import { DataAnalysisReport } from "../models/ReportModel/ReportModel";

export const questionBankService = {
    /** Gets respondent types selected by specific assessmentId 
     * * @param assessmentId A string value
     */
    getRespondentTypesById: (assessmentId: string) =>
        new Promise<Array<RespondentTypeModel>>((resolve, reject) => {
            // call an api
            const url = Constants.Api.Url.QUESTION_BANK_GET_RESPONDENT_TYPES.replace(
                "{0}",
                assessmentId
            );

            getCall(url).then((response: any) => {
                if (response) {
                    resolve(response);
                } else {
                    resolve([]);
                }
            });
        }),

    /** Saves the respondent types by calling an API
     * request: an object of type RespondentTypeRequestModel
     */
    saveRespondentTypes: (request: RespondentTypeRequestModel) =>
        new Promise<boolean>((resolve, reject) => {
            postCall<RespondentTypeRequestModel>(
                Constants.Api.Url.QUESTION_BANK_SAVE_RESPONDENT_TYPES,
                request
            ).then((response) => {
                resolve(response);
                if (response) {
                    notificationService.sendMessage(
                        new BaseMessageModel(
                            i18next.t("Assessment.DataCollection.QuestionBankSurvey.Message.SaveRespondentTypesSuccessMessage"),
                            StatusCode.Created
                        )
                    );
                } else {
                    notificationService.sendMessage(
                        new BaseMessageModel(
                            i18next.t("Errors.SomethingWentWrong"),
                            StatusCode.PreConditionFailed
                        )
                    );
                }
            })
                .catch((error) => reject(error));
        }),

    /** Gets survey questions by specific assessmentId 
     * * @param assessmentId A string value
     */
    getSurveyQuestionsById: (assessmentId: string) =>
        new Promise<Array<SurveyQuestionModel>>((resolve, reject) => {
            // call an api
            const url = Constants.Api.Url.QUESTION_BANK_GET_SURVEY_QUESTIONS.replace(
                "{0}",
                assessmentId
            );

            getCall(url).then((response: any) => {
                if (response) {
                    resolve(response);
                } else {
                    resolve([]);
                }
            });
        }),

    /** Gets objective, subobjective and indicators for survey questions by specific assessmentId 
     * * @param assessmentId A string value
     */
    getObjectivesSubobjectivesIndicators: (assessmentId: string) =>
        new Promise<SubObjectiveIndicatorModel>((resolve, reject) => {
            // call an api
            const url = Constants.Api.Url.QUESTION_BANK_GET_OBJECTIVE_SUBOBJECTIVE_INDICATORS.replace(
                "{0}",
                assessmentId
            );

            getCall(url).then((response: any) => {
                if (response) {
                    resolve(response);
                } else {
                    resolve(SubObjectiveIndicatorModel.init());
                }
            });
        }),

    /** Saves the survey questions by calling an API
     * request: an object of type SurveyQuestionRequestModel
     */
    saveSurveyQuestions: (request: SurveyQuestionRequestModel) =>
        new Promise<boolean>((resolve, reject) => {
            postCall<SurveyQuestionRequestModel>(
                Constants.Api.Url.QUESTION_BANK_SAVE_SURVEY_QUESTIONS,
                request
            ).then((response) => {
                resolve(response);
                if (response) {
                    notificationService.sendMessage(
                        new BaseMessageModel(
                            i18next.t("Assessment.DataCollection.QuestionBankSurvey.Message.SaveSurveyQuestionSuccessMessage"),
                            StatusCode.Created
                        )
                    );
                } else {
                    notificationService.sendMessage(
                        new BaseMessageModel(
                            i18next.t("Errors.SomethingWentWrong"),
                            StatusCode.PreConditionFailed
                        )
                    );
                }
            })
                .catch((error) => reject(error));
        }),

    /** Finalize survey questions response */
    finalizeSurveyQuestions: (request: SurveyQuestionFinalizeModel) =>
        new Promise((resolve, reject) => {
            putCall<SurveyQuestionFinalizeModel>(
                Constants.Api.Url.QUESTION_BANK_FINALIZE_SURVEY_QUESTIONS,
                request
            )
                .then((response) => {
                    if (response) {
                        notificationService.sendMessage(
                            new BaseMessageModel(
                                i18next.t("Assessment.DataCollection.QuestionBankSurvey.Message.FinalizeSurveyQuestionSuccessMessage"),
                                StatusCode.Created
                            )
                        );
                    } else {
                        notificationService.sendMessage(
                            new BaseMessageModel(
                                i18next.t("Assessment.DataCollection.QuestionBankSurvey.Message.FinalizeSurveyQuestionErrorMessage"),
                                StatusCode.PreConditionFailed
                            )
                        );
                    }
                    resolve(response);
                })
                .catch((error) => reject(error));
        }),

    /** Generate Survey Questionnaire
    *  @param assessmentId A string value
    */
    generateSurveyQuestionnaire: (assessmentId: string) =>
        new Promise<boolean>((resolve, reject) => {
            const url = Constants.Api.Url.QUESTION_BANK_GENERATE_QUESTIONNAIRE.replace(
                "{0}",
                assessmentId
            );
            getCall(url, "blob").then((response: any) => {
                resolve(response);
                if (response) {
                    notificationService.sendMessage(
                        new BaseMessageModel(
                            i18next.t("Assessment.DataCollection.QuestionBankSurvey.Message.GenerateQuestionnaireSuccessMessage"),
                            StatusCode.Ok
                        )
                    );
                } else {
                    notificationService.sendMessage(
                        new BaseMessageModel(
                            i18next.t("Assessment.DataCollection.QuestionBankSurvey.Message.GenerateQuestionnaireErrorMessage"),
                            StatusCode.PreConditionFailed
                        )
                    );
                }
            })
                .catch((error: ErrorModel) => {
                    if (error && error.statusCode !== StatusCode.PreConditionFailed) {
                        notificationService.sendMessage(
                            new BaseMessageModel(
                                i18next.t("Errors.SomethingWentWrong"),
                                StatusCode.InternalServerError
                            )
                        );
                    }
                    reject(true);
                });
        }),

    /** Download the health facilities excel template*/
    downloadHealthFacilities: () =>
        new Promise<any>((resolve, reject) => {
            getCall(Constants.Api.Url.QUESTION_BANK_DOWNLOAD_HEALTH_FACILITIES, "blob").then(
                (response: any) => {
                    resolve(response);
                }
            );
        }),

    /** Upload health facility template */
    uploadHealthFacilityTemplateResponse: (request: FormData) =>
        new Promise<boolean>((resolve, reject) => {
            postCall<FormData>(
                Constants.Api.Url.QUESTION_BANK_UPLOAD_HEALTH_FACILITIES,
                request
            )
                .then((response: any) => {
                    if (response) {
                        notificationService.sendMessage(
                            new BaseMessageModel(
                                i18next.t("Assessment.DataCollection.QuestionBankSurvey.Message.HealthFacilityUploadTemplateSuccessMessage"),
                                StatusCode.Created
                            )
                        );
                    } else {
                        notificationService.sendMessage(
                            new BaseMessageModel(
                                i18next.t("Assessment.DataCollection.QuestionBankSurvey.Message.HealthFacilityUploadTemplateErrorMessage"),
                                StatusCode.PreConditionFailed
                            )
                        );
                    }
                    resolve(response);
                })
                .catch((error: ErrorModel) => {
                    if (error && error.statusCode !== StatusCode.PreConditionFailed) {
                        const message = error.text ?? i18next.t("Errors.SomethingWentWrong");
                        notificationService.sendMessage(
                            new BaseMessageModel(
                                message,
                                StatusCode.InternalServerError
                            )
                        );
                    }
                    reject(true);
                });
        }),

    /** Health facility export excel */
    exportHealthFacilityExcel: (countryId: string) =>
        new Promise<boolean>((resolve, reject) => {

            const url = Constants.Api.Url.QUESTION_BANK_EXPORT_HEALTH_FACILITIES.replace(
                "{0}",
                countryId
            );
            getCall(url, "blob").then((response: any) => {
                resolve(response);
            })
                .catch((error: ErrorModel) => {
                    if (error && error.statusCode !== StatusCode.PreConditionFailed) {
                        notificationService.sendMessage(
                            new BaseMessageModel(
                                i18next.t("Errors.SomethingWentWrong"),
                                StatusCode.InternalServerError
                            )
                        );
                    }
                    reject(true);
                });
        }),

    /** Check health facility data
*  @param assessmentId A string value
*/
    hasHealthFacilityData: (assessmentId: string) =>
        new Promise<boolean>((resolve, reject) => {
            // call an api
            const url = Constants.Api.Url.QUESTION_BANK_HAS_HEALTH_FACILITIES_DATA.replace(
                "{0}",
                assessmentId
            );

            getCall(url).then((response: any) => {
                if (response) {
                    resolve(response);
                } else {
                    resolve(false);
                }
            });
        }),

    /** Health facility export excel for assessment */
    exportHealthFacilityExcelForAssessment: (assessmentId: string, countryId: string) =>
        new Promise<boolean>((resolve, reject) => {
            const url = Constants.Api.Url.QUESTION_BANK_EXPORT_HEALTH_FACILITIES_FOR_ASSESSMENT
                .replace("{0}", assessmentId)
                .replace("{1}", countryId);

            getCall(url, "blob").then((response: any) => {
                resolve(response);
            })
                .catch((error: ErrorModel) => {
                    if (error) {
                        notificationService.sendMessage(
                            new BaseMessageModel(
                                i18next.t("Errors.SomethingWentWrong"),
                                StatusCode.PreConditionFailed
                            )
                        );
                    }
                    reject(true);
                });
        }),

    /** Gets Last Uploaded Health Facility file name  by specific CountryId  
    * * @param CountryId A string value
    */
    getLastUploadedFileName: (countryId: string) =>
        new Promise<string>((resolve, reject) => {
            // call an api
            const url = Constants.Api.Url.QUESTION_BANK_GET_UPLOADES_FILENAME.replace(
                "{0}",
                countryId
            );

            getCall(url).then((response: any) => {
                if (response) {
                    resolve(response);
                } else {
                    resolve("");
                }
            });
        }),


    /** Gets Last Uploaded Health Facility file name  by specific CountryId  
        * * @param CountryId A string value
        */
    getUploadedShellReports: (assessmentId: string) =>
        new Promise<DataAnalysisReport>((resolve, reject) => {
            // call an api
            const url = Constants.Api.Url.QUESTION_BANK_GET_UPLOADED_SHELL_REPORT_FILES.replace(
                "{0}",
                assessmentId
            );

            getCall(url).then((response: any) => {
                if (response) {
                    resolve(response);
                } else {
                    resolve(DataAnalysisReport.init());
                }
            });
        }),

}
