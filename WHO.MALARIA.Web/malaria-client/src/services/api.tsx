﻿import { AxiosError, AxiosResponse, ResponseType } from "axios";
import { StatusCode } from "../models/Enums";
import { ErrorModel } from "../models/ErrorModel";
import { instance } from "./instance";
import { loaderService, notificationService } from "./notificationService";
import { authService } from "./authService";
import i18next from "../i18n";


/**
 * Make a server call for POST type
 * @param data: Generic type of request data
 * @param url: An endpoint
 */
export const postCall = <T extends unknown>(url: string, data: T) => {
    try {
        const response = instance
            .post(url, data)
            .then((response: AxiosResponse) => {
                loaderService.setLoading(false);
                return response.data;
            })
            .catch((error: AxiosError | ErrorModel) => {
                if (error instanceof ErrorModel) {
                    if (error.statusCode === StatusCode.UnAuthorized) {
                        notificationService.sendMessage(
                            new ErrorModel(
                                i18next.t("NotificationMessages.AuthenticationRedirection"),
                                StatusCode.UnAuthorized
                            )
                        );
                        authService.challenge();

                        throw null as unknown;
                    }
                    notificationService.sendMessage(error);
                    throw error;
                }
                notificationService.sendMessage(
                    new ErrorModel(error.message, StatusCode.InternalServerError)
                );
                throw error;
            });

        return response;
    } catch (error) {
        throw error;
    }
};

/**
 * Make a server call for POST type for file
 * @param data: Generic type of request data
 * @param url: An endpoint
 */
export const postCallFile = <T extends unknown>(url: string, data: T) => {
    try {
        const response = instance
            .post(url, data,
                {
                    responseType: 'blob',
                })
            .then((response: AxiosResponse) => {
                loaderService.setLoading(false);
                return response.data;
            })
            .catch((error: AxiosError | ErrorModel) => {
                if (error instanceof ErrorModel) {
                    if (error.statusCode === StatusCode.UnAuthorized) {
                        notificationService.sendMessage(
                            new ErrorModel(
                                i18next.t("NotificationMessages.AuthenticationRedirection"),
                                StatusCode.UnAuthorized
                            )
                        );
                        authService.challenge();

                        throw null as unknown;
                    }
                    notificationService.sendMessage(error);
                    throw error;
                }
                notificationService.sendMessage(
                    new ErrorModel(error.message, StatusCode.InternalServerError)
                );
                throw error;
            });

        return response;
    } catch (error) {
        throw error;
    }
};

/**
 * Make a server call for PATCH type
 * @param data: Generic type of request data
 * @param url: An endpoint
 */
export const patchCall = <T extends unknown>(url: string, data: T) => {
    try {
        const response = instance
            .patch(url, data)
            .then((response: AxiosResponse) => {
                loaderService.setLoading(false);
                return response.data;
            })
            .catch((error: AxiosError | ErrorModel) => {
                if (error instanceof ErrorModel) {
                    if (error.statusCode === StatusCode.UnAuthorized) {
                        notificationService.sendMessage(
                            new ErrorModel(
                                i18next.t("NotificationMessages.AuthenticationRedirection"),
                                StatusCode.UnAuthorized
                            )
                        );
                        authService.challenge();

                        throw null as unknown;
                    }
                    notificationService.sendMessage(error);
                    throw error;
                }
                notificationService.sendMessage(
                    new ErrorModel(error.message, StatusCode.InternalServerError)
                );
                throw error;
            });

        return response;
    } catch (error) {
        throw error;
    }
};

/**
* Make a server call for HTTP Get
* @param url: An endpoint
* @param responseType: Response type of the response
* @param showNotificationOnNotFoundError: Show notification error message for StatusCode other that 404(NotFound)
*/
export const getCall = (url: string, responseType: ResponseType = "json", showNotificationOnNotFoundError: boolean = true) => {
    try {
        const response = instance
            .get(url, { responseType })
            .then((response: AxiosResponse) => {
                loaderService.setLoading(false);
                return response.data;
            })
            .catch((error: AxiosError | ErrorModel) => {
                loaderService.clearLoading();
                if (error instanceof ErrorModel) {
                    if (error.statusCode === StatusCode.UnAuthorized) {
                        notificationService.sendMessage(
                            new ErrorModel(i18next.t("NotificationMessages.Redirecting"), StatusCode.UnAuthorized)
                        );
                        authService.challenge();

                        throw null as unknown;
                    }

                    if (error.statusCode === StatusCode.NotFound && !showNotificationOnNotFoundError) {
                        throw error;
                    }

                    notificationService.sendMessage(error);
                    throw error;
                }
                notificationService.sendMessage(
                    new ErrorModel(error.message, StatusCode.InternalServerError)
                );
                throw error;
            });

        return response;
    } catch (error) {
        loaderService.clearLoading();
        throw error;
    }
};

/**
 * Make a server call for PUT type
 * @param data: Request data
 * @param url: An endpoint
 */
export const putCall = <T extends unknown>(url: string, data: T) => {
    try {
        const response = instance
            .put(url, data)
            .then((response: AxiosResponse) => {
                loaderService.setLoading(false);
                return response.data;
            })
            .catch((error: AxiosError | ErrorModel) => {
                if (error instanceof ErrorModel) {
                    if (error.statusCode === StatusCode.UnAuthorized) {
                        notificationService.sendMessage(
                            new ErrorModel(
                                i18next.t("NotificationMessages.AuthenticationRedirection"),
                                StatusCode.UnAuthorized
                            )
                        );
                        authService.challenge();

                        throw null as unknown;
                    }
                    notificationService.sendMessage(error);
                    throw error;
                }
                notificationService.sendMessage(
                    new ErrorModel(error.message, StatusCode.InternalServerError)
                );
                throw error;
            });

        return response;
    } catch (error) {
        throw error;
    }
};

/**
 * Make a server call for DELETE type
 * @param data: Request data
 * @param url: An endpoint
 */
export const deleteCall = <T extends unknown>(url: string, data: T) => {
    try {
        const response = instance
            .delete(url, { data })
            .then((response: AxiosResponse) => {
                loaderService.setLoading(false);
                return response.data;
            })
            .catch((error: AxiosError | ErrorModel) => {
                if (error instanceof ErrorModel) {
                    if (error.statusCode === StatusCode.UnAuthorized) {
                        notificationService.sendMessage(
                            new ErrorModel(
                                i18next.t("NotificationMessages.AuthenticationRedirection"),
                                StatusCode.UnAuthorized
                            )
                        );
                        authService.challenge();

                        throw null as unknown;
                    }
                    notificationService.sendMessage(error);
                    throw error;
                }
                notificationService.sendMessage(
                    new ErrorModel(error.message, StatusCode.InternalServerError)
                );
                throw error;
            });

        return response;
    } catch (error) {
        throw error;
    }
};