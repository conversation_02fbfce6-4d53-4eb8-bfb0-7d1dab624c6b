import { Constants } from "../models/Constants";
import { BaseMessageModel, ErrorModel } from "../models/ErrorModel";
import { deleteCall, getCall, postCall } from "./api";
import { notificationService } from "./notificationService";
import i18next from "../i18n";
import { StatusCode } from "../models/Enums";
import { DataAnalysisReport, DeleteReportFile, PublishAssessment, ShowAssessmentResultOnGlobalDashboard } from "../models/ReportModel/ReportModel";

/** Handles all responsibilities related to Report generate */
export const reportService = {


    /** Publish assessment
   * request: an object of type PublishAssessment
   */
    publishAssessment: (request: PublishAssessment) =>
        new Promise<boolean>((resolve, reject) => {
            postCall<PublishAssessment>(
                Constants.Api.Url.DATA_ANALYSIS_REPORT_PUBLISH_RESPONSE,
                request
            ).then((response) => {
                resolve(response);
                if (response) {
                    notificationService.sendMessage(
                        new BaseMessageModel(
                            i18next.t("Report.Message.PublishSuccessMessage"),
                            StatusCode.Created
                        )
                    );
                } else {
                    notificationService.sendMessage(
                        new BaseMessageModel(
                            i18next.t("Report.Message.PublishErrorMessage"),
                            StatusCode.PreConditionFailed
                        )
                    );
                }
            });
        }),


    /** Opt out assessment on global dashboard
  * request: an object of type ShowAssessmentResultOnGlobalDashboard
  */
    optOutAssessmentOnGlobalDashboard: (request: ShowAssessmentResultOnGlobalDashboard) =>
        new Promise<boolean>((resolve, reject) => {
            postCall<ShowAssessmentResultOnGlobalDashboard>(
                Constants.Api.Url.DATA_ANALYSIS_SHOW_ASSESSMENT_ON_GLOBAL_DASHBOARD_RESPONSE,
                request
            ).then((response) => {
                resolve(response);
                if (response) {
                    notificationService.sendMessage(
                        new BaseMessageModel(
                            request.showResultOnGlobalDashboard ? i18next.t("Report.Message.OptOutSuccessMessage") : i18next.t("Report.Message.OptInSuccessMessage"),
                            StatusCode.Created
                        )
                    );
                } else {
                    notificationService.sendMessage(
                        new BaseMessageModel(
                            i18next.t("Report.Message.OptOutErrorMessage"),
                            StatusCode.PreConditionFailed
                        )
                    );
                }
            });
        }),

    /** Upload report file
 * request: an object of type FormData
 */
    uploadFile: (request: FormData) =>
        new Promise<boolean>((resolve, reject) => {
            postCall<FormData>(
                Constants.Api.Url.DATA_ANALYSIS_UPLOAD_FILE,
                request
            )
                .then((response: any) => {
                    if (response) {
                        notificationService.sendMessage(
                            new BaseMessageModel(
                                i18next.t("Report.Message.UploadFileSuccessMessage"),
                                StatusCode.Created
                            )
                        );
                    } else {
                        notificationService.sendMessage(
                            new BaseMessageModel(
                                i18next.t("Report.Message.UploadFileErrorMessage"),
                                StatusCode.PreConditionFailed
                            )
                        );
                    }
                    resolve(response);
                })
                .catch((error: ErrorModel) => {
                    if (error) {
                        notificationService.sendMessage(
                            new BaseMessageModel(
                                i18next.t("Errors.SomethingWentWrong"),
                                StatusCode.PreConditionFailed
                            )
                        );
                    }
                    reject(true);
                });
        }),

    /** Gets reports data selected by specific assessmentId 
* * @param assessmentId A string value
*/
    getReports: (assessmentId: string) =>
        new Promise<DataAnalysisReport>((resolve, reject) => {
            // call an api
            const url = Constants.Api.Url.DATA_ANALYSIS_REPORTS.replace(
                "{0}",
                assessmentId
            );

            getCall(url).then((response: any) => {
                if (response) {
                    resolve(response);
                } else {
                    resolve(DataAnalysisReport.init());
                }
            });
        }),

    /** Download report file by report id        
*  @param reportId A string value   
*/
    downloadReport: (reportId: string) =>
        new Promise<boolean>((resolve, reject) => {
            const url = Constants.Api.Url.DATA_ANALYSIS_DOWNLOAD_FILE.replace(
                "{0}",
                reportId
            );
            getCall(url, "blob").then((response: any) => {
                resolve(response);
            })
                .catch((error: ErrorModel) => {
                    if (error) {
                        notificationService.sendMessage(
                            new BaseMessageModel(
                                i18next.t("Errors.SomethingWentWrong"),
                                StatusCode.PreConditionFailed
                            )
                        );
                    }
                    reject(true);
                });
        }),

    /** Delete report file by report id        
    *  @param request: an object of type DeleteReportFile
    */
    deleteFile: (request: DeleteReportFile) =>
        new Promise<boolean>((resolve, reject) => {
            deleteCall<DeleteReportFile>(
                Constants.Api.Url.DATA_ANALYSIS_DELETE_FILE, request)
                .then((response: any) => {
                    if (response) {
                        notificationService.sendMessage(
                            new BaseMessageModel(
                                i18next.t("Report.Message.DeleteFileSuccessMessage"),
                                StatusCode.Created
                            )
                        );
                    } else {
                        notificationService.sendMessage(
                            new BaseMessageModel(
                                i18next.t("Report.Message.DeleteFileErrorMessage"),
                                StatusCode.PreConditionFailed
                            )
                        );
                    }
                    resolve(response);
                })
                .catch((error: ErrorModel) => {
                    if (error) {
                        notificationService.sendMessage(
                            new BaseMessageModel(
                                i18next.t("Errors.SomethingWentWrong"),
                                StatusCode.PreConditionFailed
                            )
                        );
                    }
                    reject(true);
                });
        }),
};