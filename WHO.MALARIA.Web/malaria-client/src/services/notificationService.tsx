﻿import { Subject } from "rxjs";
import { BaseMessageModel } from "../models/ErrorModel";

const notificationSubject = new Subject();
const loaderSubject = new Subject();

export const notificationService = {
  sendMessage: (message: BaseMessageModel) => notificationSubject.next(message),
  clearMessages: () => notificationSubject.next(undefined),
  getMessage: () => notificationSubject.asObservable(),
};

export const loaderService = {
  setLoading: (isLoading: boolean) => loaderSubject.next(isLoading),
  clearLoading: () => loaderSubject.next(false),
  getLoading: () => loaderSubject.asObservable(),
};
