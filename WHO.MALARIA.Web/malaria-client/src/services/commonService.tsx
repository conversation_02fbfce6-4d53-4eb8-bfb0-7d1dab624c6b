import { Constants } from "../models/Constants";
import { getCall, postCall } from "./api";
import { KeyValuePair } from "../models/DeskReview/KeyValueType";
import { RetrieveMultipleRecords } from "../models/RetrieveMultipleRecords";
import MultiSelectModel from "../models/MultiSelectModel";
import { DashboardModel } from "../models/DashboardModel";
import { notificationService } from "./notificationService";
import { DeactivatedUserModel } from "../models/DeactivatedUserModel"
import { BaseMessageModel, ErrorModel } from "../models/ErrorModel";
import i18next from "../i18n";
import { StatusCode } from "../models/Enums";

export const commonServices = {
    /** retrieve all records from country entity by making an api call */
    retrieveMultpleRecords: (entity: string) =>
        new Promise<Array<MultiSelectModel>>((resolve, reject) => {
            // replace the placeholder by entity name
            const url = Constants.Api.Url.RETRIEVE_MULTIPLE_RECORDS.replace(
                "{0}",
                entity
            );

            postCall<RetrieveMultipleRecords>(
                url,
                new RetrieveMultipleRecords(entity)
            )
                .then((response: any) => {
                    if (response) {
                        const records: Array<MultiSelectModel> = response.map(
                            (record: any): MultiSelectModel =>
                                new MultiSelectModel(record.id, record.name, false, true)
                        );
                        resolve(records);
                    }
                    resolve([]);
                })
                .catch((error: any) => {
                    if (error) {
                        notificationService.sendMessage(
                            new BaseMessageModel(
                                i18next.t("Errors.SomethingWentWrong"),
                                StatusCode.PreConditionFailed
                            )
                        );
                    }
                    reject(error);
                });
        }),

    /** retrieve all records from country entity by making an api call */
    getCountries: () =>
        new Promise<Array<MultiSelectModel>>((resolve, reject) => {
            postCall<RetrieveMultipleRecords>(
                Constants.Api.Url.RETRIEVE_COUNTRY_RECORDS,
                new RetrieveMultipleRecords("")
            )
                .then((response: any) => {
                    if (response) {
                        const records: Array<MultiSelectModel> = response.map(
                            (record: any): MultiSelectModel =>
                                new MultiSelectModel(record.id, record.name, false, true)
                        );
                        resolve(records);
                    }
                    resolve([]);
                })
                .catch((error: any) => {
                    if (error) {
                        notificationService.sendMessage(
                            new BaseMessageModel(
                                i18next.t("Errors.SomethingWentWrong"),
                                StatusCode.PreConditionFailed
                            )
                        );
                    }
                    reject(error);
                });
        }),

    /** Retrieves all country records for logged in user. */
    getCountriesForCurrentUser: () =>
        new Promise<Array<MultiSelectModel>>((resolve, reject) => {
            postCall<RetrieveMultipleRecords>(
                Constants.Api.Url.RETRIEVE_COUNTRY_RECORDS_LOGIN_USER,
                new RetrieveMultipleRecords("")
            )
                .then((response: any) => {
                    if (response) {
                        const records: Array<MultiSelectModel> = response.map(
                            (record: any): MultiSelectModel =>
                                new MultiSelectModel(record.id, record.name, false, true)
                        );
                        resolve(records);
                    }
                    resolve([]);
                })
                .catch((error: any) => {
                    if (error) {
                        notificationService.sendMessage(
                            new BaseMessageModel(
                                i18next.t("Errors.SomethingWentWrong"),
                                StatusCode.PreConditionFailed
                            )
                        );
                    }
                    reject(error);
                });
        }),

    /** Download the assessment tools */
    downloadAllTools: () =>
        new Promise<any>((resolve, reject) => {
            getCall(Constants.Api.Url.DOWNLOAD_ALL_TOOL, "blob").then(
                (response: any) => {
                    resolve(response);
                }
            );
        }),

    /** Download the overview toolkit document*/
    downloadToolkitOverview: () =>
        new Promise<any>((resolve, reject) => {
            getCall(Constants.Api.Url.OVERVIEW_DOWNLOAD, "blob").then(
                (response: any) => {
                    resolve(response);
                }
            );
        }),

    /** Get dashboard assessments details for carousel  */
    getAssessementsDetailForDashbaord: () =>
        new Promise<DashboardModel>((resolve, reject) => {
            // api get call
            getCall(Constants.Api.Url.DASHBOARD_ASSESSMENT_STATISTICS).then(
                (response: any) => {
                    resolve(response);
                }
            );
        }),

    /** Retrieves all country records whose super manager is not created or not active. */
    getCountriesWithoutSuperManager: () =>
        new Promise<Array<MultiSelectModel>>((resolve, reject) => {
            postCall<RetrieveMultipleRecords>(
                Constants.Api.Url.GET_COUNTRIES_WITHOUT_SUPER_MANAGER,
                new RetrieveMultipleRecords("")
            )
                .then((response: any) => {
                    if (response) {
                        const records: Array<MultiSelectModel> = response.map(
                            (record: any): MultiSelectModel =>
                                new MultiSelectModel(record.id, record.name, false, true)
                        );
                        resolve(records);
                    }
                    resolve([]);
                })
                .catch((error: any) => {
                    if (error) {
                        notificationService.sendMessage(
                            new BaseMessageModel(
                                i18next.t("Errors.SomethingWentWrong"),
                                StatusCode.PreConditionFailed
                            )
                        );
                    }
                    reject(error);
                });
        }),

    /** Upload Malaria Surveillance Documents */
    uploadToolkitDocuments: (request: FormData) =>
        new Promise<boolean>((resolve, reject) => {
            postCall<FormData>(
                Constants.Api.Url.SAVE_UPLOADED_TOOLKIT_DOCUMENT,
                request
            )
                .then((response: any) => {
                    if (response) {
                        notificationService.sendMessage(
                            new BaseMessageModel(
                                i18next.t("UploadDocument.Message.UploadDocumentsSuccessMessage"),
                                StatusCode.Created
                            )
                        );
                    } else {
                        notificationService.sendMessage(
                            new BaseMessageModel(
                                i18next.t("UploadDocument.Message.UploadDocumentsErrorMessage"),
                                StatusCode.PreConditionFailed
                            )
                        );
                    }
                    resolve(response);
                })
                .catch((error: ErrorModel) => {
                    if (error) {
                        notificationService.sendMessage(
                            new BaseMessageModel(
                                i18next.t("Errors.SomethingWentWrong"),
                                StatusCode.PreConditionFailed
                            )
                        );
                    }
                    reject(true);
                });
        }),

};

/** Return 12 Month Key Value List */
export default function useMonths(): Array<
    KeyValuePair<string, number | null>
> {
    const months = [
        { key: Constants.Common.Months[0], value: 1 },
        { key: Constants.Common.Months[1], value: 2 },
        { key: Constants.Common.Months[2], value: 3 },
        { key: Constants.Common.Months[3], value: 4 },
        { key: Constants.Common.Months[4], value: 5 },
        { key: Constants.Common.Months[5], value: 6 },
        { key: Constants.Common.Months[6], value: 7 },
        { key: Constants.Common.Months[7], value: 8 },
        { key: Constants.Common.Months[8], value: 9 },
        { key: Constants.Common.Months[9], value: 10 },
        { key: Constants.Common.Months[10], value: 11 },
        { key: Constants.Common.Months[11], value: 12 },
    ];
    return months;
}
