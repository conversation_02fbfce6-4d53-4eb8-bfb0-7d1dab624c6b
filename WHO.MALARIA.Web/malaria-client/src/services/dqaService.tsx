import { Constants } from "../models/Constants";
import { DataSourceModel } from "../models/DQA/DataSourceModel";
import { ServiceLevelFinalizeModel, ServiceLevelRequestModel, ServiceLevelDeleteModel } from "../models/DQA/ServiceLevelRequestModel";
import { FinalizeObservedDataQualityReasonModel, ServiceLevelObservedDataQualityReasonModel, ServiceLevelSummaryModel } from "../models/DQA/ServiceLevelSummaryModel";
import { BaseMessageModel, ErrorModel } from "../models/ErrorModel";
import { VariableModel } from "../models/VariableModel";
import { deleteCall, getCall, patchCall, postCall, putCall } from "./api";
import { notificationService } from "./notificationService";
import i18next from "../i18n";
import { StatusCode } from "../models/Enums";
import { IndicatorReportModel } from "../models/DataAnalysis/DeskReview/IndicatorReportDetailsModel";
import { IndicatorModel } from "../models/DQA/IndicatorModel";
import { DeskLevelFinalizeModel, DeskLevelRequestModel, DLDQARequestModel } from "../models/DQA/DeskLevelRequestModel";
import { EliminationSummaryModel, SummaryModel } from "../models/DQA/EliminationSummaryModel";
import MultiSelectModel from "../models/MultiSelectModel";
import { DeskLevelSummaryReportModel, SummaryDataModel } from "../models/DQA/DeskLevelSummaryReportModel";
import { RetrieveMultipleRecords } from "../models/RetrieveMultipleRecords";

/** Handles all responsibilities related to DQA */
export const dqaService = {

    /** DQA SL Export Excel     
     */
    exportExcel: () =>
        new Promise<boolean>((resolve, reject) => {
            window.open(`${Constants.Api.baseUrl}/${Constants.Api.Url.EXPORT_SL_DQA_EXCEL_FILE}`)
        }),

    /** DQA SL File Upload */
    fileUploadResponse: (request: FileList) =>
        new Promise<boolean>((resolve, reject) => {
            let formData = new FormData();
            formData.append("file", request[0])
            postCall<any>(
                Constants.Api.Url.UPLOAD_SL_DQA_EXCEL_FILE,
                formData
            ).then((response: any) => {
                resolve(response);
                if (response) {

                }
            })
                .catch((error) => reject(error));
        }),

    /** Get a response of a variables for DQA
    *  @param assessmentStrategyId A string value
    *   @param isSystemDefined A boolean value
    */
    getDQACoreVariables: (
        assessmentStrategyId: string, isSystemDefined: boolean
    ) =>
        new Promise<Array<VariableModel>>((resolve, reject) => {
            const url =
                Constants.Api.Url.DATA_COLLECTION_DQA_VARIABLES_GET_RESPONSE.replace(
                    "{0}",
                    assessmentStrategyId
                ).replace("{1}", String(isSystemDefined))

            getCall(url).then((response: any) => {
                if (response) {
                    resolve(response);
                } else {
                    resolve([]);
                }
            });
        }),

    /** Get Data Sources for DQA*/
    getDataSources: () =>
        new Promise<Array<DataSourceModel>>((resolve, reject) => {
            getCall(Constants.Api.Url.DQA_DATASOURCES_GET_RESPONSE).then(
                (response: Array<DataSourceModel>) => {
                    resolve(response);
                }
            );
        }),

    /** Save Service Level DQA Response */
    saveServiceLevelDQA: (request: ServiceLevelRequestModel) =>
        new Promise<boolean>((resolve, reject) => {
            postCall<ServiceLevelRequestModel>(
                Constants.Api.Url.DQA_SERVICE_LEVEL_SAVE_RESPONSE,
                request
            ).then((response) => {
                resolve(response);
                if (response) {
                    notificationService.sendMessage(
                        new BaseMessageModel(
                            i18next.t("Assessment.DataCollection.DataQualityAssessment.Message.SaveServiceLevelSuccessMessage"),
                            StatusCode.Created
                        )
                    );
                } else {
                    notificationService.sendMessage(
                        new BaseMessageModel(
                            i18next.t("Assessment.DataCollection.DataQualityAssessment.Message.SaveServiceLevelErrorMessage"),
                            StatusCode.PreConditionFailed
                        )
                    );
                }
            });
        }),

    /** Update Service Level DQA Response */
    updateServiceLevelDQA: (request: ServiceLevelRequestModel) =>
        new Promise((resolve, reject) => {
            putCall<ServiceLevelRequestModel>(
                Constants.Api.Url.DQA_SERVICE_LEVEL_UPDATE_RESPONSE,
                request
            )
                .then((response) => {
                    if (response == "") {
                        notificationService.sendMessage(
                            new BaseMessageModel(
                                i18next.t("Assessment.DataCollection.DataQualityAssessment.Message.UpdateServiceLevelSuccessMessage"),
                                StatusCode.Created
                            )
                        );
                    } else {
                        notificationService.sendMessage(
                            new BaseMessageModel(
                                i18next.t("Assessment.DataCollection.DataQualityAssessment.Message.UpdateServiceLevelErrorMessage"),
                                StatusCode.PreConditionFailed
                            )
                        );
                    }
                    resolve("");
                })
                .catch((error: ErrorModel) => {
                    if (error && error.statusCode !== StatusCode.PreConditionFailed) {
                        notificationService.sendMessage(
                            new BaseMessageModel(
                                i18next.t("Errors.SomethingWentWrong"),
                                StatusCode.InternalServerError
                            )
                        );
                    }
                    reject(true);
                });
        }),

    /** Get Service  Level DQA Response  by assessmentId     
    *  @param assessmentId A string value   
    */
    getServiceLevelData: (assessmentId: string) =>
        new Promise<ServiceLevelRequestModel>((resolve, reject) => {
            getCall(
                Constants.Api.Url.DQA_SERVICE_LEVEL_GET_RESPONSE.replace(
                    "{0}",
                    assessmentId
                )
            ).then((response: ServiceLevelRequestModel) => {
                if (response != null) {
                    resolve(response);
                } else {
                    resolve(ServiceLevelRequestModel.init(assessmentId));
                }
            });
        }),

    /** Finalize Service Level DQA Response */
    finalizeServiceLevel: (request: ServiceLevelFinalizeModel) =>
        new Promise((resolve, reject) => {
            putCall<ServiceLevelFinalizeModel>(
                Constants.Api.Url.DQA_SERVICE_LEVEL_FINALIZE_RESPONSE,
                request
            )
                .then((response) => {
                    if (response) {
                        notificationService.sendMessage(
                            new BaseMessageModel(
                                i18next.t("Assessment.DataCollection.DataQualityAssessment.Message.FinalizeServiceLevelSuccessMessage"),
                                StatusCode.Created
                            )
                        );
                    } else {
                        notificationService.sendMessage(
                            new BaseMessageModel(
                                i18next.t("Assessment.DataCollection.DataQualityAssessment.Message.FinalizeServiceLevelErrorMessage"),
                                StatusCode.PreConditionFailed
                            )
                        );
                    }
                    resolve(response);
                });
        }),

    /** Delete Service Level DQA Response */
    deleteServiceLevel: (request: ServiceLevelDeleteModel) =>
        new Promise((resolve, reject) => {
            deleteCall<ServiceLevelDeleteModel>(
                Constants.Api.Url.DQA_SERVICE_LEVEL_DELETE_RESPONSE,
                request
            )
                .then((response) => {
                    if (response) {
                        notificationService.sendMessage(
                            new BaseMessageModel(
                                i18next.t("Assessment.DataCollection.DataQualityAssessment.Message.DeleteServiceLevelSuccessMessage"),
                                StatusCode.Created
                            )
                        );
                    } else {
                        notificationService.sendMessage(
                            new BaseMessageModel(
                                i18next.t("Assessment.DataCollection.DataQualityAssessment.Message.DeleteServiceLevelErrorMessage"),
                                StatusCode.PreConditionFailed
                            )
                        );
                    }
                    resolve(response);
                });
        }),

    /** Generate SL DQA Template        
    *  @param serviceLevelId A string value   
    */
    generateTemplateServiceLevelDQA: (serviceLevelId: string) =>
        new Promise<boolean>((resolve, reject) => {
            const url = Constants.Api.Url.EXPORT_SL_DQA_EXCEL_FILE.replace(
                "{0}",
                serviceLevelId
            );
            getCall(url, "blob").then((response: any) => {
                resolve(response);
            })
                .catch((error: ErrorModel) => {
                    if (error && error.statusCode !== StatusCode.PreConditionFailed) {
                        notificationService.sendMessage(
                            new BaseMessageModel(
                                i18next.t("Errors.SomethingWentWrong"),
                                StatusCode.InternalServerError
                            )
                        );
                    }
                    reject(true);
                });
        }),

    /** Upload Service Level Template */
    uploadServiceLevelTemplateResponse: (request: FormData) =>
        new Promise<boolean>((resolve, reject) => {
            postCall<FormData>(
                Constants.Api.Url.UPLOAD_SL_DQA_EXCEL_FILE,
                request
            )
                .then((response: any) => {
                    if (response) {
                        notificationService.sendMessage(
                            new BaseMessageModel(
                                i18next.t("Assessment.DataCollection.DataQualityAssessment.Message.ServiceLevelUploadTemplateSuccessMessage"),
                                StatusCode.Created
                            )
                        );
                    } else {
                        notificationService.sendMessage(
                            new BaseMessageModel(
                                i18next.t("Assessment.DataCollection.DataQualityAssessment.Message.ServiceLevelUploadTemplateErrorMessage"),
                                StatusCode.PreConditionFailed
                            )
                        );
                    }
                    resolve(response);
                })
                .catch((error: ErrorModel) => {
                    if (error && error.statusCode !== StatusCode.PreConditionFailed && error.statusCode !== StatusCode.InternalServerError) {
                        notificationService.sendMessage(
                            new BaseMessageModel(
                                i18next.t("Errors.SomethingWentWrong"),
                                StatusCode.InternalServerError
                            )
                        );
                    }
                    reject(true);
                });
        }),

    /** Get Service  Level Summary DQA Response  by assessmentId       
 *  @param assessmentId A string value   
 */
    getServiceLevelSummary: (assessmentId: string) =>
        new Promise<ServiceLevelSummaryModel>((resolve, reject) => {
            getCall(
                Constants.Api.Url.DQA_SERVICE_LEVEL_SUMMARY_GET_RESPONSE.replace(
                    "{0}",
                    assessmentId
                )
            ).then((response: ServiceLevelSummaryModel) => {
                if (response != null) {
                    resolve(response);
                } else {
                    resolve(ServiceLevelSummaryModel.init());
                }
            });
        }),

    /** Update the reasons for the observed data quality results for Service Level DQA */
    updateServiceLevelObservedDataQualityReason: (request: ServiceLevelObservedDataQualityReasonModel) =>
        new Promise((resolve, reject) => {
            patchCall<ServiceLevelObservedDataQualityReasonModel>(
                Constants.Api.Url.DQA_SERVICE_LEVEL_UPDATE_OBSERVED_DATA_QUALITY_REASON_RESPONSE,
                request
            )
                .then((response) => {
                    if (response) {
                        notificationService.sendMessage(
                            new BaseMessageModel(
                                i18next.t("Assessment.DataCollection.DataQualityAssessment.Message.UpdateObservedDataQualitySuccessMessage"),
                                StatusCode.Created
                            )
                        );
                    } else {
                        notificationService.sendMessage(
                            new BaseMessageModel(
                                i18next.t("Assessment.DataCollection.DataQualityAssessment.Message.UpdateObservedDataQualityErrorMessage"),
                                StatusCode.PreConditionFailed
                            )
                        );
                    }
                    resolve(response);
                })
                .catch((error: ErrorModel) => {
                    if (error && error.statusCode !== StatusCode.PreConditionFailed) {
                        notificationService.sendMessage(
                            new BaseMessageModel(
                                i18next.t("Errors.SomethingWentWrong"),
                                StatusCode.InternalServerError
                            )
                        );
                    }
                    reject(true);
                });
        }),

    /** Finalize the reasons for the observed data quality results for Service Level DQA */
    finalizeObservedDataQualityResultReason: (request: FinalizeObservedDataQualityReasonModel) =>
        new Promise((resolve, reject) => {
            patchCall<FinalizeObservedDataQualityReasonModel>(
                Constants.Api.Url.DQA_SERVICE_LEVEL_FINALIZE_OBSERVED_DATA_QUALITY_REASON_RESPONSE,
                request
            )
                .then((response) => {
                    if (response) {
                        notificationService.sendMessage(
                            new BaseMessageModel(
                                i18next.t("Assessment.DataCollection.DataQualityAssessment.Message.FinalizeObservedDataQualitySuccessMessage"),
                                StatusCode.Created
                            )
                        );
                    } else {
                        notificationService.sendMessage(
                            new BaseMessageModel(
                                i18next.t("Assessment.DataCollection.DataQualityAssessment.Message.FinalizeObservedDataQualityErrorMessage"),
                                StatusCode.PreConditionFailed
                            )
                        );
                    }
                    resolve(response);
                })
                .catch((error: ErrorModel) => {
                    if (error && error.statusCode !== StatusCode.PreConditionFailed) {
                        notificationService.sendMessage(
                            new BaseMessageModel(
                                i18next.t("Errors.SomethingWentWrong"),
                                StatusCode.InternalServerError
                            )
                        );
                    }
                    reject(true);
                });
        }),

    /** Get a response for indicators report for DL DQA
    *  @param indicator A integer value for the indicator report type
    *  @param assessmentId A boolean value
    */
    getDeskLevelReport: (
        indicatorReportType: number, assessmentId: string
    ) =>
        new Promise<IndicatorReportModel>((resolve, reject) => {
            const url =
                Constants.Api.Url.DQA_DESK_LEVEL_REPORT.replace(
                    "{0}", indicatorReportType.toString()
                ).replace("{1}", assessmentId)

            getCall(url).then((response: any) => {
                if (response) {
                    resolve(response);
                } else {
                    resolve(IndicatorReportModel.init());
                }
            });
        }),

    /** Save Desk Level DQA Response */
    saveDeskLevelDQA: (request: DLDQARequestModel) =>
        new Promise<boolean>((resolve, reject) => {
            postCall<DLDQARequestModel>(
                Constants.Api.Url.DQA_DESK_LEVEL_SAVE_RESPONSE,
                request
            ).then((response) => {
                resolve(response);
                if (response) {
                    notificationService.sendMessage(
                        new BaseMessageModel(
                            i18next.t("Assessment.DataCollection.DataQualityAssessment.Message.SaveDeskLevelSuccessMessage"),
                            StatusCode.Created
                        )
                    );
                } else {
                    notificationService.sendMessage(
                        new BaseMessageModel(
                            i18next.t("Assessment.DataCollection.DataQualityAssessment.Message.SaveDeskLevelErrorMessage"),
                            StatusCode.PreConditionFailed
                        )
                    );
                }
            });
        }),

    /** Update Desk Level DQA Response */
    updateDeskLevelDQA: (request: DLDQARequestModel) =>
        new Promise((resolve, reject) => {
            postCall<DLDQARequestModel>(
                Constants.Api.Url.DQA_DESK_LEVEL_SAVE_RESPONSE,
                request
            )
                .then((response) => {
                    if (response) {
                        notificationService.sendMessage(
                            new BaseMessageModel(
                                i18next.t("Assessment.DataCollection.DataQualityAssessment.Message.UpdateDeskLevelSuccessMessage"),
                                StatusCode.Created
                            )
                        );
                    } else {
                        notificationService.sendMessage(
                            new BaseMessageModel(
                                i18next.t("Assessment.DataCollection.DataQualityAssessment.Message.UpdateDeskLevelErrorMessage"),
                                StatusCode.PreConditionFailed
                            )
                        );
                    }
                    resolve("");
                })
                .catch((error: ErrorModel) => {
                    if (error && error.statusCode !== StatusCode.PreConditionFailed) {
                        notificationService.sendMessage(
                            new BaseMessageModel(
                                i18next.t("Errors.SomethingWentWrong"),
                                StatusCode.InternalServerError
                            )
                        );
                    }
                    reject(true);
                });
        }),

    /** Get Desk Level DQA Response by assessmentId     
    *  @param assessmentId A string value   
    */
    getDeskLevelDQA: (assessmentId: string) =>
        new Promise<DeskLevelRequestModel>((resolve, reject) => {
            getCall(
                Constants.Api.Url.DQA_DESK_LEVEL_GET_RESPONSE.replace(
                    "{0}",
                    assessmentId
                ), "json", false
            ).then((response: DeskLevelRequestModel) => {
                if (response != null) {
                    resolve(response);
                } else {
                    resolve(DeskLevelRequestModel.init(assessmentId));
                }
            }).catch((error: ErrorModel) => {
                if (error && error.statusCode !== StatusCode.PreConditionFailed) {
                    notificationService.sendMessage(
                        new BaseMessageModel(
                            i18next.t("Errors.SomethingWentWrong"),
                            StatusCode.InternalServerError
                        )
                    );
                }
                reject(true);
            });
        }),

    /** Finalize Desk Level DQA Response */
    finalizeDeskLevel: (request: DeskLevelFinalizeModel) =>
        new Promise((resolve, reject) => {
            postCall<DeskLevelFinalizeModel>(
                Constants.Api.Url.DQA_DESK_LEVEL_FINALIZE_RESPONSE,
                request
            )
                .then((response) => {
                    if (response) {
                        notificationService.sendMessage(
                            new BaseMessageModel(
                                i18next.t("Assessment.DataCollection.DataQualityAssessment.Message.FinalizeDeskLevelSuccessMessage"),
                                StatusCode.Created
                            )
                        );
                    } else {
                        notificationService.sendMessage(
                            new BaseMessageModel(
                                i18next.t("Assessment.DataCollection.DataQualityAssessment.Message.FinalizeDeskLevelErrorMessage"),
                                StatusCode.PreConditionFailed
                            )
                        );
                    }
                    resolve(response);
                });
        }),

    /** Generate DL DQA Template        
    *  @param assessmentId A string value
    */
    generateTemplateDeskLevelDQA: (assessmentId: string) =>
        new Promise<boolean>((resolve, reject) => {
            const url = Constants.Api.Url.GENERATE_DL_DQA_EXCEL_FILE.replace(
                "{0}",
                assessmentId
            );
            getCall(url, "blob").then((response: any) => {
                resolve(response);
            })
                .catch((error: ErrorModel) => {
                    if (error && error.statusCode !== StatusCode.PreConditionFailed) {
                        notificationService.sendMessage(
                            new BaseMessageModel(
                                i18next.t("Errors.SomethingWentWrong"),
                                StatusCode.InternalServerError
                            )
                        );
                    }
                    reject(true);
                });
        }),

    /** Upload Desk Level Template */
    uploadDeskLevelTemplateResponse: (request: FormData) =>
        new Promise<boolean>((resolve, reject) => {
            postCall<FormData>(
                Constants.Api.Url.UPLOAD_DL_DQA_EXCEL_FILE,
                request
            )
                .then((response: any) => {
                    if (response) {
                        notificationService.sendMessage(
                            new BaseMessageModel(
                                i18next.t("Assessment.DataCollection.DataQualityAssessment.Message.DeskLevelUploadTemplateSuccessMessage"),
                                StatusCode.Created
                            )
                        );
                    } else {
                        notificationService.sendMessage(
                            new BaseMessageModel(
                                i18next.t("Assessment.DataCollection.DataQualityAssessment.Message.DeskLevelUploadTemplateErrorMessage"),
                                StatusCode.PreConditionFailed
                            )
                        );
                    }
                    resolve(response);
                })
                .catch((error: ErrorModel) => {
                    if (error && error.statusCode !== StatusCode.PreConditionFailed && error.statusCode !== StatusCode.InternalServerError) {
                        notificationService.sendMessage(
                            new BaseMessageModel(
                                i18next.t("Errors.SomethingWentWrong"),
                                StatusCode.InternalServerError
                            )
                        );
                    }
                    reject(true);
                });
        }),

    /** Get Indicators list for desk level data quality assessment by assessment id*/
    getDeskLevelIndicators: () =>
        new Promise<Array<IndicatorModel>>((resolve, reject) => {
            getCall(Constants.Api.Url.DQA_DESK_LEVEL_INDICATORS).then((indicators) => {
                if (indicators) {
                    resolve(indicators);
                }
            });
        }),

    /** Download report for indicator response 
    * @param assessmentId A string value
    */
    downloadReport: (assessmentId: string) =>
        new Promise<boolean>((resolve, reject) => {
            const url = Constants.Api.Url.DQA_DESK_LEVEL_REPORT_EXPORT.replace(
                "{0}",
                assessmentId
            );
            getCall(url, "blob").then((response: any) => {
                resolve(response);
            })
                .catch((error: ErrorModel) => {
                    if (error) {
                        notificationService.sendMessage(
                            new BaseMessageModel(
                                i18next.t("Errors.SomethingWentWrong"),
                                StatusCode.PreConditionFailed
                            )
                        );
                    }
                    reject(true);
                });
        }),

    /** Elimination DQA export excel */
    exportEliminationExcelTemplate: () =>
        new Promise<boolean>((resolve, reject) => {
            const url = Constants.Api.Url.ELIMINATION_DQA_EXPORT;
            getCall(url, "blob").then((response: any) => {
                resolve(response);
            })
                .catch((error: ErrorModel) => {
                    if (error && error.statusCode !== StatusCode.PreConditionFailed) {
                        notificationService.sendMessage(
                            new BaseMessageModel(
                                i18next.t("Errors.SomethingWentWrong"),
                                StatusCode.InternalServerError
                            )
                        );
                    }
                    reject(true);
                });
        }),

    /** Save summary result for Elimination DQA */
    saveEliminationDQASummary: (request: SummaryModel) =>
        new Promise<boolean>((resolve, reject) => {
            postCall<SummaryModel>(
                Constants.Api.Url.SAVE_ELIMINATION_DQA_SUMMARY,
                request
            ).then((response) => {
                resolve(response);
                if (response) {
                    notificationService.sendMessage(
                        new BaseMessageModel(
                            i18next.t("Assessment.DataCollection.DataQualityAssessment.Message.SaveEliminationDQASuccessMessage"),
                            StatusCode.Created
                        )
                    );
                } else {
                    notificationService.sendMessage(
                        new BaseMessageModel(
                            i18next.t("Assessment.DataCollection.DataQualityAssessment.Message.SaveEliminationDQAErrorMessage"),
                            StatusCode.PreConditionFailed
                        )
                    );
                }
            });
        }),

    /** Get Elimination DQA Summary Response by assessmentId and year
    *  @param assessmentId A string value
    */
    getEliminationDQASummary: (assessmentId: string) =>
        new Promise<EliminationSummaryModel>((resolve, reject) => {
            getCall(
                Constants.Api.Url.ELIMINATION_DQA_GET_SUMMARY_RESPONSE.replace(
                    "{0}",
                    assessmentId
                )
            ).then((response: EliminationSummaryModel) => {
                if (response != null) {
                    resolve(response);
                } else {
                    resolve(EliminationSummaryModel.init(assessmentId));
                }
            });
        }),

    /** Finalize Elimination DQA Response */
    finalizeEliminationDQASummary: (request: SummaryModel) =>
        new Promise((resolve, reject) => {
            postCall<SummaryModel>(
                Constants.Api.Url.ELIMINATION_DQA_FINALIZE_RESPONSE,
                request
            )
                .then((response) => {
                    if (response) {
                        notificationService.sendMessage(
                            new BaseMessageModel(
                                i18next.t("Assessment.DataCollection.DataQualityAssessment.Message.FinalizeEliminationDQASuccessMessage"),
                                StatusCode.Created
                            )
                        );
                    } else {
                        notificationService.sendMessage(
                            new BaseMessageModel(
                                i18next.t("Assessment.DataCollection.DataQualityAssessment.Message.FinalizeEliminationDQAErrorMessage"),
                                StatusCode.PreConditionFailed
                            )
                        );
                    }
                    resolve(response);
                });
        }),

    /** Generate DL DQA Template        
    *  @param assessmentId A string value
    */
    generateEliminationDQATemplate: (assessmentId: string) =>
        new Promise<boolean>((resolve, reject) => {
            const url = Constants.Api.Url.GENERATE_ELIMINATION_DQA_EXCEL_FILE.replace(
                "{0}",
                assessmentId
            );
            getCall(url, "blob").then((response: any) => {
                resolve(response);
            })
                .catch((error: ErrorModel) => {
                    if (error && error.statusCode !== StatusCode.PreConditionFailed) {
                        notificationService.sendMessage(
                            new BaseMessageModel(
                                i18next.t("Errors.SomethingWentWrong"),
                                StatusCode.InternalServerError
                            )
                        );
                    }
                    reject(true);
                });
        }),

    /** Get Indicators list for desk level data quality assessment by assessment id*/
    getDeskLevelSummaryYears: (assessmentId: string) =>
        new Promise<Array<string>>((resolve, reject) => {
            getCall(
                Constants.Api.Url.DQA_DESK_LEVEL_SUMMARY_YEARS.replace("{0}", assessmentId.toString()))
                .then((response) => {
                    if (response) {
                        resolve(response);
                    }
                });
        }),


    /** Get Sumary result list for desk level data quality assessment by assessment id*/
    getDeskLevelSummaryResult: (assessmentId: string, year: string) =>
        new Promise<Array<SummaryDataModel>>
            ((resolve, reject) => {
                getCall(Constants.Api.Url.DQA_DESK_LEVEL_NATIONAL_LEVEL_SUMMARY
                    .replace("{0}", assessmentId.toString())
                    .replace("{1}", year.toString())
                )
                    .then((response) => {
                        if (response) {
                            resolve(response);
                        } else {
                            resolve([SummaryDataModel.init(assessmentId)]);
                        }
                    });
            }),

    /** Saves the data analysis summary result by calling an API
    * request: an object of type DeskLevelSummaryReportModel
    */
    saveSummaryResult: (request: DeskLevelSummaryReportModel) =>
        new Promise<boolean>((resolve, reject) => {
            postCall<DeskLevelSummaryReportModel>(
                Constants.Api.Url.DQA_DESK_LEVEL_SAVE_NATIONAL_LEVEL_SUMMARY,
                request
            ).then((response) => {
                resolve(response);
                if (response) {
                    notificationService.sendMessage(
                        new BaseMessageModel(
                            i18next.t("Assessment.DataAnalysis.SaveDataAnalysisNationalSummaryResult"),
                            StatusCode.Created
                        )
                    );
                } else {
                    notificationService.sendMessage(
                        new BaseMessageModel(
                            i18next.t("Errors.SomethingWentWrong"),
                            StatusCode.PreConditionFailed
                        )
                    );
                }
            })
                .catch((error) => reject(error));
        }),

    /** Finalize survey questions response */
    finalizeSummaryResult: (request: DeskLevelSummaryReportModel) =>
        new Promise((resolve, reject) => {
            postCall<DeskLevelSummaryReportModel>(
                Constants.Api.Url.DQA_DESK_LEVEL_FINALIZE_NATIONAL_LEVEL_SUMMARY,
                request
            )
                .then((response) => {
                    if (response) {
                        notificationService.sendMessage(
                            new BaseMessageModel(
                                i18next.t("Assessment.DataAnalysis.FinalizedDataAnalysisNationalSummaryResult"),
                                StatusCode.Created
                            )
                        );
                    } else {
                        notificationService.sendMessage(
                            new BaseMessageModel(
                                i18next.t("Assessment.DataAnalysis.FinalizedDataAnalysisNationalSummaryResult"),
                                StatusCode.PreConditionFailed
                            )
                        );
                    }
                    resolve(response);
                })
                .catch((error) => reject(error));
        }),

    /** Generate DL DQA Template        
*  @param assessmentId A string value
*/
    generateDeskLevelDQATemplate: (assessmentId: string) =>
        new Promise<boolean>((resolve, reject) => {
            const url = Constants.Api.Url.GENERATE_DESK_LEVEL_DQA_EXCEL_FILE.replace(
                "{0}",
                assessmentId
            );
            getCall(url, "blob").then((response: any) => {
                resolve(response);
            })
                .catch((error: ErrorModel) => {
                    if (error && error.statusCode !== StatusCode.PreConditionFailed) {
                        notificationService.sendMessage(
                            new BaseMessageModel(
                                i18next.t("Errors.SomethingWentWrong"),
                                StatusCode.InternalServerError
                            )
                        );
                    }
                    reject(true);
                });
        }),

};

