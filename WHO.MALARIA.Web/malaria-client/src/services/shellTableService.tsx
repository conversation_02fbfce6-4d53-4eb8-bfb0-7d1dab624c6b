﻿import i18next from "i18next";
import { Constants } from "../models/Constants";
import { StatusCode } from "../models/Enums";
import { BaseMessageModel, ErrorModel } from "../models/ErrorModel";
import { ObjectivesSubObjectivesIndicatorsModel, QuestionModel, RespondentAndHealthFacilityType } from "../models/ShellTable/ShellTableModel";
import { getCall, postCall } from "./api";
import { notificationService } from "./notificationService";

export const shellTableService = {
    /** Upload shell table for questions */
    uploadFile: (request: FormData) =>
        new Promise<boolean>((resolve, reject) => {
            postCall<FormData>(
                Constants.Api.Url.SHELL_TABLE_UPLOAD_FILE,
                request
            )
                .then((response: any) => {
                    if (response) {
                        notificationService.sendMessage(
                            new BaseMessageModel(
                                i18next.t("Assessment.DataCollection.ShellTable.Message.ShellTableUploadTemplateSuccessMessage"),
                                StatusCode.Created
                            )
                        );
                    } else {
                        notificationService.sendMessage(
                            new BaseMessageModel(
                                i18next.t("Assessment.DataCollection.ShellTable.Message.ShellTableUploadTemplateErrorMessage"),
                                StatusCode.PreConditionFailed
                            )
                        );
                    }
                    resolve(response);
                })
                .catch((error: ErrorModel) => {
                    if (error && error.statusCode !== StatusCode.PreConditionFailed) {
                        notificationService.sendMessage(
                            new BaseMessageModel(
                                i18next.t(error.text ? error.text : "Errors.SomethingWentWrong"),
                                StatusCode.InternalServerError
                            )
                        );
                    }
                    reject(true);
                });
        }),

    /** Gets respondent types and health facility types selected by specific assessmentId 
* * @param assessmentId A string value
*/
    getRespondentTypesHealthFacilityTypesByAssessmentId: (assessmentId: string) =>
        new Promise<RespondentAndHealthFacilityType>((resolve, reject) => {
            // call an api
            const url = Constants.Api.Url.SHELL_TABLE_RESPONDENT_TYPES_HEALTH_FACILITY_TYPES.replace(
                "{0}",
                assessmentId
            );

            getCall(url).then((response: any) => {
                if (response) {
                    resolve(response);
                } else {
                    resolve(RespondentAndHealthFacilityType.init());
                }
            });
        }),

    /** Gets objectives, sub objectives, indicators selected by specific assessmentId 
* * @param assessmentId A string value
*/
    getObjectiveSubObjectivesIndicatorsAsync: (assessmentId: string) =>
        new Promise<ObjectivesSubObjectivesIndicatorsModel>((resolve, reject) => {
            // call an api
            const url = Constants.Api.Url.SHELL_TABLE_OBJECTIVES_SUB_OBJECTIVES_INDICATORS.replace(
                "{0}",
                assessmentId
            );

            getCall(url).then((response: any) => {
                if (response) {
                    resolve(response);
                } else {
                    resolve(ObjectivesSubObjectivesIndicatorsModel.init());
                }
            });
        }),

    /** Gets questions selected by specific assessmentId, respondent type, geo graphical level, health facility type
* * @param assessmentId A string value
* * @param respondentType A number value
* * @param geoGraphicLevel A number value
* * @param healthFacilityType A number value
*/
    getQuestionsAsync: (assessmentId: string, respondentType: number, geoGraphicLevel: number, healthFacilityType: number) =>
        new Promise<Array<QuestionModel>>((resolve, reject) => {
            // call an api
            const url =
                Constants.Api.Url.SHELL_TABLE_QUESTIONS.replace(
                    "{0}",
                    assessmentId
                )
                    .replace("{1}", respondentType.toString())
                    .replace("{2}", geoGraphicLevel.toString())
                    .replace("{3}", healthFacilityType.toString());

            getCall(url).then((response: any) => {
                if (response) {
                    resolve(response);
                } else {
                    resolve([]);
                }
            });
        }),

    /** export questions selected by specific assessmentId, respondent type, health facility type
  * * @param assessmentId A string value
  * * @param respondentType A number value
  * * @param healthFacilityType A number value
  */
    exportQuestions: (assessmentId: string, respondentType: number, healthFacilityType: number) =>
        new Promise<boolean>((resolve, reject) => {
            const url = `${Constants.Api.baseUrl}/${Constants.Api.Url.SHELL_TABLE_EXPORT.replace(
                "{0}",
                assessmentId
            )
                .replace("{1}", respondentType.toString())
                .replace("{2}", healthFacilityType.toString())}`;

            window.open(url);
        }),
}