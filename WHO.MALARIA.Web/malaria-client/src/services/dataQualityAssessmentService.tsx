﻿import { VariableModel } from "../models/VariableModel";
import { GetIndicatorModel } from "../models/IndicatorModel";
import { IndicatorReportModel } from "../models/DataAnalysis/DeskReview/IndicatorReportDetailsModel";
import { DQAVariableApplicableFor, MetNotMetStatus } from "../models/Enums";
import { GetCountrySpecificSubObjectiveModel, SubObjectivePercentageModel } from "../models/DashboardModel";
import SubObjectiveModel from "../models/SubObjectiveModel";
import { DashboardReportModel } from "../models/GlobalDashboardModel"
import { TabModel } from "../models/TabModel";
import { StrategyModel } from "../models/StrategyModel";

export const dataQualityAssessmentService = {

    // TODO: Remove this file once DL DQA indicators are binded through the API
    /** Get All Indicators  */
    getIndicators: (): Array<GetIndicatorModel> => {
        return [
            new GetIndicatorModel("1", "Completeness of reporting", "1.2.1", "Completeness of reporting", 1, 1, "", 1),
            new GetIndicatorModel("2", "Timeliness of reporting", "1.2.3", "Timeliness of reporting", 1, 1, "", 1),
            new GetIndicatorModel("3", "Completeness variables", "1.2.7", "Completeness variables", 1, 1, "", 1),
            new GetIndicatorModel("4", "Consistency btw variables", "1.2.8", "Consistency btw variables", 1, 1, "", 1),
            new GetIndicatorModel("5", "Consistency over time", "1.2.9", "Consistency over time", 1, 1, "", 1),
            new GetIndicatorModel("6", "Concordance", "1.2.10", "Concordance", 1, 1, "", 1),
        ];
    },

    /** Get all completeness of core variables within registers for DQA */
    getRegisterCoreVariables: (): Array<VariableModel> => {
        return [
            new VariableModel("1", "Sex", 1, false, DQAVariableApplicableFor.Both),
            new VariableModel("2", "Age", 1, false, DQAVariableApplicableFor.Both),
            new VariableModel("3", "Diagnosis", 1, false, DQAVariableApplicableFor.Both),
        ];
    },

    /** View report for the priority indicators */
    viewReportForIndicators: (): DashboardReportModel =>
        new DashboardReportModel(1, [{
            "objectives": [
                {
                    key: "Performance",
                    value: 12
                },
                {
                    key: "Context and Infrastructure",
                    value: 15
                },
                {
                    key: "Technical and Process",
                    value: 15
                },
                {
                    key: "Behaviour",
                    value: 9
                }
            ],
        },
        {
            "subObjectives": [
                {
                    key: "SURVEILLANCE SYSTEM COVERAGE",
                    value: 7
                },
                {
                    key: "DATA USE",
                    value: 5
                },
                {
                    key: "SURVEILLANCE SECTORS & STRATEGIES",
                    value: 4
                },
                {
                    key: "INFORMATION SYSTEMS",
                    value: 5
                },
                {
                    key: "SURVEILLANCE GUIDELINES & SOPs",
                    value: 2
                },
                {
                    key: "RESOURCES (STAFF, EQUIPMENT, INFRASTRUCTURE)",
                    value: 3
                },
                {
                    key: "FINANCIAL SUPPORT",
                    value: 1
                },
                {
                    key: "CASE MANAGEMENT",
                    value: 2
                },
                {
                    key: "RECORDING(assessed on forms/tools recording information at the health facility level)",
                    value: 3
                },
                {
                    key: "REPORTING(Transmission of data from the health facility to the national level)",
                    value: 4
                },
                {
                    key: "ANALYSIS",
                    value: 2
                },
                {
                    key: "DATA QUALITY ASSURANCE",
                    value: 3
                },
                {
                    key: "DATA ACCESS",
                    value: 1
                },
                {
                    key: "GOVERNANCE",
                    value: 3
                },
                {
                    key: "PROMOTION OF AN INFORMATION CULTURE",
                    value: 2
                },
                {
                    key: "SUPERVISION",
                    value: 1
                },
                {
                    key: "SURVEILLANCE STAFF PROFICIENCY",
                    value: 3
                }
            ],
        },
        {
            "rows": [{
                Region: "AFRO",
                Country: "India",
                YearOfAssessment: "2019",
                Indicator_1_1_2: { deskreview: "85", isSurvey: false, isSurveyValue: "65" },
                Indicator_1_1_3: { deskreview: "21", isSurvey: true, isSurveyValue: "65" },
                Indicator_1_1_4: { deskreview: "65", isSurvey: true, isSurveyValue: "65" },
                Indicator_1_1_5: { deskreview: "11", isSurvey: true, isSurveyValue: "65" },
                Indicator_1_1_7: { deskreview: "45", isSurvey: false, isSurveyValue: "65" },
                Indicator_1_1_8: { deskreview: "78", isSurvey: true, isSurveyValue: "65" },
                Indicator_1_1_9: { deskreview: "86", isSurvey: true, isSurveyValue: "65" },
                Indicator_1_3_1: { deskreview: "65", isSurvey: true, isSurveyValue: "65" },
                Indicator_1_3_2: { deskreview: "33", isSurvey: true, isSurveyValue: "65" },
                Indicator_1_3_3: { deskreview: "11", isSurvey: false, isSurveyValue: "65" },
                Indicator_1_3_4: { deskreview: "65", isSurvey: true, isSurveyValue: "65" },
                Indicator_1_3_7: { deskreview: "98", isSurvey: true, isSurveyValue: "65" },
                Indicator_2_1_1: { deskreview: "45", isSurvey: true, isSurveyValue: "65" },
                Indicator_2_1_2: { deskreview: "65", isSurvey: true, isSurveyValue: "65" },
                Indicator_2_1_3: { deskreview: "33", isSurvey: true, isSurveyValue: "65" },
                Indicator_2_1_4: { deskreview: "11", isSurvey: true, isSurveyValue: "65" },
                Indicator_2_2_1: { deskreview: "65", isSurvey: true, isSurveyValue: "65" },
                Indicator_2_2_2: { deskreview: "98", isSurvey: false, isSurveyValue: "65" },
                Indicator_2_2_4: { deskreview: "45", isSurvey: true, isSurveyValue: "65" },
                Indicator_2_2_5: { deskreview: "21", isSurvey: true, isSurveyValue: "65" },
                Indicator_2_2_6: { deskreview: "65", isSurvey: true, isSurveyValue: "65" },
                Indicator_2_3_1: { deskreview: "98", isSurvey: true, isSurveyValue: "65" },
                Indicator_2_3_2: { deskreview: "45", isSurvey: true, isSurveyValue: "65" },
                Indicator_2_4_1: { deskreview: "75", isSurvey: true, isSurveyValue: "65" },
                Indicator_2_4_2: { deskreview: "65", isSurvey: true, isSurveyValue: "65" },
                Indicator_2_4_4: { deskreview: "85", isSurvey: true, isSurveyValue: "65" },
                Indicator_2_5_1: { deskreview: "22", isSurvey: true, isSurveyValue: "65" },
                Indicator_3_1_2: { deskreview: "45", isSurvey: true, isSurveyValue: "65" },
                Indicator_3_1_3: { deskreview: "75", isSurvey: true, isSurveyValue: "65" },
                Indicator_3_2_1: { deskreview: "85", isSurvey: true, isSurveyValue: "65" },
                Indicator_3_2_2: { deskreview: "91", isSurvey: true, isSurveyValue: "65" },
                Indicator_3_2_3: { deskreview: "77", isSurvey: true, isSurveyValue: "65" },

                Indicator_3_3_1: { deskreview: "7", isSurvey: true, isSurveyValue: "65" },
                Indicator_3_3_2: { deskreview: "17", isSurvey: true, isSurveyValue: "65" },
                Indicator_3_3_3: { deskreview: "27", isSurvey: true, isSurveyValue: "65" },
                Indicator_3_3_4: { deskreview: "37", isSurvey: true, isSurveyValue: "65" },
                Indicator_3_4_1: { deskreview: "47", isSurvey: true, isSurveyValue: "65" },
                Indicator_3_4_2: { deskreview: "57", isSurvey: true, isSurveyValue: "65" },
                Indicator_3_5_1: { deskreview: "67", isSurvey: true, isSurveyValue: "65" },
                Indicator_3_5_2: { deskreview: "77", isSurvey: true, isSurveyValue: "65" },
                Indicator_3_5_3: { deskreview: "87", isSurvey: true, isSurveyValue: "65" },
                Indicator_3_6_1: { deskreview: "97", isSurvey: true, isSurveyValue: "65" },
                Indicator_4_1_1: { deskreview: "17", isSurvey: true, isSurveyValue: "65" },
                Indicator_4_1_2: { deskreview: "27", isSurvey: true, isSurveyValue: "65" },
                Indicator_4_1_3: { deskreview: "37", isSurvey: true, isSurveyValue: "65" },
                Indicator_4_2_1: { deskreview: "47", isSurvey: true, isSurveyValue: "65" },
                Indicator_4_2_2: { deskreview: "57", isSurvey: true, isSurveyValue: "65" },
                Indicator_4_3_1: { deskreview: "67", isSurvey: true, isSurveyValue: "65" },
                Indicator_4_4_1: { deskreview: "77", isSurvey: true, isSurveyValue: "65" },
                Indicator_4_4_2: { deskreview: "87", isSurvey: true, isSurveyValue: "65" },
                Indicator_4_4_3: { deskreview: "97", isSurvey: true, isSurveyValue: "65" },
            },
            {
                Region: "AFRO",
                Country: "Nepal",
                YearOfAssessment: "2018",
                Indicator_1_1_2: { deskreview: "85", isSurvey: true, isSurveyValue: "65" },
                Indicator_1_1_3: { deskreview: "21", isSurvey: true, isSurveyValue: "65" },
                Indicator_1_1_4: { deskreview: "65", isSurvey: true, isSurveyValue: "65" },
                Indicator_1_1_5: { deskreview: "11", isSurvey: true, isSurveyValue: "65" },
                Indicator_1_1_7: { deskreview: "45", isSurvey: false, isSurveyValue: "65" },
                Indicator_1_1_8: { deskreview: "78", isSurvey: true, isSurveyValue: "65" },
                Indicator_1_1_9: { deskreview: "86", isSurvey: true, isSurveyValue: "65" },
                Indicator_1_3_1: { deskreview: "65", isSurvey: true, isSurveyValue: "65" },
                Indicator_1_3_2: { deskreview: "33", isSurvey: true, isSurveyValue: "65" },
                Indicator_1_3_3: { deskreview: "11", isSurvey: false, isSurveyValue: "65" },
                Indicator_1_3_4: { deskreview: "65", isSurvey: true, isSurveyValue: "65" },
                Indicator_1_3_7: { deskreview: "98", isSurvey: true, isSurveyValue: "65" },
                Indicator_2_1_1: { deskreview: "45", isSurvey: true, isSurveyValue: "65" },
                Indicator_2_1_2: { deskreview: "65", isSurvey: true, isSurveyValue: "65" },
                Indicator_2_1_3: { deskreview: "33", isSurvey: true, isSurveyValue: "65" },
                Indicator_2_1_4: { deskreview: "11", isSurvey: true, isSurveyValue: "65" },
                Indicator_2_2_1: { deskreview: "65", isSurvey: true, isSurveyValue: "65" },
                Indicator_2_2_2: { deskreview: "98", isSurvey: false, isSurveyValue: "65" },
                Indicator_2_2_4: { deskreview: "45", isSurvey: true, isSurveyValue: "65" },
                Indicator_2_2_5: { deskreview: "21", isSurvey: true, isSurveyValue: "65" },
                Indicator_2_2_6: { deskreview: "65", isSurvey: true, isSurveyValue: "65" },
                Indicator_2_3_1: { deskreview: "98", isSurvey: true, isSurveyValue: "65" },
                Indicator_2_3_2: { deskreview: "45", isSurvey: true, isSurveyValue: "65" },
                Indicator_2_4_1: { deskreview: "75", isSurvey: true, isSurveyValue: "65" },
                Indicator_2_4_2: { deskreview: "65", isSurvey: true, isSurveyValue: "65" },
                Indicator_2_4_4: { deskreview: "85", isSurvey: true, isSurveyValue: "65" },
                Indicator_2_5_1: { deskreview: "22", isSurvey: true, isSurveyValue: "65" },
                Indicator_3_1_2: { deskreview: "45", isSurvey: true, isSurveyValue: "65" },
                Indicator_3_1_3: { deskreview: "75", isSurvey: true, isSurveyValue: "65" },
                Indicator_3_2_1: { deskreview: "85", isSurvey: true, isSurveyValue: "65" },
                Indicator_3_2_2: { deskreview: "91", isSurvey: true, isSurveyValue: "65" },
                Indicator_3_2_3: { deskreview: "77", isSurvey: true, isSurveyValue: "65" },
                Indicator_3_3_1: { deskreview: "7", isSurvey: true, isSurveyValue: "65" },
                Indicator_3_3_2: { deskreview: "17", isSurvey: true, isSurveyValue: "65" },
                Indicator_3_3_3: { deskreview: "27", isSurvey: true, isSurveyValue: "65" },
                Indicator_3_3_4: { deskreview: "37", isSurvey: true, isSurveyValue: "65" },
                Indicator_3_4_1: { deskreview: "47", isSurvey: true, isSurveyValue: "65" },
                Indicator_3_4_2: { deskreview: "57", isSurvey: true, isSurveyValue: "65" },
                Indicator_3_5_1: { deskreview: "67", isSurvey: true, isSurveyValue: "65" },
                Indicator_3_5_2: { deskreview: "77", isSurvey: true, isSurveyValue: "65" },
                Indicator_3_5_3: { deskreview: "87", isSurvey: true, isSurveyValue: "65" },
                Indicator_3_6_1: { deskreview: "97", isSurvey: true, isSurveyValue: "65" },
                Indicator_4_1_1: { deskreview: "17", isSurvey: true, isSurveyValue: "65" },
                Indicator_4_1_2: { deskreview: "27", isSurvey: true, isSurveyValue: "65" },
                Indicator_4_1_3: { deskreview: "37", isSurvey: true, isSurveyValue: "65" },
                Indicator_4_2_1: { deskreview: "47", isSurvey: true, isSurveyValue: "65" },
                Indicator_4_2_2: { deskreview: "57", isSurvey: true, isSurveyValue: "65" },
                Indicator_4_3_1: { deskreview: "67", isSurvey: true, isSurveyValue: "65" },
                Indicator_4_4_1: { deskreview: "77", isSurvey: true, isSurveyValue: "65" },
                Indicator_4_4_2: { deskreview: "87", isSurvey: true, isSurveyValue: "65" },
                Indicator_4_4_3: { deskreview: "97", isSurvey: true, isSurveyValue: "65" },
            },
            {
                Region: "AFRO",
                Country: "Bangladesh",
                YearOfAssessment: "2017",
                Indicator_1_1_2: { deskreview: "85", isSurvey: true, isSurveyValue: "65" },
                Indicator_1_1_3: { deskreview: "21", isSurvey: true, isSurveyValue: "65" },
                Indicator_1_1_4: { deskreview: "65", isSurvey: true, isSurveyValue: "65" },
                Indicator_1_1_5: { deskreview: "11", isSurvey: true, isSurveyValue: "65" },
                Indicator_1_1_7: { deskreview: "45", isSurvey: false, isSurveyValue: "65" },
                Indicator_1_1_8: { deskreview: "78", isSurvey: true, isSurveyValue: "65" },
                Indicator_1_1_9: { deskreview: "86", isSurvey: true, isSurveyValue: "65" },
                Indicator_1_3_1: { deskreview: "65", isSurvey: true, isSurveyValue: "65" },
                Indicator_1_3_2: { deskreview: "33", isSurvey: true, isSurveyValue: "65" },
                Indicator_1_3_3: { deskreview: "11", isSurvey: false, isSurveyValue: "65" },
                Indicator_1_3_4: { deskreview: "65", isSurvey: true, isSurveyValue: "65" },
                Indicator_1_3_7: { deskreview: "98", isSurvey: true, isSurveyValue: "65" },
                Indicator_2_1_1: { deskreview: "45", isSurvey: true, isSurveyValue: "65" },
                Indicator_2_1_2: { deskreview: "65", isSurvey: true, isSurveyValue: "65" },
                Indicator_2_1_3: { deskreview: "33", isSurvey: true, isSurveyValue: "65" },
                Indicator_2_1_4: { deskreview: "11", isSurvey: true, isSurveyValue: "65" },
                Indicator_2_2_1: { deskreview: "65", isSurvey: true, isSurveyValue: "65" },
                Indicator_2_2_2: { deskreview: "98", isSurvey: false, isSurveyValue: "65" },
                Indicator_2_2_4: { deskreview: "45", isSurvey: true, isSurveyValue: "65" },
                Indicator_2_2_5: { deskreview: "21", isSurvey: true, isSurveyValue: "65" },
                Indicator_2_2_6: { deskreview: "65", isSurvey: true, isSurveyValue: "65" },
                Indicator_2_3_1: { deskreview: "98", isSurvey: true, isSurveyValue: "65" },
                Indicator_2_3_2: { deskreview: "45", isSurvey: true, isSurveyValue: "65" },
                Indicator_2_4_1: { deskreview: "75", isSurvey: true, isSurveyValue: "65" },
                Indicator_2_4_2: { deskreview: "65", isSurvey: true, isSurveyValue: "65" },
                Indicator_2_4_4: { deskreview: "85", isSurvey: true, isSurveyValue: "65" },
                Indicator_2_5_1: { deskreview: "22", isSurvey: true, isSurveyValue: "65" },
                Indicator_3_1_2: { deskreview: "45", isSurvey: true, isSurveyValue: "65" },
                Indicator_3_1_3: { deskreview: "75", isSurvey: true, isSurveyValue: "65" },
                Indicator_3_2_1: { deskreview: "85", isSurvey: true, isSurveyValue: "65" },
                Indicator_3_2_2: { deskreview: "91", isSurvey: true, isSurveyValue: "65" },
                Indicator_3_2_3: { deskreview: "77", isSurvey: true, isSurveyValue: "65" },
                Indicator_3_3_1: { deskreview: "7", isSurvey: true, isSurveyValue: "65" },
                Indicator_3_3_2: { deskreview: "17", isSurvey: true, isSurveyValue: "65" },
                Indicator_3_3_3: { deskreview: "27", isSurvey: true, isSurveyValue: "65" },
                Indicator_3_3_4: { deskreview: "37", isSurvey: true, isSurveyValue: "65" },
                Indicator_3_4_1: { deskreview: "47", isSurvey: true, isSurveyValue: "65" },
                Indicator_3_4_2: { deskreview: "57", isSurvey: true, isSurveyValue: "65" },
                Indicator_3_5_1: { deskreview: "67", isSurvey: true, isSurveyValue: "65" },
                Indicator_3_5_2: { deskreview: "77", isSurvey: true, isSurveyValue: "65" },
                Indicator_3_5_3: { deskreview: "87", isSurvey: true, isSurveyValue: "65" },
                Indicator_3_6_1: { deskreview: "97", isSurvey: true, isSurveyValue: "65" },
                Indicator_4_1_1: { deskreview: "17", isSurvey: true, isSurveyValue: "65" },
                Indicator_4_1_2: { deskreview: "27", isSurvey: true, isSurveyValue: "65" },
                Indicator_4_1_3: { deskreview: "37", isSurvey: true, isSurveyValue: "65" },
                Indicator_4_2_1: { deskreview: "47", isSurvey: true, isSurveyValue: "65" },
                Indicator_4_2_2: { deskreview: "57", isSurvey: true, isSurveyValue: "65" },
                Indicator_4_3_1: { deskreview: "67", isSurvey: true, isSurveyValue: "65" },
                Indicator_4_4_1: { deskreview: "77", isSurvey: true, isSurveyValue: "65" },
                Indicator_4_4_2: { deskreview: "87", isSurvey: true, isSurveyValue: "65" },
                Indicator_4_4_3: { deskreview: "97", isSurvey: true, isSurveyValue: "65" },
            },
            {
                Region: "AFRO",
                Country: "Srilanka",
                YearOfAssessment: "2016",
                Indicator_1_1_2: { deskreview: "85", isSurvey: true, isSurveyValue: "65" },
                Indicator_1_1_3: { deskreview: "21", isSurvey: true, isSurveyValue: "65" },
                Indicator_1_1_4: { deskreview: "65", isSurvey: true, isSurveyValue: "65" },
                Indicator_1_1_5: { deskreview: "11", isSurvey: true, isSurveyValue: "65" },
                Indicator_1_1_7: { deskreview: "45", isSurvey: false, isSurveyValue: "65" },
                Indicator_1_1_8: { deskreview: "78", isSurvey: true, isSurveyValue: "65" },
                Indicator_1_1_9: { deskreview: "86", isSurvey: true, isSurveyValue: "65" },
                Indicator_1_3_1: { deskreview: "65", isSurvey: true, isSurveyValue: "65" },
                Indicator_1_3_2: { deskreview: "33", isSurvey: true, isSurveyValue: "65" },
                Indicator_1_3_3: { deskreview: "11", isSurvey: false, isSurveyValue: "65" },
                Indicator_1_3_4: { deskreview: "65", isSurvey: true, isSurveyValue: "65" },
                Indicator_1_3_7: { deskreview: "98", isSurvey: true, isSurveyValue: "65" },
                Indicator_2_1_1: { deskreview: "45", isSurvey: true, isSurveyValue: "65" },
                Indicator_2_1_2: { deskreview: "65", isSurvey: true, isSurveyValue: "65" },
                Indicator_2_1_3: { deskreview: "33", isSurvey: true, isSurveyValue: "65" },
                Indicator_2_1_4: { deskreview: "11", isSurvey: true, isSurveyValue: "65" },
                Indicator_2_2_1: { deskreview: "65", isSurvey: true, isSurveyValue: "65" },
                Indicator_2_2_2: { deskreview: "98", isSurvey: false, isSurveyValue: "65" },
                Indicator_2_2_4: { deskreview: "45", isSurvey: true, isSurveyValue: "65" },
                Indicator_2_2_5: { deskreview: "21", isSurvey: true, isSurveyValue: "65" },
                Indicator_2_2_6: { deskreview: "65", isSurvey: true, isSurveyValue: "65" },
                Indicator_2_3_1: { deskreview: "98", isSurvey: true, isSurveyValue: "65" },
                Indicator_2_3_2: { deskreview: "45", isSurvey: true, isSurveyValue: "65" },
                Indicator_2_4_1: { deskreview: "75", isSurvey: true, isSurveyValue: "65" },
                Indicator_2_4_2: { deskreview: "65", isSurvey: true, isSurveyValue: "65" },
                Indicator_2_4_4: { deskreview: "85", isSurvey: true, isSurveyValue: "65" },
                Indicator_2_5_1: { deskreview: "22", isSurvey: true, isSurveyValue: "65" },
                Indicator_3_1_2: { deskreview: "45", isSurvey: true, isSurveyValue: "65" },
                Indicator_3_1_3: { deskreview: "75", isSurvey: true, isSurveyValue: "65" },
                Indicator_3_2_1: { deskreview: "85", isSurvey: true, isSurveyValue: "65" },
                Indicator_3_2_2: { deskreview: "91", isSurvey: true, isSurveyValue: "65" },
                Indicator_3_2_3: { deskreview: "77", isSurvey: true, isSurveyValue: "65" },
                Indicator_3_3_1: { deskreview: "7", isSurvey: true, isSurveyValue: "65" },
                Indicator_3_3_2: { deskreview: "17", isSurvey: true, isSurveyValue: "65" },
                Indicator_3_3_3: { deskreview: "27", isSurvey: true, isSurveyValue: "65" },
                Indicator_3_3_4: { deskreview: "37", isSurvey: true, isSurveyValue: "65" },
                Indicator_3_4_1: { deskreview: "47", isSurvey: true, isSurveyValue: "65" },
                Indicator_3_4_2: { deskreview: "57", isSurvey: true, isSurveyValue: "65" },
                Indicator_3_5_1: { deskreview: "67", isSurvey: true, isSurveyValue: "65" },
                Indicator_3_5_2: { deskreview: "77", isSurvey: true, isSurveyValue: "65" },
                Indicator_3_5_3: { deskreview: "87", isSurvey: true, isSurveyValue: "65" },
                Indicator_3_6_1: { deskreview: "97", isSurvey: true, isSurveyValue: "65" },
                Indicator_4_1_1: { deskreview: "17", isSurvey: true, isSurveyValue: "65" },
                Indicator_4_1_2: { deskreview: "27", isSurvey: true, isSurveyValue: "65" },
                Indicator_4_1_3: { deskreview: "37", isSurvey: true, isSurveyValue: "65" },
                Indicator_4_2_1: { deskreview: "47", isSurvey: true, isSurveyValue: "65" },
                Indicator_4_2_2: { deskreview: "57", isSurvey: true, isSurveyValue: "65" },
                Indicator_4_3_1: { deskreview: "67", isSurvey: true, isSurveyValue: "65" },
                Indicator_4_4_1: { deskreview: "77", isSurvey: true, isSurveyValue: "65" },
                Indicator_4_4_2: { deskreview: "87", isSurvey: true, isSurveyValue: "65" },
                Indicator_4_4_3: { deskreview: "97", isSurvey: true, isSurveyValue: "65" },
            }
            ],
            "columns": [
                {
                    key: "Region",
                    label: "Region",
                    width: "100"
                },
                {
                    key: "Country",
                    label: "Country",
                    width: "100"
                },
                {
                    key: "YearOfAssessment",
                    label: "YearOfAssessment",
                    width: "100"
                },
                {
                    key: "Indicator_1_1_2",
                    label: "1.1.2",
                    width: "100"
                },
                {
                    key: "Indicator_1_1_3",
                    label: "1.1.3",
                    width: "100"
                },
                {
                    key: "Indicator_1_1_4",
                    label: "1.1.4",
                    width: "100"
                },
                {
                    key: "Indicator_1_1_5",
                    label: "1.1.5",
                    width: "100"
                },
                {
                    key: "Indicator_1_1_7",
                    label: "1.1.7",
                    width: "100"
                },
                {
                    key: "Indicator_1_1_8",
                    label: "1.1.8",
                    width: "100"
                },
                {
                    key: "Indicator_1_1_9",
                    label: "1.1.9",
                    width: "100"
                },
                {
                    key: "Indicator_1_3_1",
                    label: "1.3.1",
                    width: "100"
                },
                {
                    key: "Indicator_1_3_2",
                    label: "1.3.2",
                    width: "100"
                },
                {
                    key: "Indicator_1_3_3",
                    label: "1.3.3",
                    width: "100"
                },
                {
                    key: "Indicator_1_3_4",
                    label: "1.3.4",
                    width: "100"
                },
                {
                    key: "Indicator_1_3_7",
                    label: "1.3.7",
                    width: "100"
                },
                {
                    key: "Indicator_2_1_1",
                    label: "2.1.1",
                    width: "100"
                },
                {
                    key: "Indicator_2_1_2",
                    label: "2.1.2",
                    width: "100"
                },
                {
                    key: "Indicator_2_1_3",
                    label: "2.1.3",
                    width: "100"
                },
                {
                    key: "Indicator_2_1_4",
                    label: "2.1.4",
                    width: "100"
                },
                {
                    key: "Indicator_2_2_1",
                    label: "2.2.1",
                    width: "100"
                },
                {
                    key: "Indicator_2_2_2",
                    label: "2.2.2",
                    width: "100"
                },
                {
                    key: "Indicator_2_2_4",
                    label: "2.2.4",
                    width: "100"
                },
                {
                    key: "Indicator_2_2_5",
                    label: "2.2.5",
                    width: "100"
                },
                {
                    key: "Indicator_2_2_6",
                    label: "2.2.6",
                    width: "100"
                },
                {
                    key: "Indicator_2_3_1",
                    label: "2.3.1",
                    width: "100"
                },
                {
                    key: "Indicator_2_3_2",
                    label: "2.3.2",
                    width: "100"
                },
                {
                    key: "Indicator_2_4_1",
                    label: "2.4.1",
                    width: "100"
                },
                {
                    key: "Indicator_2_4_2",
                    label: "2.4.2",
                    width: "100"
                },
                {
                    key: "Indicator_2_4_4",
                    label: "2.4.4",
                    width: "100"
                },
                {
                    key: "Indicator_2_5_1",
                    label: "2.5.1",
                    width: "100"
                },
                {
                    key: "Indicator_3_1_2",
                    label: "3.1.2",
                    width: "100"
                },
                {
                    key: "Indicator_3_1_3",
                    label: "3.1.3",
                    width: "100"
                },
                {
                    key: "Indicator_3_2_1",
                    label: "3.2.1",
                    width: "100"
                },
                {
                    key: "Indicator_3_2_2",
                    label: "3.2.2",
                    width: "100"
                },
                {
                    key: "Indicator_3_2_3",
                    label: "3.2.3",
                    width: "100"
                },
                {
                    key: "Indicator_3_3_1",
                    label: "3.3.1",
                    width: "100"
                },
                {
                    key: "Indicator_3_3_2",
                    label: "3.3.2",
                    width: "100"
                },
                {
                    key: "Indicator_3_3_3",
                    label: "3.3.3",
                    width: "100"
                },
                {
                    key: "Indicator_3_3_4",
                    label: "3.3.4",
                    width: "100"
                },
                {
                    key: "Indicator_3_4_1",
                    label: "3.4.1",
                    width: "100"
                },
                {
                    key: "Indicator_3_4_2",
                    label: "3.4.2",
                    width: "100"
                },
                {
                    key: "Indicator_3_5_1",
                    label: "3.5.1",
                    width: "100"
                },
                {
                    key: "Indicator_3_5_2",
                    label: "3.5.2",
                    width: "100"
                },
                {
                    key: "Indicator_3_5_3",
                    label: "3.5.3",
                    width: "100"
                },
                {
                    key: "Indicator_3_6_1",
                    label: "3.6.1",
                    width: "100"
                },
                {
                    key: "Indicator_4_1_1",
                    label: "4.1.1",
                    width: "100"
                },
                {
                    key: "Indicator_4_1_2",
                    label: "4.1.2",
                    width: "100"
                },
                {
                    key: "Indicator_4_1_3",
                    label: "4.1.3",
                    width: "100"
                },
                {
                    key: "Indicator_4_2_1",
                    label: "4.2.1",
                    width: "100"
                },
                {
                    key: "Indicator_4_2_2",
                    label: "4.2.2",
                    width: "100"
                },
                {
                    key: "Indicator_4_3_1",
                    label: "4.3.1",
                    width: "100"
                },
                {
                    key: "Indicator_4_4_1",
                    label: "4.4.1",
                    width: "100"
                },
                {
                    key: "Indicator_4_4_2",
                    label: "4.4.2",
                    width: "100"
                },
                {
                    key: "Indicator_4_4_3",
                    label: "4.4.3",
                    width: "100"
                },
            ],
        }],
        ),

    viewReportForIndicators1: (): DashboardReportModel =>
        new DashboardReportModel(1, [{
            Region: "Region",
            Country: "Country",
            YearOfAssessment: "YearOfAssessment",
            "objectives": [
                {
                    key: "Performance",
                    value: "Performance",
                    name: "Performance",
                    subObjective: [{
                        name: "SURVEILLANCE SYSTEM COVERAGE",
                        indicators: [{
                            key: "Indicator_1_1_2",
                            value: "1.1.2",
                        },
                        {
                            key: "Indicator_1_1_3",
                            value: "1.1.3",
                        },
                        {
                            key: "Indicator_1_1_4",
                            value: "1.1.4",
                        },
                        {
                            key: "Indicator_1_1_5",
                            value: "1.1.5",
                        },
                        {
                            key: "Indicator_1_1_7",
                            value: "1.1.7",
                        },
                        {
                            key: "Indicator_1_1_8",
                            value: "1.1.8",
                        },
                        {
                            key: "Indicator_1_1_9",
                            value: "1.1.9",
                        },
                        ]
                    },
                    {
                        name: "DATA USE",
                        indicators: [{
                            key: "Indicator_1_3_1",
                            value: "1.3.1",
                        },
                        {
                            key: "Indicator_1_3_2",
                            value: "1.3.2",
                        },
                        {
                            key: "Indicator_1_3_3",
                            value: "1.3.3",
                        },
                        {
                            key: "Indicator_1_3_4",
                            value: "1.3.4",
                        },
                        {
                            key: "Indicator_1_3_7",
                            value: "1.3.7",
                        },
                        ]
                    },
                    ]
                },
                {
                    key: "Context and Infrastructure",
                    value: 15,
                    name: "Context and Infrastructure",
                    subObjective: [{
                        name: "SURVEILLANCE SECTORS & STRATEGIES",
                        indicators: [{
                            key: "Indicator_2_1_1",
                            value: "2.1.1",
                        },
                        {
                            key: "Indicator_2_1_2",
                            value: "2.1.2",
                        },
                        {
                            key: "Indicator_2_1_3",
                            value: "2.1.3",
                        },
                        {
                            key: "Indicator_2_1_4",
                            value: "2.1.4",
                        },
                        ]
                    },
                    {
                        name: "INFORMATION SYSTEMS",
                        indicators: [{
                            key: "Indicator_2_2_1",
                            value: "2.2.1",
                        },
                        {
                            key: "Indicator_2_2_2",
                            value: "2.2.2",
                        },
                        {
                            key: "Indicator_2_2_4",
                            value: "2.2.4",
                        },
                        {
                            key: "Indicator_2_2_5",
                            value: "2.2.5",
                        },
                        {
                            key: "Indicator_2_2_6",
                            value: "2.2.6",
                        },
                        ]
                    },
                    {
                        name: "SURVEILLANCE GUIDELINES & SOPs",
                        indicators: [{
                            key: "Indicator_2_3_1",
                            value: "2.3.1",
                        },
                        {
                            key: "Indicator_2_3_2",
                            value: "2.3.2",
                        },
                        ]
                    },
                    {
                        name: "FINANCIAL SUPPORT",
                        indicators: [{
                            key: "Indicator_2_4_1",
                            value: "2.4.1",
                        },
                        {
                            key: "Indicator_2_4_2",
                            value: "2.4.2",
                        },
                        {
                            key: "Indicator_2_4_4",
                            value: "2.4.4",
                        },
                        ]
                    },
                    {
                        name: "RESOURCES (STAFF, EQUIPMENT, INFRASTRUCTURE)",
                        indicators: [{
                            key: "Indicator_2_5_1",
                            value: "2.5.1",
                        },

                        ]
                    },
                    ]
                },
                {
                    key: "Technical and Process",
                    value: 15,
                    subObjective: [{
                        name: "CASE MANAGEMENT",
                        indicators: [{
                            key: "Indicator_3_1_2",
                            value: "3.1.2",
                        },
                        {
                            key: "Indicator_3_1_3",
                            value: "3.1.3",
                        },

                        ]
                    },
                    {
                        name: "RECORDING(assessed on forms/tools recording information at the health facility level)",
                        indicators: [{
                            key: "Indicator_3_2_1",
                            value: "3.2.1",
                        },
                        {
                            key: "Indicator_3_2_2",
                            value: "3.2.2",
                        },
                        {
                            key: "Indicator_3_2_3",
                            value: "3.2.3",
                        },
                        ]
                    },
                    ]
                },
                {
                    key: "Behaviour",
                    value: 9,
                    name: "Behaviour",
                    subObjective: [{
                        name: "SURVEILLANCE SYSTEM COVERAGE",
                        indicators: [{
                            key: "Indicator_1_1_3",
                            value: "1.1.3",
                        },
                        {
                            key: "Indicator_1_1_3",
                            value: "1.1.3",
                        },
                        ]
                    },
                    {
                        name: "DATA USE",
                        indicators: [{
                            key: "Indicator_1_1_3",
                            value: "1.1.3",
                        },
                        {
                            key: "Indicator_1_1_3",
                            value: "1.1.3",
                        },
                        ]
                    },
                    ]
                }
            ],
        },
        {
            "rows": [{
                Region: "AFRO",
                Country: "India",
                YearOfAssessment: "2019",
                Indicator_1_1_2: { deskreview: "85", isSurvey: true, isSurveyValue: "65" },
                Indicator_1_1_3: { deskreview: "55", isSurvey: false, isSurveyValue: "65" },
                Indicator_1_1_4: { deskreview: "55", isSurvey: false, isSurveyValue: "45" },
                Indicator_1_1_5: { deskreview: "65", isSurvey: false, isSurveyValue: "65" },
                Indicator_1_1_7: { deskreview: "55", isSurvey: false, isSurveyValue: "65" },
                Indicator_1_1_8: { deskreview: "15", isSurvey: false, isSurveyValue: "65" },
                Indicator_1_1_9: { deskreview: "55", isSurvey: false, isSurveyValue: "65" },
                Indicator_1_3_1: { deskreview: "65", isSurvey: false, isSurveyValue: "65" },
                Indicator_1_3_2: { deskreview: "33", isSurvey: false, isSurveyValue: "65" },
                Indicator_1_3_3: { deskreview: "11", isSurvey: false, isSurveyValue: "65" },
                Indicator_1_3_4: { deskreview: "65", isSurvey: false, isSurveyValue: "65" },
                Indicator_1_3_7: { deskreview: "98", isSurvey: false, isSurveyValue: "65" },
                Indicator_2_1_1: { deskreview: "45", isSurvey: false, isSurveyValue: "65" },
                Indicator_2_1_2: { deskreview: "65", isSurvey: false, isSurveyValue: "65" },
                Indicator_2_1_3: { deskreview: "33", isSurvey: false, isSurveyValue: "65" },
                Indicator_2_1_4: { deskreview: "11", isSurvey: false, isSurveyValue: "65" },
                Indicator_2_2_1: { deskreview: "65", isSurvey: false, isSurveyValue: "65" },
                Indicator_2_2_2: { deskreview: "98", isSurvey: false, isSurveyValue: "65" },
                Indicator_2_2_4: { deskreview: "55", isSurvey: false, isSurveyValue: "65" },
                Indicator_2_2_5: { deskreview: "55", isSurvey: false, isSurveyValue: "65" },
                Indicator_2_2_6: { deskreview: "65", isSurvey: false, isSurveyValue: "65" },
                Indicator_2_3_1: { deskreview: "98", isSurvey: false, isSurveyValue: "65" },
                Indicator_2_3_2: { deskreview: "22", isSurvey: false, isSurveyValue: "65" },
                Indicator_2_4_1: { deskreview: "56", isSurvey: false, isSurveyValue: "65" },
                Indicator_2_4_2: { deskreview: "85", isSurvey: false, isSurveyValue: "65" },
                Indicator_2_4_4: { deskreview: "66", isSurvey: false, isSurveyValue: "65" },
                Indicator_2_5_1: { deskreview: "55", isSurvey: false, isSurveyValue: "65" },
                Indicator_3_1_2: { deskreview: "85", isSurvey: false, isSurveyValue: "65" },
                Indicator_3_1_3: { deskreview: "33", isSurvey: false, isSurveyValue: "65" },
                Indicator_3_2_1: { deskreview: "44", isSurvey: false, isSurveyValue: "65" },
                Indicator_3_2_2: { deskreview: "45", isSurvey: false, isSurveyValue: "65" },
                Indicator_3_2_3: { deskreview: "75", isSurvey: false, isSurveyValue: "65" },
            },
            {
                Region: "AFRO",
                Country: "Nepal",
                YearOfAssessment: "2018",
                Indicator_1_1_2: { deskreview: "25", isSurvey: true, isSurveyValue: "65" },
                Indicator_1_1_3: { deskreview: "75", isSurvey: true, isSurveyValue: "65" },
                Indicator_1_1_4: { deskreview: "55", isSurvey: false, isSurveyValue: "45" },
                Indicator_1_1_5: { deskreview: "65", isSurvey: false, isSurveyValue: "65" },
                Indicator_1_1_7: { deskreview: "55", isSurvey: false, isSurveyValue: "65" },
                Indicator_1_1_8: { deskreview: "15", isSurvey: false, isSurveyValue: "65" },
                Indicator_1_1_9: { deskreview: "55", isSurvey: false, isSurveyValue: "65" },
                Indicator_1_3_1: { deskreview: "65", isSurvey: false, isSurveyValue: "65" },
                Indicator_1_3_2: { deskreview: "33", isSurvey: false, isSurveyValue: "65" },
                Indicator_1_3_3: { deskreview: "11", isSurvey: false, isSurveyValue: "65" },
                Indicator_1_3_4: { deskreview: "65", isSurvey: false, isSurveyValue: "65" },
                Indicator_1_3_7: { deskreview: "98", isSurvey: false, isSurveyValue: "65" },
                Indicator_2_1_1: { deskreview: "45", isSurvey: false, isSurveyValue: "65" },
                Indicator_2_1_2: { deskreview: "65", isSurvey: false, isSurveyValue: "65" },
                Indicator_2_1_3: { deskreview: "33", isSurvey: false, isSurveyValue: "65" },
                Indicator_2_1_4: { deskreview: "11", isSurvey: false, isSurveyValue: "65" },
                Indicator_2_2_1: { deskreview: "65", isSurvey: false, isSurveyValue: "65" },
                Indicator_2_2_2: { deskreview: "98", isSurvey: false, isSurveyValue: "65" },
                Indicator_2_2_4: { deskreview: "55", isSurvey: false, isSurveyValue: "65" },
                Indicator_2_2_5: { deskreview: "55", isSurvey: false, isSurveyValue: "65" },
                Indicator_2_2_6: { deskreview: "65", isSurvey: false, isSurveyValue: "65" },
                Indicator_2_3_1: { deskreview: "98", isSurvey: false, isSurveyValue: "65" },
                Indicator_2_3_2: { deskreview: "22", isSurvey: false, isSurveyValue: "65" },
                Indicator_2_4_1: { deskreview: "56", isSurvey: false, isSurveyValue: "65" },
                Indicator_2_4_2: { deskreview: "85", isSurvey: false, isSurveyValue: "65" },
                Indicator_2_4_4: { deskreview: "66", isSurvey: false, isSurveyValue: "65" },
                Indicator_2_5_1: { deskreview: "55", isSurvey: false, isSurveyValue: "65" },
                Indicator_3_1_2: { deskreview: "85", isSurvey: false, isSurveyValue: "65" },
                Indicator_3_1_3: { deskreview: "33", isSurvey: false, isSurveyValue: "65" },
                Indicator_3_2_1: { deskreview: "44", isSurvey: false, isSurveyValue: "65" },
                Indicator_3_2_2: { deskreview: "45", isSurvey: false, isSurveyValue: "65" },
                Indicator_3_2_3: { deskreview: "75", isSurvey: false, isSurveyValue: "65" },
            },
            {
                Region: "AFRO",
                Country: "Bangladesh",
                YearOfAssessment: "2017",
                Indicator_1_1_2: { deskreview: "85", isSurvey: true, isSurveyValue: "65" },
                Indicator_1_1_3: { deskreview: "55", isSurvey: true, isSurveyValue: "65" },
                Indicator_1_1_4: { deskreview: "55", isSurvey: false, isSurveyValue: "45" },
                Indicator_1_1_5: { deskreview: "65", isSurvey: false, isSurveyValue: "65" },
                Indicator_1_1_7: { deskreview: "55", isSurvey: false, isSurveyValue: "65" },
                Indicator_1_1_8: { deskreview: "15", isSurvey: false, isSurveyValue: "65" },
                Indicator_1_1_9: { deskreview: "55", isSurvey: false, isSurveyValue: "65" },
                Indicator_1_3_1: { deskreview: "65", isSurvey: false, isSurveyValue: "65" },
                Indicator_1_3_2: { deskreview: "33", isSurvey: false, isSurveyValue: "65" },
                Indicator_1_3_3: { deskreview: "11", isSurvey: false, isSurveyValue: "65" },
                Indicator_1_3_4: { deskreview: "65", isSurvey: false, isSurveyValue: "65" },
                Indicator_1_3_7: { deskreview: "98", isSurvey: false, isSurveyValue: "65" },
                Indicator_2_1_1: { deskreview: "45", isSurvey: false, isSurveyValue: "65" },
                Indicator_2_1_2: { deskreview: "65", isSurvey: false, isSurveyValue: "65" },
                Indicator_2_1_3: { deskreview: "33", isSurvey: false, isSurveyValue: "65" },
                Indicator_2_1_4: { deskreview: "11", isSurvey: false, isSurveyValue: "65" },
                Indicator_2_2_1: { deskreview: "65", isSurvey: false, isSurveyValue: "65" },
                Indicator_2_2_2: { deskreview: "98", isSurvey: false, isSurveyValue: "65" },
                Indicator_2_2_4: { deskreview: "55", isSurvey: false, isSurveyValue: "65" },
                Indicator_2_2_5: { deskreview: "55", isSurvey: false, isSurveyValue: "65" },
                Indicator_2_2_6: { deskreview: "65", isSurvey: false, isSurveyValue: "65" },
                Indicator_2_3_1: { deskreview: "98", isSurvey: false, isSurveyValue: "65" },
                Indicator_2_3_2: { deskreview: "22", isSurvey: false, isSurveyValue: "65" },
                Indicator_2_4_1: { deskreview: "56", isSurvey: false, isSurveyValue: "65" },
                Indicator_2_4_2: { deskreview: "85", isSurvey: false, isSurveyValue: "65" },
                Indicator_2_4_4: { deskreview: "66", isSurvey: false, isSurveyValue: "65" },
                Indicator_2_5_1: { deskreview: "55", isSurvey: false, isSurveyValue: "65" },
                Indicator_3_1_2: { deskreview: "85", isSurvey: false, isSurveyValue: "65" },
                Indicator_3_1_3: { deskreview: "33", isSurvey: false, isSurveyValue: "65" },
                Indicator_3_2_1: { deskreview: "44", isSurvey: false, isSurveyValue: "65" },
                Indicator_3_2_2: { deskreview: "45", isSurvey: false, isSurveyValue: "65" },
                Indicator_3_2_3: { deskreview: "75", isSurvey: false, isSurveyValue: "65" },
            },
            {
                Region: "AFRO",
                Country: "Srilanka",
                YearOfAssessment: "2016",
                Indicator_1_1_2: { deskreview: "85", isSurvey: true, isSurveyValue: "65" },
                Indicator_1_1_3: { deskreview: "55", isSurvey: true, isSurveyValue: "65" },
                Indicator_1_1_4: { deskreview: "55", isSurvey: false, isSurveyValue: "45" },
                Indicator_1_1_5: { deskreview: "65", isSurvey: false, isSurveyValue: "65" },
                Indicator_1_1_7: { deskreview: "55", isSurvey: false, isSurveyValue: "65" },
                Indicator_1_1_8: { deskreview: "15", isSurvey: false, isSurveyValue: "65" },
                Indicator_1_1_9: { deskreview: "55", isSurvey: false, isSurveyValue: "65" },
                Indicator_1_3_1: { deskreview: "65", isSurvey: false, isSurveyValue: "65" },
                Indicator_1_3_2: { deskreview: "33", isSurvey: false, isSurveyValue: "65" },
                Indicator_1_3_3: { deskreview: "11", isSurvey: false, isSurveyValue: "65" },
                Indicator_1_3_4: { deskreview: "65", isSurvey: false, isSurveyValue: "65" },
                Indicator_1_3_7: { deskreview: "98", isSurvey: false, isSurveyValue: "65" },
                Indicator_2_1_1: { deskreview: "45", isSurvey: false, isSurveyValue: "65" },
                Indicator_2_1_2: { deskreview: "65", isSurvey: false, isSurveyValue: "65" },
                Indicator_2_1_3: { deskreview: "33", isSurvey: false, isSurveyValue: "65" },
                Indicator_2_1_4: { deskreview: "11", isSurvey: false, isSurveyValue: "65" },
                Indicator_2_2_1: { deskreview: "65", isSurvey: false, isSurveyValue: "65" },
                Indicator_2_2_2: { deskreview: "98", isSurvey: false, isSurveyValue: "65" },
                Indicator_2_2_4: { deskreview: "55", isSurvey: false, isSurveyValue: "65" },
                Indicator_2_2_5: { deskreview: "55", isSurvey: false, isSurveyValue: "65" },
                Indicator_2_2_6: { deskreview: "65", isSurvey: false, isSurveyValue: "65" },
                Indicator_2_3_1: { deskreview: "98", isSurvey: false, isSurveyValue: "65" },
                Indicator_2_3_2: { deskreview: "22", isSurvey: false, isSurveyValue: "65" },
                Indicator_2_4_1: { deskreview: "56", isSurvey: false, isSurveyValue: "65" },
                Indicator_2_4_2: { deskreview: "85", isSurvey: false, isSurveyValue: "65" },
                Indicator_2_4_4: { deskreview: "66", isSurvey: false, isSurveyValue: "65" },
                Indicator_2_5_1: { deskreview: "55", isSurvey: false, isSurveyValue: "65" },
                Indicator_3_1_2: { deskreview: "85", isSurvey: false, isSurveyValue: "65" },
                Indicator_3_1_3: { deskreview: "33", isSurvey: false, isSurveyValue: "65" },
                Indicator_3_2_1: { deskreview: "44", isSurvey: false, isSurveyValue: "65" },
                Indicator_3_2_2: { deskreview: "45", isSurvey: false, isSurveyValue: "65" },
                Indicator_3_2_3: { deskreview: "75", isSurvey: false, isSurveyValue: "65" },
            }
            ],

        }],
        ),

    /** View report for the priority indicators */
    ViewReportForIndicators1: (): IndicatorReportModel =>
        new IndicatorReportModel("1", 2, [{
            "tabId": "0",
            "tabName": "National",
            "tables": [{
                "rows": [{
                    Name: "Private",
                    Year1: "85",
                    Year2: "75",
                    Year3: "65",
                },
                {
                    Name: "Private",
                    Year1: "80",
                    Year2: "70",
                    Year3: "60",
                },
                {
                    Name: "Private",
                    Year1: "80",
                    Year2: "70",
                    Year3: "60",
                },
                {
                    Name: "Private",
                    Year1: "80",
                    Year2: "70",
                    Year3: "60",
                }
                ],
                "columns": [
                    {
                        key: "Name",
                        label: "Country (Name)",
                        width: "300"
                    },
                    {
                        key: "Year1",
                        label: "2017",
                        width: "200"
                    },
                    {
                        key: "Year2",
                        label: "2018",
                        width: "200"
                    },
                    {
                        key: "Year3",
                        label: "2019",
                        width: "200"
                    },
                    {
                        key: "Year3",
                        label: "20",
                        width: "200"
                    },
                ],
                "reportTitle": "Title 345"
            },
            {
                "rows": [{
                    Name: "Private",
                    Year1: "85",
                    Year2: "75",
                    Year3: "65",
                },
                {
                    Name: "Private",
                    Year1: "81",
                    Year2: "71",
                    Year3: "61",
                },
                {
                    Name: "Private",
                    Year1: "85",
                    Year2: "75",
                    Year3: "65",
                },
                {
                    Name: "Private",
                    Year1: "80",
                    Year2: "70",
                    Year3: "60",
                }
                ],
                "columns": [
                    {
                        key: "Name",
                        label: "Country (Name)",
                        width: "300"
                    },
                    {
                        key: "Year1",
                        label: "2011",
                        width: "200"
                    },
                    {
                        key: "Year2",
                        label: "2012",
                        width: "200"
                    },
                    {
                        key: "Year3",
                        label: "2013",
                        width: "200"
                    },
                    {
                        key: "Year3",
                        label: "20",
                        width: "200"
                    },
                ],
                "reportTitle": "Title 123"
            }
            ],
            "graphs": [{
                "values": [
                    {
                        key: "2011",
                        value: 10
                    },
                    {
                        key: "2012",
                        value: 20
                    },
                    {
                        key: "2013",
                        value: 30
                    },
                    {
                        key: "2014",
                        value: 40
                    }
                ],
                "xAxis": "Year 123",
                "yAxis": "National Level Estimate 2",
                "graphTitle": "Figure 1.1.2bb  Proportion of suspects tested over time"
            },
            {
                "values": [
                    {
                        key: "2017",
                        value: 60
                    },
                    {
                        key: "2018",
                        value: 70
                    },
                    {
                        key: "2019",
                        value: 80
                    },
                    {
                        key: "2014",
                        value: 40
                    }
                ],
                "xAxis": "Year 123",
                "yAxis": "National Level Estimate 2",
                "graphTitle": "Figure Proportion of suspects tested over time"
            }
            ]
        },
        {
            "tabId": "1",
            "tabName": "Province",
            "tables": [{
                "rows": [{
                    Name: "Private",
                    Year1: "85",
                    Year2: "75",
                    Year3: "65",
                },
                {
                    Name: "Private",
                    Year1: "80",
                    Year2: "70",
                    Year3: "60",
                },
                {
                    Name: "Private",
                    Year1: "80",
                    Year2: "70",
                    Year3: "60",
                },
                {
                    Name: "Private",
                    Year1: "80",
                    Year2: "70",
                    Year3: "60",
                }
                ],
                "columns": [
                    {
                        key: "Name",
                        label: "Country (Name)",
                        width: "300"
                    },
                    {
                        key: "Year1",
                        label: "2014",
                        width: "200"
                    },
                    {
                        key: "Year2",
                        label: "2015",
                        width: "200"
                    },
                    {
                        key: "Year3",
                        label: "2016",
                        width: "200"
                    },
                    {
                        key: "Year3",
                        label: "20",
                        width: "200"
                    },
                ],
                "reportTitle": "Title"
            },
            {
                "rows": [{
                    Name: "Private",
                    Year1: "85",
                    Year2: "75",
                    Year3: "65",
                },
                {
                    Name: "Private",
                    Year1: "80",
                    Year2: "70",
                    Year3: "60",
                },
                {
                    Name: "Private",
                    Year1: "80",
                    Year2: "70",
                    Year3: "60",
                },
                {
                    Name: "Private",
                    Year1: "80",
                    Year2: "70",
                    Year3: "60",
                }
                ],
                "columns": [
                    {
                        key: "Name",
                        label: "Country (Name)",
                        width: "300"
                    },
                    {
                        key: "Year1",
                        label: "2009",
                        width: "200"
                    },
                    {
                        key: "Year2",
                        label: "2010",
                        width: "200"
                    },
                    {
                        key: "Year3",
                        label: "2011",
                        width: "200"
                    },
                    {
                        key: "Year3",
                        label: "20",
                        width: "200"
                    },
                ],
                "reportTitle": "Title"
            }
            ],
            "graphs": [
                {
                    "values": [
                        {
                            key: "2011",
                            value: 10
                        },
                        {
                            key: "2012",
                            value: 20
                        },
                        {
                            key: "2013",
                            value: 30
                        },
                        {
                            key: "2014",
                            value: 40
                        }
                    ],
                    "xAxis": "Year 123",
                    "yAxis": "National Level Estimate 2",
                    "graphTitle": "Figure 1.1.2bb  Proportion of suspects tested over time"
                },
                {
                    "values": [
                        {
                            key: "2017",
                            value: 60
                        },
                        {
                            key: "2018",
                            value: 70
                        },
                        {
                            key: "2019",
                            value: 80
                        },
                        {
                            key: "2014",
                            value: 40
                        }
                    ],
                    "xAxis": "Year 123",
                    "yAxis": "National Level Estimate 2",
                    "graphTitle": "Figure Proportion of suspects tested over time"
                }
            ]
        },
        {
            "tabId": "2",
            "tabName": "District",
            "tables": [{
                "rows": [{
                    Name: "Private",
                    Year1: "85",
                    Year2: "75",
                    Year3: "65",
                },
                {
                    Name: "Private",
                    Year1: "80",
                    Year2: "70",
                    Year3: "60",
                },
                {
                    Name: "Private",
                    Year1: "80",
                    Year2: "70",
                    Year3: "60",
                },
                {
                    Name: "Private",
                    Year1: "80",
                    Year2: "70",
                    Year3: "60",
                }
                ],
                "columns": [
                    {
                        key: "Name",
                        label: "Country (Name)",
                        width: "300"
                    },
                    {
                        key: "Year1",
                        label: "2014",
                        width: "200"
                    },
                    {
                        key: "Year2",
                        label: "2015",
                        width: "200"
                    },
                    {
                        key: "Year3",
                        label: "2016",
                        width: "200"
                    },
                    {
                        key: "Year3",
                        label: "20",
                        width: "200"
                    },
                ],
                "reportTitle": "Title"
            }],
            "graphs": [
                {
                    "values": [
                        {
                            key: "2011",
                            value: 10
                        },
                        {
                            key: "2012",
                            value: 20
                        },
                        {
                            key: "2013",
                            value: 30
                        },
                        {
                            key: "2014",
                            value: 40
                        }
                    ],
                    "xAxis": "Year 123",
                    "yAxis": "National Level Estimate 2",
                    "graphTitle": "Figure 1.1.2bb  Proportion of suspects tested over time"
                },
            ]
        },
        ],
        ),

    /** View report for the priority indicators */
    ViewReportForIndicators11: (): IndicatorReportModel =>
        new IndicatorReportModel("1", 3, [{
            "tabId": "0",
            "tabName": "National",
            "tabs": [{
                "tabId": "0",
                "tabName": "National Sub1",
                "tables": [{
                    "rows": [{
                        Name: "Private",
                        Year1: "85",
                        Year2: "75",
                        Year3: "65",
                    },
                    {
                        Name: "Private",
                        Year1: "80",
                        Year2: "70",
                        Year3: "60",
                    },
                    {
                        Name: "Private",
                        Year1: "80",
                        Year2: "70",
                        Year3: "60",
                    },
                    {
                        Name: "Private",
                        Year1: "80",
                        Year2: "70",
                        Year3: "60",
                    }
                    ],
                    "columns": [
                        {
                            key: "Name",
                            label: "Country (Name) Nested",
                            width: "300"
                        },
                        {
                            key: "Year1",
                            label: "2017",
                            width: "200"
                        },
                        {
                            key: "Year2",
                            label: "2018",
                            width: "200"
                        },
                        {
                            key: "Year3",
                            label: "2019",
                            width: "200"
                        },
                        {
                            key: "Year3",
                            label: "20",
                            width: "200"
                        },
                    ],
                    "reportTitle": "Title 345"
                },
                {
                    "rows": [{
                        Name: "Private",
                        Year1: "85",
                        Year2: "75",
                        Year3: "65",
                    },
                    {
                        Name: "Private",
                        Year1: "81",
                        Year2: "71",
                        Year3: "61",
                    },
                    {
                        Name: "Private",
                        Year1: "85",
                        Year2: "75",
                        Year3: "65",
                    },
                    {
                        Name: "Private",
                        Year1: "80",
                        Year2: "70",
                        Year3: "60",
                    }
                    ],
                    "columns": [
                        {
                            key: "Name",
                            label: "Country (Name)",
                            width: "300"
                        },
                        {
                            key: "Year1",
                            label: "2011",
                            width: "200"
                        },
                        {
                            key: "Year2",
                            label: "2012",
                            width: "200"
                        },
                        {
                            key: "Year3",
                            label: "2013",
                            width: "200"
                        },
                        {
                            key: "Year3",
                            label: "20",
                            width: "200"
                        },
                    ],
                    "reportTitle": "Title 123"
                }
                ],
                "graphs": [{
                    "values": [
                        {
                            key: "2011",
                            value: 10
                        },
                        {
                            key: "2012",
                            value: 20
                        },
                        {
                            key: "2013",
                            value: 30
                        },
                        {
                            key: "2014",
                            value: 40
                        }
                    ],
                    "xAxis": "Year 123",
                    "yAxis": "National Level Estimate 2",
                    "graphTitle": "Figure 1.1.2bb  Proportion of suspects tested over time"
                },
                {
                    "values": [
                        {
                            key: "2017",
                            value: 60
                        },
                        {
                            key: "2018",
                            value: 70
                        },
                        {
                            key: "2019",
                            value: 80
                        },
                        {
                            key: "2014",
                            value: 40
                        }
                    ],
                    "xAxis": "Year 123",
                    "yAxis": "National Level Estimate 2",
                    "graphTitle": "Figure Proportion of suspects tested over time"
                }
                ]
            },
            {
                "tabId": "1",
                "tabName": "Province Sub1",
                "tables": [{
                    "rows": [{
                        Name: "Private",
                        Year1: "85",
                        Year2: "75",
                        Year3: "65",
                    },
                    {
                        Name: "Private",
                        Year1: "80",
                        Year2: "70",
                        Year3: "60",
                    },
                    {
                        Name: "Private",
                        Year1: "80",
                        Year2: "70",
                        Year3: "60",
                    },
                    {
                        Name: "Private",
                        Year1: "80",
                        Year2: "70",
                        Year3: "60",
                    }
                    ],
                    "columns": [
                        {
                            key: "Name",
                            label: "Country (Name)",
                            width: "300"
                        },
                        {
                            key: "Year1",
                            label: "2017",
                            width: "200"
                        },
                        {
                            key: "Year2",
                            label: "2018",
                            width: "200"
                        },
                        {
                            key: "Year3",
                            label: "2019",
                            width: "200"
                        },
                        {
                            key: "Year3",
                            label: "20",
                            width: "200"
                        },
                    ],
                    "reportTitle": "Title 345"
                },
                {
                    "rows": [{
                        Name: "Private",
                        Year1: "85",
                        Year2: "75",
                        Year3: "65",
                    },
                    {
                        Name: "Private",
                        Year1: "81",
                        Year2: "71",
                        Year3: "61",
                    },
                    {
                        Name: "Private",
                        Year1: "85",
                        Year2: "75",
                        Year3: "65",
                    },
                    {
                        Name: "Private",
                        Year1: "80",
                        Year2: "70",
                        Year3: "60",
                    }
                    ],
                    "columns": [
                        {
                            key: "Name",
                            label: "Country (Name)",
                            width: "300"
                        },
                        {
                            key: "Year1",
                            label: "2011",
                            width: "200"
                        },
                        {
                            key: "Year2",
                            label: "2012",
                            width: "200"
                        },
                        {
                            key: "Year3",
                            label: "2013",
                            width: "200"
                        },
                        {
                            key: "Year3",
                            label: "20",
                            width: "200"
                        },
                    ],
                    "reportTitle": "Title 123"
                }
                ],
                "graphs": [{
                    "values": [
                        {
                            key: "2011",
                            value: 10
                        },
                        {
                            key: "2012",
                            value: 20
                        },
                        {
                            key: "2013",
                            value: 30
                        },
                        {
                            key: "2014",
                            value: 40
                        }
                    ],
                    "xAxis": "Year 123",
                    "yAxis": "National Level Estimate 2",
                    "graphTitle": "Figure 1.1.2bb  Proportion of suspects tested over time"
                },
                {
                    "values": [
                        {
                            key: "2017",
                            value: 60
                        },
                        {
                            key: "2018",
                            value: 70
                        },
                        {
                            key: "2019",
                            value: 80
                        },
                        {
                            key: "2014",
                            value: 40
                        }
                    ],
                    "xAxis": "Year 123",
                    "yAxis": "National Level Estimate 2",
                    "graphTitle": "Figure Proportion of suspects tested over time"
                }
                ]
            }
            ],
        },
        {
            "tabId": "1",
            "tabName": "Province",
            "tabs": [{
                "tabId": "1",
                "tabName": "Province Sub1",
                "tables": [{
                    "rows": [{
                        Name: "Private",
                        Year1: "85",
                        Year2: "75",
                        Year3: "65",
                    },
                    {
                        Name: "Private",
                        Year1: "80",
                        Year2: "70",
                        Year3: "60",
                    },
                    {
                        Name: "Private",
                        Year1: "80",
                        Year2: "70",
                        Year3: "60",
                    },
                    {
                        Name: "Private",
                        Year1: "80",
                        Year2: "70",
                        Year3: "60",
                    }
                    ],
                    "columns": [
                        {
                            key: "Name",
                            label: "Country (Name)",
                            width: "300"
                        },
                        {
                            key: "Year1",
                            label: "2014",
                            width: "200"
                        },
                        {
                            key: "Year2",
                            label: "2015",
                            width: "200"
                        },
                        {
                            key: "Year3",
                            label: "2016",
                            width: "200"
                        },
                        {
                            key: "Year3",
                            label: "20",
                            width: "200"
                        },
                    ],
                    "reportTitle": "Title"
                }
                ],
                "graphs": [
                    {
                        "values": [
                            {
                                key: "2011",
                                value: 10
                            },
                            {
                                key: "2012",
                                value: 20
                            },
                            {
                                key: "2013",
                                value: 30
                            },
                            {
                                key: "2014",
                                value: 40
                            }
                        ],
                        "xAxis": "Year 123",
                        "yAxis": "National Level Estimate 2",
                        "graphTitle": "Figure Nested  Proportion of suspects tested over time"
                    },
                ]
            }]
        },
        ],
        ),

    /** View report for the priority indicators */
    ViewReportRegionwise: (): IndicatorReportModel =>
        new IndicatorReportModel("1", 2, [{
            "tabId": "0",
            "tabName": "AFRO",
            "charts": [{
                "values": [
                    {
                        key: "Met",
                        value: [20, 25, 16, 31, 22, 14, 42, 21, 30, 31, 22, 14, 22, 31, 11, 9]
                    },
                    {
                        key: "Partially Met",
                        value: [22, 32, 32, 31, 22, 14, 42, 21, 30, 31, 22, 14, 22, 31, 11, 9]
                    },
                    {
                        key: "Not Met",
                        value: [32, 19, 18, 31, 22, 14, 42, 21, 30, 31, 22, 14, 22, 31, 11, 9]
                    },
                    {
                        key: "Not Assessed",
                        value: [42, 21, 30, 31, 22, 14, 42, 21, 30, 31, 22, 14, 22, 31, 11, 9]
                    },
                ],
                "xAxis": "Year 123",
                "yAxis": "Percentage",
                "title": "Region Chart",
                "categories": ["SubObj 1.1", "SubObj 1.2", "SubObj 1.3", "SubObj 2.1", "SubObj 2.2", "SubObj 2.3", "SubObj 2.4", "SubObj 2.5", "SubObj 3.1", "SubObj 3.2", "SubObj 3.3", "SubObj 3.4", "SubObj 3.5", "SubObj 3.6", "SubObj 4.1", "SubObj 4.3"]
            }
            ]
        },
        {
            "tabId": "1",
            "tabName": "EMRO",
            "charts": [{
                "values": [
                    {
                        key: "Met",
                        value: [42, 21, 30, 31, 22, 14, 42, 21, 30, 31, 22, 14, 22, 31, 11, 9]
                    },
                    {
                        key: "Partially Met",
                        value: [42, 21, 30, 31, 22, 14, 42, 21, 30, 31, 22, 14, 22, 31, 11, 9]
                    },
                    {
                        key: "Not Met",
                        value: [42, 21, 30, 31, 22, 14, 42, 21, 30, 31, 22, 14, 22, 31, 11, 9]
                    },
                    {
                        key: "Not Assessed",
                        value: [42, 21, 30, 31, 22, 14, 42, 21, 30, 31, 22, 14, 22, 31, 11, 9]
                    },
                ],
                "xAxis": "Year 123",
                "yAxis": "Percentage",
                "title": "Region Chart",
                "categories": ["SubObj 1.1", "SubObj 1.2", "SubObj 1.3", "SubObj 2.1", "SubObj 2.2", "SubObj 2.3", "SubObj 2.4", "SubObj 2.5", "SubObj 3.1", "SubObj 3.2", "SubObj 3.3", "SubObj 3.4", "SubObj 3.5", "SubObj 3.6", "SubObj 4.1", "SubObj 4.3"]
            }
            ]
        },
        {
            "tabId": "2",
            "tabName": "PAHO",
            "charts": [{
                "values": [
                    {
                        key: "Met",
                        value: [42, 21, 30, 31, 22, 14, 42, 21, 30, 31, 22, 14, 22, 31, 11, 9]
                    },
                    {
                        key: "Partially Met",
                        value: [42, 21, 30, 31, 22, 14, 42, 21, 30, 31, 22, 14, 22, 31, 11, 9]
                    },
                    {
                        key: "Not Met",
                        value: [42, 21, 30, 31, 22, 14, 42, 21, 30, 31, 22, 14, 22, 31, 11, 9]
                    },
                    {
                        key: "Not Assessed",
                        value: [42, 21, 30, 31, 22, 14, 42, 21, 30, 31, 22, 14, 22, 31, 11, 9]
                    },
                ],
                "xAxis": "Year 123",
                "yAxis": "Percentage",
                "title": "Region Chart",
                "categories": ["SubObj 1.1", "SubObj 1.2", "SubObj 1.3", "SubObj 2.1", "SubObj 2.2", "SubObj 2.3", "SubObj 2.4", "SubObj 2.5", "SubObj 3.1", "SubObj 3.2", "SubObj 3.3", "SubObj 3.4", "SubObj 3.5", "SubObj 3.6", "SubObj 4.1", "SubObj 4.3"]
            }
            ]
        },
        {
            "tabId": "3",
            "tabName": "SEARO",
            "charts": [{
                "values": [
                    {
                        key: "Met",
                        value: [42, 21, 30, 31, 22, 14, 42, 21, 30, 31, 22, 14, 22, 31, 11, 9]
                    },
                    {
                        key: "Partially Met",
                        value: [42, 21, 30, 31, 22, 14, 42, 21, 30, 31, 22, 14, 22, 31, 11, 9]
                    },
                    {
                        key: "Not Met",
                        value: [42, 21, 30, 31, 22, 14, 42, 21, 30, 31, 22, 14, 22, 31, 11, 9]
                    },
                    {
                        key: "Not Assessed",
                        value: [42, 21, 30, 31, 22, 14, 42, 21, 30, 31, 22, 14, 22, 31, 11, 9]
                    },
                ],
                "xAxis": "Year 123",
                "yAxis": "Percentage",
                "title": "Region Chart",
                "categories": ["SubObj 1.1", "SubObj 1.2", "SubObj 1.3", "SubObj 2.1", "SubObj 2.2", "SubObj 2.3", "SubObj 2.4", "SubObj 2.5", "SubObj 3.1", "SubObj 3.2", "SubObj 3.3", "SubObj 3.4", "SubObj 3.5", "SubObj 3.6", "SubObj 4.1", "SubObj 4.3"]
            }
            ]
        }
        ],

        ),


    getObjeciveSubObjective: (): Array<SubObjectivePercentageModel> => {
        return [
            new SubObjectivePercentageModel("1", "Performance", 60,
                [
                    new GetCountrySpecificSubObjectiveModel("1.1", "Surveillance", 60, "1",false),
                    new GetCountrySpecificSubObjectiveModel("1.2", "Data Quality", 80, "1",false),
                    new GetCountrySpecificSubObjectiveModel("1.3", "Information System", 61, "1",false)
                ], "2017"),
            new SubObjectivePercentageModel("3", "Technical and Process", 100,
                [
                    new GetCountrySpecificSubObjectiveModel("3.1", "Case Management", 39, "2",false),
                    new GetCountrySpecificSubObjectiveModel("3.2", "Recording", 23, "2",false),
                    new GetCountrySpecificSubObjectiveModel("3.3", "Surveillance", 77, "2",false)
                ], "2019"),
            new SubObjectivePercentageModel("2", "Technical and Process", 45,
                [
                    new GetCountrySpecificSubObjectiveModel("2.1", "Case Management", 39, "2",false),
                    new GetCountrySpecificSubObjectiveModel("2.2", "Recording", 98, "2",false),
                    new GetCountrySpecificSubObjectiveModel("2.5", "Data Quality", 76, "2",false)
                ], "2019"),
            new SubObjectivePercentageModel("2", "Technical and Process", 35,
                [
                    new GetCountrySpecificSubObjectiveModel("2.1", "Case Management", 78, "2", false),
                    new GetCountrySpecificSubObjectiveModel("2.2", "Information System", 43, "2",false),
                    new GetCountrySpecificSubObjectiveModel("2.3", "Recording", 20, "2",false)
                ], "2020"),
        ]

    },

    viewWolrdMapObjective: (): DashboardReportModel =>
        new DashboardReportModel(3, [{
            "strategy": [
                {
                    "tabId": "0",
                    "tabName": "Both",
                    "objectives": [
                    {
                        key: "Performance",
                        value: "Performance",
                        name: "Performance",
                        tabId: "0",
                        tabName: "Performance",
                        countries: [
                            { iso: "BEN", status: MetNotMetStatus.Met },
                            { iso: "KHM", status: MetNotMetStatus.Met },
                            { iso: "AGO", status: MetNotMetStatus.NotAssessed },
                            { iso: "SDN", status: MetNotMetStatus.Met },
                            { iso: "PAK", status: MetNotMetStatus.Met },
                            { iso: "BFA", status: MetNotMetStatus.NotMet },
                            { iso: "MYS", status: MetNotMetStatus.PartiallyMet },
                            { iso: "BTN", status: MetNotMetStatus.NotMet },
                            { iso: "GHA", status: MetNotMetStatus.NotMet },
                            { iso: "CMR", status: MetNotMetStatus.NotMet },
                            { iso: "COD", status: MetNotMetStatus.PartiallyMet },
                            { iso: "BFA", status: MetNotMetStatus.NotMet },
                            { iso: "MYS", status: MetNotMetStatus.NotMet },
                            { iso: "BTN", status: MetNotMetStatus.PartiallyMet },
                            { iso: "GHA", status: MetNotMetStatus.NotMet },
                            { iso: "CMR", status: MetNotMetStatus.NotMet },
                            { iso: "COD", status: MetNotMetStatus.NotAssessed },
                            
                        ]
                    },
                    {
                        key: "Context and Infrastructure",
                        value: "Context and Infrastructure",
                        name: "Context and Infrastructure",
                        tabId: "1",
                        tabName: "Context and Infrastructure",
                        countries: [
                            { iso: "BFA", status: MetNotMetStatus.NotMet },
                            { iso: "MYS", status: MetNotMetStatus.NotMet },
                            { iso: "BTN", status: MetNotMetStatus.NotMet },
                            { iso: "GHA", status: MetNotMetStatus.NotMet },
                            { iso: "CMR", status: MetNotMetStatus.NotMet },
                            { iso: "COD", status: MetNotMetStatus.NotMet },
                            { iso: "MYS", status: MetNotMetStatus.PartiallyMet },
                            { iso: "BTN", status: MetNotMetStatus.PartiallyMet },
                            { iso: "AGO", status: MetNotMetStatus.PartiallyMet },
                            { iso: "SDN", status: MetNotMetStatus.PartiallyMet },
                            { iso: "PAK", status: MetNotMetStatus.PartiallyMet },
                        ]
                    },
                    {
                        key: "Technical and Process",
                        value: "Technical and Process",
                        name: "Technical and Process",
                        tabId: "2",
                        tabName: "Technical and Process",
                        countries: [
                            { iso: "MYS", status: MetNotMetStatus.PartiallyMet },
                            { iso: "BTN", status: MetNotMetStatus.PartiallyMet },
                            { iso: "AGO", status: MetNotMetStatus.PartiallyMet },
                            { iso: "SDN", status: MetNotMetStatus.PartiallyMet },
                            { iso: "PAK", status: MetNotMetStatus.PartiallyMet },
                            { iso: "KHM", status: MetNotMetStatus.PartiallyMet },
                            { iso: "BFA", status: MetNotMetStatus.NotAssessed },
                            { iso: "GHA", status: MetNotMetStatus.NotAssessed },
                            { iso: "COD", status: MetNotMetStatus.NotAssessed },
                            { iso: "BEN", status: MetNotMetStatus.NotAssessed },
                            { iso: "CMR", status: MetNotMetStatus.NotAssessed },
                        ]
                    },
                    {
                        key: "Behaviour",
                        value: "Behaviour",
                        name: "Behaviour",
                        tabId: "3",
                        tabName: "Behaviour",
                        countries: [
                            { iso: "BTN", status: MetNotMetStatus.NotAssessed },
                            { iso: "MYS", status: MetNotMetStatus.NotMet },
                            { iso: "BFA", status: MetNotMetStatus.NotMet },
                            { iso: "AGO", status: MetNotMetStatus.PartiallyMet },
                            { iso: "SDN", status: MetNotMetStatus.PartiallyMet },
                            { iso: "PAK", status: MetNotMetStatus.PartiallyMet },
                            { iso: "KHM", status: MetNotMetStatus.PartiallyMet },
                            { iso: "GHA", status: MetNotMetStatus.Met },
                            { iso: "COD", status: MetNotMetStatus.Met },
                            { iso: "BEN", status: MetNotMetStatus.Met },
                            { iso: "CMR", status: MetNotMetStatus.Met },
                        ]
                    }
                    ]
                },
                {
                    "tabId": "1",
                    "tabName": "Burden Reduction",
                    "objectives": [
                    {
                        key: "Performance",
                        value: "Performance",
                        name: "Performance",
                        tabId: "0",
                        tabName: "Performance",
                        countries: [
                            { iso: "BEN", status: MetNotMetStatus.Met },
                            { iso: "KHM", status: MetNotMetStatus.Met },
                            { iso: "AGO", status: MetNotMetStatus.Met },
                            { iso: "SDN", status: MetNotMetStatus.Met },
                            { iso: "PAK", status: MetNotMetStatus.Met },
                            { iso: "BFA", status: MetNotMetStatus.NotMet },
                            { iso: "MYS", status: MetNotMetStatus.NotMet },
                            { iso: "BTN", status: MetNotMetStatus.NotMet },
                            { iso: "GHA", status: MetNotMetStatus.NotMet },
                            { iso: "CMR", status: MetNotMetStatus.NotMet },
                            { iso: "COD", status: MetNotMetStatus.NotMet },
                        ]
                    },
                    {
                        key: "Context and Infrastructure",
                        value: "Context and Infrastructure",
                        name: "Context and Infrastructure",
                        tabId: "1",
                        tabName: "Context and Infrastructure",
                        countries: [
                            { iso: "BFA", status: MetNotMetStatus.NotMet },
                            { iso: "MYS", status: MetNotMetStatus.NotMet },
                            { iso: "BTN", status: MetNotMetStatus.NotMet },
                            { iso: "GHA", status: MetNotMetStatus.NotMet },
                            { iso: "CMR", status: MetNotMetStatus.NotMet },
                            { iso: "COD", status: MetNotMetStatus.NotMet },
                            { iso: "MYS", status: MetNotMetStatus.PartiallyMet },
                            { iso: "BTN", status: MetNotMetStatus.PartiallyMet },
                            { iso: "AGO", status: MetNotMetStatus.PartiallyMet },
                            { iso: "SDN", status: MetNotMetStatus.PartiallyMet },
                            { iso: "PAK", status: MetNotMetStatus.PartiallyMet },
                        ]
                    },
                    {
                        key: "Technical and Process",
                        value: "Technical and Process",
                        name: "Technical and Process",
                        tabId: "2",
                        tabName: "Technical and Process",
                        countries: [
                            { iso: "BFA", status: MetNotMetStatus.NotMet },
                            { iso: "MYS", status: MetNotMetStatus.NotMet },
                            { iso: "BTN", status: MetNotMetStatus.NotMet },
                            { iso: "GHA", status: MetNotMetStatus.NotMet },
                            { iso: "CMR", status: MetNotMetStatus.NotMet },
                            { iso: "COD", status: MetNotMetStatus.NotMet },
                            { iso: "MYS", status: MetNotMetStatus.PartiallyMet },
                            { iso: "BTN", status: MetNotMetStatus.PartiallyMet },
                            { iso: "AGO", status: MetNotMetStatus.PartiallyMet },
                            { iso: "SDN", status: MetNotMetStatus.PartiallyMet },
                            { iso: "PAK", status: MetNotMetStatus.PartiallyMet },
                            { iso: "KHM", status: MetNotMetStatus.PartiallyMet },
                            { iso: "BFA", status: MetNotMetStatus.NotAssessed },
                            { iso: "GHA", status: MetNotMetStatus.NotAssessed },
                            { iso: "COD", status: MetNotMetStatus.NotAssessed },
                            { iso: "BEN", status: MetNotMetStatus.NotAssessed },
                            { iso: "CMR", status: MetNotMetStatus.NotAssessed },
                        ]
                    },
                    {
                        key: "Behaviour",
                        value: "Behaviour",
                        name: "Behaviour",
                        tabId: "3",
                        tabName: "Behaviour",
                        countries: [
                            { iso: "BTN", status: MetNotMetStatus.NotAssessed },
                            { iso: "MYS", status: MetNotMetStatus.NotMet },
                            { iso: "BFA", status: MetNotMetStatus.NotMet },
                            { iso: "AGO", status: MetNotMetStatus.PartiallyMet },
                            { iso: "SDN", status: MetNotMetStatus.PartiallyMet },
                            { iso: "PAK", status: MetNotMetStatus.PartiallyMet },
                            { iso: "KHM", status: MetNotMetStatus.PartiallyMet },
                            { iso: "GHA", status: MetNotMetStatus.Met },
                            { iso: "COD", status: MetNotMetStatus.Met },
                            { iso: "BEN", status: MetNotMetStatus.Met },
                            { iso: "CMR", status: MetNotMetStatus.Met },
                            { iso: "BTN", status: MetNotMetStatus.NotMet },
                            { iso: "GHA", status: MetNotMetStatus.NotMet },
                            { iso: "CMR", status: MetNotMetStatus.NotMet },
                            { iso: "COD", status: MetNotMetStatus.NotMet },
                        ]
                    }
                    ]
                },
                {
                    "tabId": "2",
                    "tabName": "Elimination",
                    "objectives": [
                    {
                        key: "Performance",
                        value: "Performance",
                        name: "Performance",
                        tabId: "0",
                        tabName: "Context and Infrastructure",
                        countries: [
                            { iso: "BEN", status: MetNotMetStatus.Met },
                            { iso: "KHM", status: MetNotMetStatus.Met },
                            { iso: "AGO", status: MetNotMetStatus.Met },
                            { iso: "SDN", status: MetNotMetStatus.Met },
                            { iso: "PAK", status: MetNotMetStatus.Met },
                            { iso: "BFA", status: MetNotMetStatus.NotMet },
                            { iso: "MYS", status: MetNotMetStatus.NotMet },
                            { iso: "BTN", status: MetNotMetStatus.NotMet },
                            { iso: "GHA", status: MetNotMetStatus.NotMet },
                            { iso: "CMR", status: MetNotMetStatus.NotMet },
                            { iso: "COD", status: MetNotMetStatus.NotMet },
                        ]
                    },
                    {
                        key: "Context and Infrastructure",
                        value: "Context and Infrastructure",
                        name: "Context and Infrastructure",
                        tabId: "1",
                        tabName: "Performance",
                        countries: [
                            { iso: "BFA", status: MetNotMetStatus.NotMet },
                            { iso: "MYS", status: MetNotMetStatus.NotMet },
                            { iso: "BTN", status: MetNotMetStatus.NotMet },
                            { iso: "GHA", status: MetNotMetStatus.NotMet },
                            { iso: "CMR", status: MetNotMetStatus.NotMet },
                            { iso: "COD", status: MetNotMetStatus.NotMet },
                            { iso: "MYS", status: MetNotMetStatus.PartiallyMet },
                            { iso: "BTN", status: MetNotMetStatus.PartiallyMet },
                            { iso: "AGO", status: MetNotMetStatus.PartiallyMet },
                            { iso: "SDN", status: MetNotMetStatus.PartiallyMet },
                            { iso: "PAK", status: MetNotMetStatus.PartiallyMet },
                        ]
                    },
                    {
                        key: "Technical and Process",
                        value: "Technical and Process",
                        name: "Technical and Process",
                        tabId: "2",
                        tabName: "Technical and Process",
                        countries: [
                            { iso: "MYS", status: MetNotMetStatus.PartiallyMet },
                            { iso: "BTN", status: MetNotMetStatus.PartiallyMet },
                            { iso: "AGO", status: MetNotMetStatus.PartiallyMet },
                            { iso: "SDN", status: MetNotMetStatus.PartiallyMet },
                            { iso: "PAK", status: MetNotMetStatus.PartiallyMet },
                            { iso: "KHM", status: MetNotMetStatus.PartiallyMet },
                            { iso: "BFA", status: MetNotMetStatus.NotAssessed },
                            { iso: "GHA", status: MetNotMetStatus.NotAssessed },
                            { iso: "COD", status: MetNotMetStatus.NotAssessed },
                            { iso: "BEN", status: MetNotMetStatus.NotAssessed },
                            { iso: "CMR", status: MetNotMetStatus.NotAssessed },
                        ]
                    },
                    {
                        key: "Behaviour",
                        value: "Behaviour",
                        name: "Behaviour",
                        tabId: "3",
                        tabName: "Behaviour",
                        countries: [
                            { iso: "BTN", status: MetNotMetStatus.NotAssessed },
                            { iso: "MYS", status: MetNotMetStatus.NotMet },
                            { iso: "BFA", status: MetNotMetStatus.NotMet },
                            { iso: "AGO", status: MetNotMetStatus.PartiallyMet },
                            { iso: "SDN", status: MetNotMetStatus.PartiallyMet },
                            { iso: "PAK", status: MetNotMetStatus.PartiallyMet },
                            { iso: "KHM", status: MetNotMetStatus.PartiallyMet },
                            { iso: "GHA", status: MetNotMetStatus.Met },
                            { iso: "COD", status: MetNotMetStatus.Met },
                            { iso: "BEN", status: MetNotMetStatus.Met },
                            { iso: "CMR", status: MetNotMetStatus.Met },
                        ]
                    }
                    ]
                }
            ]
        }]),
};