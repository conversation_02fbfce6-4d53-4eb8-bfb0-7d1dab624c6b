﻿using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Localization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using WHO.MALARIA.Domain.Constants;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Web.Models;

namespace WHO.MALARIA.Web.Controllers
{
    public class ErrorController : Controller
    {
        private readonly IWebHostEnvironment _webHostEnvironment;
        private readonly AppSettings _appSettings;

        public ErrorController(IWebHostEnvironment webHostEnvironment, AppSettings appSettings)
        {
            _webHostEnvironment = webHostEnvironment;
            _appSettings = appSettings;
        }
        /// <summary>
        /// Renders the error page whenever an Ajax call is made from the client. 
        /// Typically this method is called whenever a HttpStatusCode = 500 is found at client side as a response
        /// </summary>
        /// <returns>An error cshtml page with react injection</returns> 
        [Route("Error/Info")]
        [HttpGet]
        public IActionResult RenderError()
        {
            ErrorDetails errorDetails = new ErrorDetails();

            if (TempData != null && TempData.Any() && TempData["ErrorDetails"] != null)
            {
                errorDetails = JsonConvert.DeserializeObject<ErrorDetails>(TempData["ErrorDetails"].ToString());
            }

            ErrorViewModel errorViewModel = new ErrorViewModel
            {

                // Email of support team
                SupportEmail = _appSettings?.SupportEmail,

                // Current environment name ex: Testing, Production etc
                EnvironmentName = _webHostEnvironment?.EnvironmentName,

                // Message is exception Title
                Message = errorDetails.Message,

                // StackStrace is details explanation of exception 
                StackStrace = errorDetails.StackStrace,

                // Request  Status Code
                StatusCode = errorDetails.StatusCode
            };

            // get localized values of labels
            ViewBag.ProblemOccuredWhileProcessingRequest = "Problem occured while processing the request.";
            ViewBag.Message = errorViewModel.Message;
            ViewBag.StatusCode = errorViewModel.StatusCode;
            ViewBag.StackStrace = errorViewModel.StackStrace;

            return View("ClientError", errorViewModel);
        }

        [Route("Error/UnAuthorized")]
        [HttpGet]
        public IActionResult UnAuthorized()
        {
            string currentUserEmail = HttpContext.User.Claims.FirstOrDefault(c => c.Type == Constants.IdentityClaims.Email)?.Value;      

            BaseViewModel baseViewModel = new BaseViewModel
            {
                // Email of support team
                SupportEmail = _appSettings?.SupportEmail,

                // Current environment name ex: Testing, Production etc
                EnvironmentName = _webHostEnvironment?.EnvironmentName,

                // Logged in user's id
                CurrentUserEmail = currentUserEmail
            };


            return View(baseViewModel);
        }
    }
}
