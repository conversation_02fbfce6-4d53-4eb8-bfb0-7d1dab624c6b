﻿
using Duende.IdentityServer;
using Duende.IdentityServer.Models;
using System.Collections.Generic;

namespace WHO.MALARIA.Web.Areas.Idp.Configuration
{
    /// <summary>
    /// Identity Server Configuration and setup
    /// </summary>
    public static class IdentityServerConfig
    {
        /// <summary>
        /// Gets the list of clients that can use the Identity server instance for authentication
        /// </summary>
        /// <param name="hostUrl">the URL of identity server</param>
        /// <returns>List of registered clients</returns>
        public static IEnumerable<Client> GetClients(string hostUrl)
        {
            return new List<Client>
            {
                new Client
                {
                    ClientId = "MALARIA",
                    ClientName = "MALARIA",
                    RedirectUris = { $"{hostUrl}/signin-oidc"},
                    FrontChannelLogoutUri = $"{hostUrl}/signout-oidc",
                    PostLogoutRedirectUris = { $"{hostUrl}/signout-callback" },
                    AllowedScopes =
                    {
                        IdentityServerConstants.StandardScopes.OpenId,
                        IdentityServerConstants.StandardScopes.Profile
                    },
                    AlwaysIncludeUserClaimsInIdToken = false,
                    RequireConsent = false,
                    AllowOfflineAccess = true,
                    RequireClientSecret = false,
                    AllowedGrantTypes = GrantTypes.Hybrid,
                    AlwaysSendClientClaims=true
                }
            };
        }

        public static IEnumerable<IdentityResource> GetIdentityResources()
        {
            return new List<IdentityResource>
            {
                new IdentityResources.OpenId(),
                new IdentityResources.Profile()
            };
        }

        public static IEnumerable<ApiResource> GetApis()
        {
            return new List<ApiResource>
            {
                new ApiResource("malaria", "External MALARIA")
            };
        }
    }
}
