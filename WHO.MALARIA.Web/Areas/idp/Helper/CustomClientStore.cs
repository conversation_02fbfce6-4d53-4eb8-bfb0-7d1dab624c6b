﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

using Duende.IdentityServer.Models;
using Duende.IdentityServer.Stores;

using MediatR;

using Microsoft.AspNetCore.Http;

using WHO.MALARIA.Domain.Dtos;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Domain.Queries;
using WHO.MALARIA.Features.Helpers;
using WHO.MALARIA.Web.Areas.Idp.Configuration;

namespace WHO.MALARIA.Web.Areas.Idp.Helper
{
    /// <summary>
    /// Class which acts as a repository for the clients registered in STS.
    /// </summary>
    public class CustomClientStore : IClientStore
    {
        private readonly IMediator _mediator;
        private readonly IHttpContextAccessor _httpContextAccessor;

        public CustomClientStore(IMediator mediator, IHttpContextAccessor httpContextAccessor)
        {
            _mediator = mediator;
            _httpContextAccessor = httpContextAccessor;
        }

        /// <summary>
        /// Get the clients registered for STS
        /// </summary>
        /// <param name="clientId">Client Id</param>
        /// <returns>List of all clients. MVC clients are returned by default.</returns>
        private async Task<List<Client>> GetIdentityClients(string clientId)
        {
            List<Client> stsClients = new List<Client>();

            List<Client> mvcClients = IdentityServerConfig.GetClients(_httpContextAccessor.HttpContext.GetApplicationURL()).ToList();

            stsClients.AddRange(mvcClients);

            // if client id is other than MVC client Id's , fetch the clients from Database. API clients are saved in Db.
            if (!mvcClients.Any(item => item.ClientId == clientId))
            {
                // The newly registered clients are saved in Identity table with user name equals to client Id and PasswordHash set to client secrete Id
                List<FilterCriteria> filterCriteria = new List<FilterCriteria>()
               {
                    new FilterCriteria
                    {
                        Field = "clientId",
                        Operator = "=",
                        Value = clientId
                    }
                };

                List<IdentityDto> identities = await _mediator.Send(new GetIdentityQuery(filterCriteria));

                IdentityDto identity = identities.Single();

                if (identity != null)
                {
                    Client apiClient = new Client
                    {
                        ClientId = identity.Username,
                        AllowedGrantTypes = GrantTypes.ClientCredentials,
                        // secret for authentication
                        ClientSecrets =
                        {
                            new Secret(identity.PasswordHash)
                        },
                        // scopes that client has access to
                        AllowedScopes = { "maleria" },

                    };

                    stsClients.Add(apiClient);
                }
            }

            return stsClients;
        }

        /// <summary>
        /// Check if the client is exists as a valid client id for STS.
        /// </summary>
        /// <param name="clientId">client id</param>
        /// <returns>Valid STS client</returns>
        public async Task<Client> FindClientByIdAsync(string clientId)
        {
            var result = await GetIdentityClients(clientId);

            return result.FirstOrDefault(tt => tt.ClientId.Equals(clientId, StringComparison.InvariantCultureIgnoreCase));
        }
    }
}
