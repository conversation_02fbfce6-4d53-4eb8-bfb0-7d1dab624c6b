using Autofac;
using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.HttpOverrides;
using Microsoft.AspNetCore.Mvc.ViewFeatures;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;

using SendGrid.Extensions.DependencyInjection;

using WHO.MALARIA.Domain.Constants;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Web.Extensions;
using WHO.MALARIA.Web.Middlewares;
using WHO.MALARIA.Web.PipelineBehaviours;

namespace WHO.MALARIA.Web
{
    public class Startup
    {
        public ILifetimeScope AutofacContainer { get; private set; }

        public Startup(IConfiguration configuration)
        {
            Configuration = configuration;
        }

        public IConfiguration Configuration { get; }

        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
            var appSettingConfiguration = new AppSettings();
            Configuration.Bind(Constants.Startup.AppSettings, appSettingConfiguration);

            //configure section of custom config json file
            services.Configure<DQAExcelSetting>(Configuration.GetSection(DQAExcelSetting.DQASetting));
            services.Configure<QuestionBankExcelSetting>(Configuration.GetSection(QuestionBankExcelSetting.QuestionBankSetting));
            services.Configure<AnalyticalOutputExcelSetting>(Configuration.GetSection(AnalyticalOutputExcelSetting.AnalyticalOutputSetting));
            services.Configure<ShellTableExcelSetting>(Configuration.GetSection(ShellTableExcelSetting.ShellTableSetting));
            services.AddMemoryCache();
            services.Configure<ForwardedHeadersOptions>(options =>
            {
                options.ForwardedHeaders = ForwardedHeaders.XForwardedFor | ForwardedHeaders.XForwardedProto |
                ForwardedHeaders.XForwardedHost;

                options.ForwardedHostHeaderName = "X-Original-Host";

                options.KnownNetworks.Clear();
                options.KnownProxies.Clear();
            });

            services.AddSingleton(appSettingConfiguration);
            services.AddControllers().AddNewtonsoftJson(options =>
            {
                // Configure date handling for consistent parsing
                options.SerializerSettings.DateFormatHandling = Newtonsoft.Json.DateFormatHandling.IsoDateFormat;
                options.SerializerSettings.DateTimeZoneHandling = Newtonsoft.Json.DateTimeZoneHandling.Utc;
                options.SerializerSettings.DateParseHandling = Newtonsoft.Json.DateParseHandling.DateTime;
            });
            //services.AddControllersWithViews();
            services.AddMvc(options => { options.EnableEndpointRouting = true; });
            //services.AddControllersWithViews();

            // adding swagger
            services.AddSwaggerDocumentation();

            // problem details 
            //services.AddProblemDetails(opts =>
            //{
            //    opts.IncludeExceptionDetails = (context, ex) =>
            //    {
            //        var environment = context.RequestServices.GetRequiredService<IHostEnvironment>();

            //        return environment.IsDevelopment() || environment.IsStaging();
            //    };

            //    // add all custom exceptions and business logic violation rule
            //    opts.Map<BusinessRuleValidationException>(ex => new BusinessRuleValidationExceptionProblemDetails(ex));
            //    opts.Map<InvalidCommandException>(ex => new InvalidCommandProblemDetails(ex));

            //});

            services.AddHttpContextAccessor();

            // adding database context
            services.AddSqlDbContext(Configuration);

            services.AddLogging();

            // adding log behaviour pipeline
            services.AddTransient(typeof(IPipelineBehavior<,>), typeof(LoggingBehaviour<,>));

            // fixes the issue with google chrome and safari for same site cookies issue
            services.ConfigureNonBreakingSameSiteCookies();

            // identity serve client set up
            services.Setup();
            services.ClientSetup(appSettingConfiguration);

            //Add DIs
            services.RegisterDependencies(Configuration);

            services.AddMvc(option => option.EnableEndpointRouting = false);

            // adding static file for spa in production
            services.AddSpaStaticFiles();

            services.AddSendGrid(options =>
            {
                options.ApiKey = System.Environment.GetEnvironmentVariable("SENDGRID_API_KEY") ?? appSettingConfiguration.SendGrid.ApiKey;
                options.HttpErrorAsException = true;
            });
        }

        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app, IWebHostEnvironment env, ITempDataProvider tempDataProvider, AppSettings appSettings)
        {
            app.UseSecurityHeaders();

            app.UseForwardedHeaders();

            app.UseIdentityServer();

            //Enable middleware to handle exception
            app.ConfigureExceptionHandler(env.IsProduction(), tempDataProvider, Configuration, appSettings);

            if (env.IsProduction())
            {
                app.UseSpaStaticFiles();

                // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
                app.UseHsts();
            }

            //You may would disable auto migrate if needed
            // app.UseAutoMigrateDatabase<MALARIADbContext>();

            app.UseHttpsRedirection();
            app.UseAntiXssMiddleware();
            app.UseVirusScan();
            app.UseStaticFiles();
            app.UseRouting();
            app.UseAuthentication();
            app.UseAuthorization();

            app.UseSwaggerDocumentation();

            // mvc and area routing
            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllerRoute(name: "default", pattern: "{controller}/{action}/{id?}");
                //endpoints.MapControllerRoute(name: "mvc", pattern: "{controller=Home}/{action=Index}/{id?}");
                endpoints.MapAreaControllerRoute(name: "area", areaName: "idp", pattern: "{area:exists}/{controller}/{action}/{id?}");

                //endpoints.MapRazorPages();
            });

            // In development mode use 'npm start' command
            app.UseSpa(env);

        }
    }
}
