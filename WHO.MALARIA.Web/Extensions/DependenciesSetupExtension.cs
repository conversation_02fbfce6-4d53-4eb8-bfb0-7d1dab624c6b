﻿using MediatR;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Serilog;
using System;
using WHO.MALARIA.Common.Services;
using WHO.MALARIA.Database;
using WHO.MALARIA.Database.IRepositories;
using WHO.MALARIA.Database.Repositories;
using WHO.MALARIA.DocumentManager;
using WHO.MALARIA.Domain.Constants;
using WHO.MALARIA.Domain.Dtos;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Features;
using WHO.MALARIA.Services;
using WHO.MALARIA.Services.BusinessRuleValidations.Implementations;
using WHO.MALARIA.Services.BusinessRuleValidations.Interaces;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;
using WHO.MALARIA.Services.Dxos;
using WHO.MALARIA.Services.Handlers.Queries;
using WHO.MALARIA.Services.Infrastructure;
using WHO.MALARIA.Services.Interfaces;
using WHO.MALARIA.Services.Services;
using WHO.MALARIA.Web.Areas.Idp.Helper;
using WHO.MALARIA.Web.Infrastructure.Events;


namespace WHO.MALARIA.Web.Extensions
{
    internal static class DependenciesSetupExtension
    {
       internal static IServiceCollection RegisterDependencies(this IServiceCollection services, IConfiguration configuration)
        {
            // register MediaR assemblies
            services.AddMediatR(cfg => cfg.RegisterServicesFromAssembly(AppDomain.CurrentDomain.Load(Constants.Startup.MediatRAssemblyName)));

            services.AddSingleton(Log.Logger);
            services.Configure<AppSettings>(configuration.GetSection(Constants.Startup.AppSettingsSection));

            services.AddScoped<ISqlConnectionFactory, SqlConnectionFactory>(service => {
                return new SqlConnectionFactory(configuration.GetConnectionString(Constants.Startup.ConnectionString));
            });

            // common dependencies
            services.AddScoped(typeof(IPasswordHasher<>), typeof(PasswordHasher<>));
            services.AddScoped<IUnitOfWork, UnitOfWork>();

            // repositories
            services.AddScoped<IIdentityRepository, IdentityRepository>();
            services.AddScoped<IUserRepository, UserRepository>();
            services.AddScoped<IUserCountryAccessRepository, UserCountryAccessRepository>();
            services.AddScoped<ICountryRepository, CountryRepository>();
            services.AddScoped<IServiceLevelRepository, ServiceLevelRepository>();
            services.AddScoped<ISLVariableDataResponseRepository, SLVariableDataResponseRepository>();
            services.AddScoped<ISLVariableCompletenessResponseRepository, SLVariableCompletenessResponseRepository>();
            services.AddScoped<IDQARepository, DQARepository>();
            services.AddScoped<IRegionRepository, RegionRepository>();
            services.AddScoped<IIndicatorRepository, IndicatorRepository>();

            // queries
            services.AddScoped<IAssessmentQueries, AssessmentQueries>();
            services.AddScoped<IUserQueries, UserQueries>();
            services.AddScoped<IDQAQueries, DQAQueries>();
            services.AddScoped<IDQADocumentManager, DQADocumentManager>();
            services.AddScoped<IDeskReviewQueries, DeskReviewQueries>();
            services.AddScoped<IAssessmentQuestionQueries, AssessmentQuestionQueries>();
            services.AddScoped<IQuestionBank, QuestionBank>();
            services.AddScoped<IAnalyticalOutputQueries, AnalyticalOutputQueries>();
            services.AddScoped<IAnalyticsQueries, AnalyticsQueries>();
            services.AddScoped<IAnalyticalOutput, AnalyticalOutput>();
            services.AddTransient<IDQAReportGeneration, DQAReportGeneration>();
            services.AddScoped<IShellTable, ShellTable>();
            services.AddScoped<IShellTableQueries, ShellTableQueries>();
            services.AddScoped<IScoreCardQueries, ScoreCardQueries>();
            services.AddScoped<IDataAnalysisReportQueries, DataAnalysisReportQueries>();
            services.AddScoped<IScoreCard, DocumentManager.ScoreCard>();
            services.AddScoped<IDashboardQueries, DashboardQueries>();

            // domain exchange objects
            services.AddScoped<IUserDxos, UserDxos>();
            services.AddScoped<IIdentityDxos, IdentityDxos>();
            services.AddScoped<IUserCountryAccessDxos, UserCountryAccessDxos>();
            services.AddScoped(typeof(IRetrieveMultipleRecordsDxos<,>), typeof(RetrieveMultipleRecordsDxos<,>));

            // business rule validation
            services.AddScoped<IAssessmentRuleChecker, AssessmentRuleChecker>();
            services.AddScoped<ICustomerRuleChecker, CustomerRuleChecker>();
            services.AddScoped<IIdentityRuleChecker, IdentityRuleChecker>();
            services.AddScoped<IUserRuleChecker, UserRuleChecker>();
            services.AddScoped<ICommonRuleChecker, CommonRuleChecker>();
            services.AddScoped<IUserCountryAccessRuleChecker, UserCountryAccessRuleChecker>();
            services.AddScoped<IDQARuleChecker, DQARuleChecker>();

            // identity server
            services.AddTransient<IUserStore<IdentityDto>, UserStore>();
            services.AddTransient<IRoleStore<UserRoleDto>, RoleStore>();
            services.AddIdentity<IdentityDto, UserRoleDto>().AddDefaultTokenProviders();
            services.AddTransient<CustomCookieAuthenticationEvents>();
            services.AddTransient<ITokenRefresherService, TokenRefresherService>();

            // graph service
            services.AddScoped<IGraphService, GraphService>();

            //translation service
            services.AddScoped<ITranslationService,TranslationService>();

            //Caching service
            services.AddScoped<ICacheDataService, CacheDataService>();

            services.AddTransient<IEmailService, EmailService>();

            services.AddTransient<IDbManager, DbManager>();

            return services;
        }
    }
}
